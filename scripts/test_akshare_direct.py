"""
直接测试AkShare功能
不使用适配器，直接调用akshare API
"""
import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, date

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from qlib_trading_system.utils.logging.logger import system_logger

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False


def test_akshare_basic():
    """测试AkShare基本功能"""
    
    if not AKSHARE_AVAILABLE:
        system_logger.error("AkShare未安装")
        return False
    
    system_logger.info("=" * 50)
    system_logger.info("直接测试AkShare功能")
    system_logger.info("=" * 50)
    
    try:
        # 测试1: 获取股票实时数据
        system_logger.info("测试1: 获取股票实时数据...")
        try:
            df_realtime = ak.stock_zh_a_spot_em()
            if not df_realtime.empty:
                system_logger.info(f"✅ 获取到 {len(df_realtime)} 只股票的实时数据")
                system_logger.info("实时数据样本:")
                system_logger.info(f"\n{df_realtime.head(3).to_string()}")
            else:
                system_logger.warning("实时数据为空")
        except Exception as e:
            system_logger.error(f"获取实时数据失败: {e}")
        
        # 测试2: 获取单只股票历史数据
        system_logger.info("\n测试2: 获取单只股票历史数据...")
        test_symbol = "000001"  # 平安银行
        start_date = "20240701"
        end_date = "20240731"
        
        try:
            df_hist = ak.stock_zh_a_hist(
                symbol=test_symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=""
            )
            
            if not df_hist.empty:
                system_logger.info(f"✅ 获取到 {test_symbol} 的 {len(df_hist)} 条历史数据")
                system_logger.info("历史数据样本:")
                system_logger.info(f"\n{df_hist.head(5).to_string()}")
                
                # 保存测试数据
                data_dir = project_root / "data" / "raw"
                data_dir.mkdir(parents=True, exist_ok=True)
                
                csv_path = data_dir / f"akshare_direct_test_{test_symbol}.csv"
                df_hist.to_csv(csv_path, index=False, encoding='utf-8')
                system_logger.info(f"✅ 数据已保存到: {csv_path}")
                
            else:
                system_logger.warning("历史数据为空")
                
        except Exception as e:
            system_logger.error(f"获取历史数据失败: {e}")
        
        # 测试3: 获取多只股票数据
        system_logger.info("\n测试3: 获取多只股票数据...")
        test_symbols = ["000001", "600000", "000858"]
        all_data = []
        
        for symbol in test_symbols:
            try:
                system_logger.info(f"下载 {symbol} 数据...")
                df = ak.stock_zh_a_hist(
                    symbol=symbol,
                    period="daily", 
                    start_date=start_date,
                    end_date=end_date,
                    adjust=""
                )
                
                if not df.empty:
                    # 添加股票代码列
                    df['股票代码'] = symbol
                    all_data.append(df)
                    system_logger.info(f"✅ {symbol}: {len(df)} 条记录")
                else:
                    system_logger.warning(f"⚠️  {symbol}: 数据为空")
                    
            except Exception as e:
                system_logger.error(f"❌ {symbol}: {e}")
                continue
        
        # 合并数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 重命名列
            combined_df = combined_df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low', 
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'amount',
                '股票代码': 'symbol'
            })
            
            # 保存合并数据
            csv_path = data_dir / "akshare_direct_test_multiple.csv"
            combined_df.to_csv(csv_path, index=False, encoding='utf-8')
            
            system_logger.info(f"✅ 合并数据已保存到: {csv_path}")
            system_logger.info(f"总计: {len(combined_df)} 条记录")
            
            # 统计信息
            stock_counts = combined_df.groupby('symbol').size()
            system_logger.info("\n各股票记录数:")
            for symbol, count in stock_counts.items():
                system_logger.info(f"  {symbol}: {count} 条")
            
            return True
        else:
            system_logger.error("没有成功获取任何数据")
            return False
            
    except Exception as e:
        system_logger.error(f"测试过程发生错误: {e}")
        return False


def main():
    """主函数"""
    success = test_akshare_basic()
    
    if success:
        system_logger.info("\n🎉 AkShare直接测试成功！")
        system_logger.info("AkShare功能正常，可以用于数据下载")
        system_logger.info("\n检查生成的文件:")
        system_logger.info("- data/raw/akshare_direct_test_000001.csv")
        system_logger.info("- data/raw/akshare_direct_test_multiple.csv")
    else:
        system_logger.error("❌ AkShare直接测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
