"""
简单测试AkShare下载功能
只下载几只代表性股票进行测试
"""
import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime, date

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from qlib_trading_system.utils.logging.logger import system_logger
from qlib_trading_system.data.collectors.akshare_adapter import AkShareAdapter
from qlib_trading_system.data.collectors.config_factory import DataSourceConfigFactory


# 测试股票列表（只选择几只）
TEST_STOCKS = [
    "000001.SZ",  # 平安银行
    "600000.SH",  # 浦发银行
    "000858.SZ",  # 五粮液
    "600519.SH",  # 贵州茅台
    "002415.SZ",  # 海康威视
]


async def test_akshare_download():
    """测试AkShare下载功能"""
    
    system_logger.info("=" * 50)
    system_logger.info("测试AkShare数据下载功能")
    system_logger.info("=" * 50)
    system_logger.info(f"测试股票: {TEST_STOCKS}")
    
    try:
        # 创建AkShare配置
        config = DataSourceConfigFactory.create_akshare_config()
        adapter = AkShareAdapter(config)
        
        # 连接AkShare
        system_logger.info("连接AkShare...")
        if not await adapter.connect():
            system_logger.error("AkShare连接失败")
            return False
        
        system_logger.info("✅ AkShare连接成功")
        
        # 测试获取股票列表
        system_logger.info("测试获取股票列表...")
        try:
            stocks = await adapter.get_stock_list()
            system_logger.info(f"✅ 获取到 {len(stocks)} 只股票")
        except Exception as e:
            system_logger.warning(f"获取股票列表失败: {e}")
        
        # 测试下载历史数据
        system_logger.info("测试下载历史数据...")
        start_date = "2024-07-01"
        end_date = "2024-07-31"
        
        system_logger.info(f"下载时间范围: {start_date} 到 {end_date}")
        
        df = await adapter.get_daily_price(TEST_STOCKS, start_date, end_date)
        
        if not df.empty:
            # 重置索引
            df_reset = df.reset_index()
            df_reset = df_reset.rename(columns={
                'symbol': 'instrument',
                'date': 'datetime'
            })
            
            # 保存数据
            data_dir = project_root / "data" / "raw"
            data_dir.mkdir(parents=True, exist_ok=True)
            
            csv_path = data_dir / "akshare_test_data.csv"
            df_reset.to_csv(csv_path, index=False, encoding='utf-8')
            
            system_logger.info(f"✅ 测试数据已保存到: {csv_path}")
            system_logger.info(f"数据统计: {len(df_reset)} 条记录")
            
            # 显示数据样本
            system_logger.info("数据样本:")
            system_logger.info(f"\n{df_reset.head(10).to_string()}")
            
            # 按股票统计
            stock_counts = df_reset.groupby('instrument').size()
            system_logger.info("\n各股票记录数:")
            for stock, count in stock_counts.items():
                system_logger.info(f"  {stock}: {count} 条")
            
            return True
        else:
            system_logger.error("没有获取到数据")
            return False
            
    except Exception as e:
        system_logger.error(f"测试失败: {str(e)}")
        return False
    finally:
        try:
            await adapter.disconnect()
        except:
            pass


def main():
    """主函数"""
    success = asyncio.run(test_akshare_download())
    
    if success:
        system_logger.info("🎉 AkShare测试成功！")
        system_logger.info("\n下一步可以:")
        system_logger.info("1. 运行完整下载: python scripts/download_sample_data.py")
        system_logger.info("2. 检查数据文件: data/raw/akshare_test_data.csv")
    else:
        system_logger.error("❌ AkShare测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
