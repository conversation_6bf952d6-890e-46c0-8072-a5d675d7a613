"""
iTick有限权限适配器
基于实际测试结果，适应当前Token的权限限制
"""
import asyncio
import aiohttp
import pandas as pd
from typing import List, Union, Optional
from datetime import datetime, date, timedelta
import json

from .base import (
    BaseDataSource, DataSourceConfig, DataSourceType, DataType,
    StockBasicInfo, PriceData, TickData, Level2Data,
    DataSourceException, DataSourceConnectionError, DataSourceAuthError
)


class LimitediTickAdapter(BaseDataSource):
    """有限权限的iTick数据源适配器"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.itick.io"
        self.token = config.api_key
        self.session = None
        
        # 基于测试结果，只支持历史数据
        self.features = [DataType.HISTORICAL]
        
        # 预定义的股票列表（由于API限制）
        self.predefined_stocks = [
            StockBasicInfo(symbol="000001", name="平安银行", industry="银行", market="SZ"),
            StockBasicInfo(symbol="000002", name="万科A", industry="房地产", market="SZ"),
            StockBasicInfo(symbol="000858", name="五粮液", industry="食品饮料", market="SZ"),
            StockBasicInfo(symbol="002415", name="海康威视", industry="电子", market="SZ"),
            StockBasicInfo(symbol="002594", name="比亚迪", industry="汽车", market="SZ"),
            StockBasicInfo(symbol="600000", name="浦发银行", industry="银行", market="SH"),
            StockBasicInfo(symbol="600036", name="招商银行", industry="银行", market="SH"),
            StockBasicInfo(symbol="600519", name="贵州茅台", industry="食品饮料", market="SH"),
            StockBasicInfo(symbol="600887", name="伊利股份", industry="食品饮料", market="SH"),
            StockBasicInfo(symbol="601318", name="中国平安", industry="非银金融", market="SH"),
        ]
    
    async def connect(self) -> bool:
        """连接iTick API"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout),
                connector=aiohttp.TCPConnector(ssl=False)
            )
            
            # 测试连接
            if await self.test_connection():
                self.is_connected = True
                return True
            else:
                await self.disconnect()
                return False
                
        except Exception as e:
            self.last_error = e
            raise DataSourceConnectionError(f"iTick连接失败: {str(e)}")
    
    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.is_connected = False
            return True
        except Exception as e:
            self.last_error = e
            return False
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            url = f"{self.base_url}/stock/kline"
            headers = self._get_auth_headers()
            params = {
                'region': 'cn',
                'code': '000001.SZ',
                'kType': '101',
                'limit': 1
            }
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    # code: 0 表示成功，即使数据为空也说明连接正常
                    return data.get('code') == 0
                elif response.status == 401:
                    raise DataSourceAuthError("iTick认证失败，请检查Token")
                else:
                    return False
                    
        except Exception as e:
            self.last_error = e
            return False
    
    def _get_auth_headers(self) -> dict:
        """生成认证头"""
        return {
            'Token': self.token,
            'Content-Type': 'application/json'
        }
    
    async def get_stock_list(self) -> List[StockBasicInfo]:
        """获取股票列表（使用预定义列表）"""
        # 由于API权限限制，返回预定义的股票列表
        return self.predefined_stocks.copy()
    
    async def get_stock_basic_info(self, symbols: List[str]) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        # 从预定义列表中筛选
        result = []
        for symbol in symbols:
            for stock in self.predefined_stocks:
                if stock.symbol == symbol or stock.symbol == symbol.split('.')[0]:
                    result.append(stock)
                    break
        return result
    
    async def get_daily_price(self, 
                            symbols: List[str], 
                            start_date: Union[str, date], 
                            end_date: Union[str, date]) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            df_list = []
            
            # 逐个股票请求数据
            for symbol in symbols:
                try:
                    # 确保股票代码格式正确
                    formatted_symbol = self._format_symbol(symbol)
                    
                    # 尝试多个时间范围，找到有数据的范围
                    data_found = False
                    for time_range in self._get_time_ranges(start_date, end_date):
                        try:
                            params = {
                                'region': 'cn',
                                'code': formatted_symbol,
                                'kType': '101',  # 日K线
                                'start_time': time_range['start'],
                                'end_time': time_range['end'],
                                'limit': 1000
                            }
                            
                            url = f"{self.base_url}/stock/kline"
                            headers = self._get_auth_headers()
                            
                            async with self.session.get(url, headers=headers, params=params) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    
                                    if data.get('code') == 0 and data.get('data'):
                                        klines = data['data']
                                        
                                        for kline in klines:
                                            # 处理不同的数据格式
                                            record = self._parse_kline_data(kline, symbol)
                                            if record:
                                                df_list.append(record)
                                        
                                        data_found = True
                                        print(f"✅ {symbol} 获取到 {len(klines)} 条数据")
                                        break  # 找到数据就停止尝试其他时间范围
                                    
                        except Exception as e:
                            print(f"⚠️  {symbol} 时间范围 {time_range} 失败: {str(e)}")
                            continue
                        
                        # 添加延迟避免限流
                        await asyncio.sleep(0.5)
                    
                    if not data_found:
                        print(f"⚠️  {symbol} 所有时间范围都无数据")
                        
                except Exception as e:
                    print(f"❌ {symbol} 处理失败: {str(e)}")
                    continue
            
            # 转换为DataFrame
            if df_list:
                df = pd.DataFrame(df_list)
                df['date'] = pd.to_datetime(df['date'])
                df.set_index(['symbol', 'date'], inplace=True)
                return df
            else:
                return pd.DataFrame()
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取日线数据失败: {str(e)}")
    
    def _format_symbol(self, symbol: str) -> str:
        """格式化股票代码"""
        # 移除可能的后缀
        clean_symbol = symbol.split('.')[0]
        
        # 添加正确的后缀
        if clean_symbol.startswith('6'):
            return f"{clean_symbol}.SH"
        else:
            return f"{clean_symbol}.SZ"
    
    def _get_time_ranges(self, start_date: str, end_date: str) -> List[dict]:
        """获取多个时间范围进行尝试"""
        ranges = []
        
        # 原始时间范围
        ranges.append({'start': start_date, 'end': end_date})
        
        # 如果原始范围太大，尝试较小的范围
        start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        if (end_dt - start_dt).days > 365:
            # 尝试最近一年
            recent_start = end_dt - timedelta(days=365)
            ranges.append({
                'start': recent_start.strftime('%Y-%m-%d'),
                'end': end_date
            })
            
            # 尝试最近半年
            recent_start = end_dt - timedelta(days=180)
            ranges.append({
                'start': recent_start.strftime('%Y-%m-%d'),
                'end': end_date
            })
        
        # 尝试一些固定的时间范围
        fixed_ranges = [
            {'start': '2024-01-01', 'end': '2024-12-31'},
            {'start': '2023-01-01', 'end': '2023-12-31'},
            {'start': '2022-01-01', 'end': '2022-12-31'},
        ]
        
        for fixed_range in fixed_ranges:
            if fixed_range not in ranges:
                ranges.append(fixed_range)
        
        return ranges
    
    def _parse_kline_data(self, kline, symbol: str) -> Optional[dict]:
        """解析K线数据"""
        try:
            if isinstance(kline, dict):
                # 字典格式
                return {
                    'symbol': symbol.split('.')[0],
                    'date': kline.get('time', kline.get('date')),
                    'open': float(kline.get('open', 0)),
                    'high': float(kline.get('high', 0)),
                    'low': float(kline.get('low', 0)),
                    'close': float(kline.get('close', 0)),
                    'volume': int(kline.get('volume', 0)),
                    'amount': float(kline.get('amount', 0)),
                    'pre_close': float(kline.get('pre_close', 0)),
                    'change': float(kline.get('change', 0)),
                    'pct_change': float(kline.get('pct_change', 0))
                }
            elif isinstance(kline, list) and len(kline) >= 6:
                # 数组格式
                return {
                    'symbol': symbol.split('.')[0],
                    'date': kline[0],
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': int(kline[5]),
                    'amount': float(kline[6]) if len(kline) > 6 else 0,
                    'pre_close': float(kline[7]) if len(kline) > 7 else 0,
                    'change': float(kline[8]) if len(kline) > 8 else 0,
                    'pct_change': float(kline[9]) if len(kline) > 9 else 0
                }
            else:
                return None
        except Exception as e:
            print(f"解析K线数据失败: {e}, 数据: {kline}")
            return None
    
    async def get_minute_price(self, 
                             symbol: str, 
                             date: Union[str, date],
                             frequency: str = "1min") -> pd.DataFrame:
        """获取分钟线数据（当前权限可能不支持）"""
        raise NotImplementedError("当前iTick权限不支持分钟线数据")
    
    async def get_realtime_price(self, symbols: List[str]) -> List[PriceData]:
        """获取实时价格数据（当前权限不支持）"""
        raise NotImplementedError("当前iTick权限不支持实时价格数据")
    
    async def get_tick_data(self, 
                          symbol: str, 
                          date: Union[str, date]) -> List[TickData]:
        """获取Tick数据（当前权限不支持）"""
        raise NotImplementedError("当前iTick权限不支持Tick数据")
    
    async def get_level2_data(self, 
                            symbol: str, 
                            date: Union[str, date]) -> List[Level2Data]:
        """获取Level-2数据（当前权限不支持）"""
        raise NotImplementedError("当前iTick权限不支持Level-2数据")
    
    def get_supported_features(self) -> List[str]:
        """获取支持的功能列表"""
        return [
            "历史K线数据（日线）",
            "预定义股票列表",
            "基础股票信息"
        ]
    
    def get_limitations(self) -> List[str]:
        """获取当前限制"""
        return [
            "无法获取完整股票列表（使用预定义列表）",
            "无法获取实时行情数据",
            "无法获取分钟线数据",
            "无法获取Tick数据",
            "无法获取Level-2数据",
            "部分股票可能无历史数据"
        ]