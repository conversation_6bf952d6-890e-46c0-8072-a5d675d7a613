{"version": 3, "file": "static/js/9800.54db3b3e.chunk.js", "mappings": "4HAAA,MAaYA,IA4DPC,OAzDD,WAGI,IAAKD,EAAOE,sBAAZ,CAMA,GAAKF,EAAOG,4BAIR,OAFAH,EAAOE,sBAAwBF,EAAqC,iCACpEA,EAAOI,qBAAuBJ,EAAoC,4BAAMA,EAA2C,mCAOvH,IAAIK,EAAW,EAEfL,EAAOE,sBAAwB,SAAUI,GAErC,IAAIC,GAAW,IAAIC,MAAOC,UAEtBC,EAAaC,KAAKC,IAAK,EAAG,IAAOL,EAAWF,IAE5CQ,EAAKb,EAAOc,YAAY,WAExBR,EAAUC,EAAWG,EAEzB,GAAGA,GAIH,OAFAL,EAAWE,EAAWG,EAEfG,CAEX,EAEAb,EAAOI,qBAAuB,SAAUS,GAEpCE,aAAcF,EAElB,CApCA,CAsCH,CA7CD,QAqDM,KAJFG,EAAAA,WAEI,OAAOhB,EAAOE,qBAEhB,+B,+ECrEV,SAASe,IAAQ,CAEF,WAASC,GACtB,OAAmB,MAAZA,EAAmBD,EAAO,WAC/B,OAAOE,KAAKC,cAAcF,EAC5B,CACF,CCNA,SAASG,IACP,MAAO,EACT,CAEe,WAASH,GACtB,OAAmB,MAAZA,EAAmBG,EAAQ,WAChC,OAAOF,KAAKG,iBAAiBJ,EAC/B,CACF,CCRe,WAASA,GACtB,OAAO,WACL,OAAOC,KAAKI,QAAQL,EACtB,CACF,CCJe,WAASM,GACtB,OAAO,IAAIC,MAAMD,EAAOE,OAC1B,CCKO,SAASC,EAAUC,EAAQC,GAChCV,KAAKW,cAAgBF,EAAOE,cAC5BX,KAAKY,aAAeH,EAAOG,aAC3BZ,KAAKa,MAAQ,KACbb,KAAKc,QAAUL,EACfT,KAAKe,SAAWL,CAClB,CAEAF,EAAUQ,UAAY,CACpBC,YAAaT,EACbU,YAAa,SAASC,GAAS,OAAOnB,KAAKc,QAAQM,aAAaD,EAAOnB,KAAKa,MAAQ,EACpFO,aAAc,SAASD,EAAOE,GAAQ,OAAOrB,KAAKc,QAAQM,aAAaD,EAAOE,EAAO,EACrFpB,cAAe,SAASF,GAAY,OAAOC,KAAKc,QAAQb,cAAcF,EAAW,EACjFI,iBAAkB,SAASJ,GAAY,OAAOC,KAAKc,QAAQX,iBAAiBJ,EAAW,GCdzF,SAASuB,EAAUb,EAAQc,EAAOC,EAAOnB,EAAQoB,EAAMC,GASrD,IARA,IACIC,EADAC,EAAI,EAEJC,EAAcN,EAAMhB,OACpBuB,EAAaJ,EAAKnB,OAKfqB,EAAIE,IAAcF,GACnBD,EAAOJ,EAAMK,KACfD,EAAKZ,SAAWW,EAAKE,GACrBvB,EAAOuB,GAAKD,GAEZH,EAAMI,GAAK,IAAIpB,EAAUC,EAAQiB,EAAKE,IAK1C,KAAOA,EAAIC,IAAeD,GACpBD,EAAOJ,EAAMK,MACfH,EAAKG,GAAKD,EAGhB,CAEA,SAASI,EAAQtB,EAAQc,EAAOC,EAAOnB,EAAQoB,EAAMC,EAAMM,GACzD,IAAIJ,EACAD,EAKAM,EAJAC,EAAiB,CAAC,EAClBL,EAAcN,EAAMhB,OACpBuB,EAAaJ,EAAKnB,OAClB4B,EAAY,IAAI7B,MAAMuB,GAK1B,IAAKD,EAAI,EAAGA,EAAIC,IAAeD,GACzBD,EAAOJ,EAAMK,MACfO,EAAUP,GAAKK,EAzCL,IAyC4BD,EAAII,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,GACnEU,KAAYC,EACdT,EAAKG,GAAKD,EAEVO,EAAeD,GAAYN,GAQjC,IAAKC,EAAI,EAAGA,EAAIE,IAAcF,GAExBD,EAAOO,EADXD,EAtDY,IAsDWD,EAAII,KAAK3B,EAAQiB,EAAKE,GAAIA,EAAGF,MAElDrB,EAAOuB,GAAKD,EACZA,EAAKZ,SAAWW,EAAKE,GACrBM,EAAeD,GAAY,MAE3BT,EAAMI,GAAK,IAAIpB,EAAUC,EAAQiB,EAAKE,IAK1C,IAAKA,EAAI,EAAGA,EAAIC,IAAeD,GACxBD,EAAOJ,EAAMK,KAAQM,EAAeC,EAAUP,MAAQD,IACzDF,EAAKG,GAAKD,EAGhB,CCrDA,SAASU,EAAUC,EAAGC,GACpB,OAAOD,EAAIC,GAAK,EAAID,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAIC,GAC/C,CCvBO,IAAIC,EAAQ,+BAEnB,GACEC,IAAK,6BACLD,MAAOA,EACPE,MAAO,+BACPC,IAAK,uCACLC,MAAO,iCCLM,WAASC,GACtB,IAAIC,EAASD,GAAQ,GAAIlB,EAAImB,EAAOC,QAAQ,KAE5C,OADIpB,GAAK,GAAqC,WAA/BmB,EAASD,EAAKG,MAAM,EAAGrB,MAAiBkB,EAAOA,EAAKG,MAAMrB,EAAI,IACtEsB,EAAWC,eAAeJ,GAAU,CAACK,MAAOF,EAAWH,GAASM,MAAOP,GAAQA,CACxF,CCJA,SAASQ,EAAWR,GAClB,OAAO,WACL9C,KAAKuD,gBAAgBT,EACvB,CACF,CAEA,SAASU,EAAaC,GACpB,OAAO,WACLzD,KAAK0D,kBAAkBD,EAASL,MAAOK,EAASJ,MAClD,CACF,CAEA,SAASM,EAAab,EAAMc,GAC1B,OAAO,WACL5D,KAAK6D,aAAaf,EAAMc,EAC1B,CACF,CAEA,SAASE,EAAeL,EAAUG,GAChC,OAAO,WACL5D,KAAK+D,eAAeN,EAASL,MAAOK,EAASJ,MAAOO,EACtD,CACF,CAEA,SAASI,EAAalB,EAAMc,GAC1B,OAAO,WACL,IAAIK,EAAIL,EAAMM,MAAMlE,KAAMmE,WACjB,MAALF,EAAWjE,KAAKuD,gBAAgBT,GAC/B9C,KAAK6D,aAAaf,EAAMmB,EAC/B,CACF,CAEA,SAASG,EAAeX,EAAUG,GAChC,OAAO,WACL,IAAIK,EAAIL,EAAMM,MAAMlE,KAAMmE,WACjB,MAALF,EAAWjE,KAAK0D,kBAAkBD,EAASL,MAAOK,EAASJ,OAC1DrD,KAAK+D,eAAeN,EAASL,MAAOK,EAASJ,MAAOY,EAC3D,CACF,CCxCe,WAAStC,GACtB,OAAQA,EAAKhB,eAAiBgB,EAAKhB,cAAc0D,aACzC1C,EAAK2C,UAAY3C,GAClBA,EAAK0C,WACd,CCFA,SAASE,EAAYzB,GACnB,OAAO,WACL9C,KAAKwE,MAAMC,eAAe3B,EAC5B,CACF,CAEA,SAAS4B,EAAc5B,EAAMc,EAAOe,GAClC,OAAO,WACL3E,KAAKwE,MAAMI,YAAY9B,EAAMc,EAAOe,EACtC,CACF,CAEA,SAASE,EAAc/B,EAAMc,EAAOe,GAClC,OAAO,WACL,IAAIV,EAAIL,EAAMM,MAAMlE,KAAMmE,WACjB,MAALF,EAAWjE,KAAKwE,MAAMC,eAAe3B,GACpC9C,KAAKwE,MAAMI,YAAY9B,EAAMmB,EAAGU,EACvC,CACF,CAWO,SAASG,EAAWnD,EAAMmB,GAC/B,OAAOnB,EAAK6C,MAAMO,iBAAiBjC,IAC5BuB,EAAY1C,GAAMqD,iBAAiBrD,EAAM,MAAMoD,iBAAiBjC,EACzE,CClCA,SAASmC,EAAenC,GACtB,OAAO,kBACE9C,KAAK8C,EACd,CACF,CAEA,SAASoC,EAAiBpC,EAAMc,GAC9B,OAAO,WACL5D,KAAK8C,GAAQc,CACf,CACF,CAEA,SAASuB,EAAiBrC,EAAMc,GAC9B,OAAO,WACL,IAAIK,EAAIL,EAAMM,MAAMlE,KAAMmE,WACjB,MAALF,SAAkBjE,KAAK8C,GACtB9C,KAAK8C,GAAQmB,CACpB,CACF,CClBA,SAASmB,EAAWC,GAClB,OAAOA,EAAOC,OAAOC,MAAM,QAC7B,CAEA,SAASC,EAAU7D,GACjB,OAAOA,EAAK6D,WAAa,IAAIC,EAAU9D,EACzC,CAEA,SAAS8D,EAAU9D,GACjB3B,KAAK0F,MAAQ/D,EACb3B,KAAK2F,OAASP,EAAWzD,EAAKiE,aAAa,UAAY,GACzD,CAsBA,SAASC,EAAWlE,EAAMmE,GAExB,IADA,IAAIC,EAAOP,EAAU7D,GAAOC,GAAK,EAAGoE,EAAIF,EAAMvF,SACrCqB,EAAIoE,GAAGD,EAAKE,IAAIH,EAAMlE,GACjC,CAEA,SAASsE,EAAcvE,EAAMmE,GAE3B,IADA,IAAIC,EAAOP,EAAU7D,GAAOC,GAAK,EAAGoE,EAAIF,EAAMvF,SACrCqB,EAAIoE,GAAGD,EAAKI,OAAOL,EAAMlE,GACpC,CAEA,SAASwE,EAAYN,GACnB,OAAO,WACLD,EAAW7F,KAAM8F,EACnB,CACF,CAEA,SAASO,EAAaP,GACpB,OAAO,WACLI,EAAclG,KAAM8F,EACtB,CACF,CAEA,SAASQ,EAAgBR,EAAOlC,GAC9B,OAAO,YACJA,EAAMM,MAAMlE,KAAMmE,WAAa0B,EAAaK,GAAelG,KAAM8F,EACpE,CACF,CC3DA,SAASS,IACPvG,KAAKwG,YAAc,EACrB,CAEA,SAASC,EAAa7C,GACpB,OAAO,WACL5D,KAAKwG,YAAc5C,CACrB,CACF,CAEA,SAAS8C,EAAa9C,GACpB,OAAO,WACL,IAAIK,EAAIL,EAAMM,MAAMlE,KAAMmE,WAC1BnE,KAAKwG,YAAmB,MAALvC,EAAY,GAAKA,CACtC,CACF,CCfA,SAAS0C,IACP3G,KAAK4G,UAAY,EACnB,CAEA,SAASC,EAAajD,GACpB,OAAO,WACL5D,KAAK4G,UAAYhD,CACnB,CACF,CAEA,SAASkD,EAAalD,GACpB,OAAO,WACL,IAAIK,EAAIL,EAAMM,MAAMlE,KAAMmE,WAC1BnE,KAAK4G,UAAiB,MAAL3C,EAAY,GAAKA,CACpC,CACF,CCfA,SAAS8C,IACH/G,KAAKgH,aAAahH,KAAKiH,WAAW/F,YAAYlB,KACpD,CCFA,SAASkH,IACHlH,KAAKmH,iBAAiBnH,KAAKiH,WAAW7F,aAAapB,KAAMA,KAAKiH,WAAWG,WAC/E,CCCA,SAASC,EAAevE,GACtB,OAAO,WACL,IAAIwB,EAAWtE,KAAKW,cAChB2G,EAAMtH,KAAKY,aACf,OAAO0G,IAAQ7E,GAAS6B,EAASiD,gBAAgB3G,eAAiB6B,EAC5D6B,EAASkD,cAAc1E,GACvBwB,EAASmD,gBAAgBH,EAAKxE,EACtC,CACF,CAEA,SAAS4E,EAAajE,GACpB,OAAO,WACL,OAAOzD,KAAKW,cAAc8G,gBAAgBhE,EAASL,MAAOK,EAASJ,MACrE,CACF,CAEe,WAASP,GACtB,IAAIW,EAAWkE,EAAU7E,GACzB,OAAQW,EAASJ,MACXqE,EACAL,GAAgB5D,EACxB,CCrBA,SAASmE,IACP,OAAO,IACT,CCLA,SAASzB,IACP,IAAI1F,EAAST,KAAKiH,WACdxG,GAAQA,EAAOoH,YAAY7H,KACjC,CCHA,SAAS8H,IACP,IAAIC,EAAQ/H,KAAKgI,WAAU,GAAQvH,EAAST,KAAKiH,WACjD,OAAOxG,EAASA,EAAOW,aAAa2G,EAAO/H,KAAKgH,aAAee,CACjE,CAEA,SAASE,IACP,IAAIF,EAAQ/H,KAAKgI,WAAU,GAAOvH,EAAST,KAAKiH,WAChD,OAAOxG,EAASA,EAAOW,aAAa2G,EAAO/H,KAAKgH,aAAee,CACjE,CRKAtC,EAAUzE,UAAY,CACpBiF,IAAK,SAASnD,GACJ9C,KAAK2F,OAAO3C,QAAQF,GACpB,IACN9C,KAAK2F,OAAOuC,KAAKpF,GACjB9C,KAAK0F,MAAM7B,aAAa,QAAS7D,KAAK2F,OAAOwC,KAAK,MAEtD,EACAhC,OAAQ,SAASrD,GACf,IAAIlB,EAAI5B,KAAK2F,OAAO3C,QAAQF,GACxBlB,GAAK,IACP5B,KAAK2F,OAAOyC,OAAOxG,EAAG,GACtB5B,KAAK0F,MAAM7B,aAAa,QAAS7D,KAAK2F,OAAOwC,KAAK,MAEtD,EACAE,SAAU,SAASvF,GACjB,OAAO9C,KAAK2F,OAAO3C,QAAQF,IAAS,CACtC,GS9BF,IAAIwF,GAAe,CAAC,EAETC,GAAQ,KAEK,qBAAbjE,WAEH,iBADQA,SAASiD,kBAErBe,GAAe,CAACE,WAAY,YAAaC,WAAY,cAIzD,SAASC,GAAsBC,EAAUC,EAAOrH,GAE9C,OADAoH,EAAWE,GAAgBF,EAAUC,EAAOrH,GACrC,SAASgH,GACd,IAAIO,EAAUP,EAAMQ,cACfD,IAAYA,IAAY9I,MAAkD,EAAxC8I,EAAQE,wBAAwBhJ,QACrE2I,EAASvG,KAAKpC,KAAMuI,EAExB,CACF,CAEA,SAASM,GAAgBF,EAAUC,EAAOrH,GACxC,OAAO,SAAS0H,GACd,IAAIC,EAASX,GACbA,GAAQU,EACR,IACEN,EAASvG,KAAKpC,KAAMA,KAAKe,SAAU6H,EAAOrH,EAC5C,CAAE,QACAgH,GAAQW,CACV,CACF,CACF,CAUA,SAASC,GAASC,GAChB,OAAO,WACL,IAAIC,EAAKrJ,KAAKsJ,KACd,GAAKD,EAAL,CACA,IAAK,IAAkCE,EAA9BC,EAAI,EAAG5H,GAAK,EAAG6H,EAAIJ,EAAG9I,OAAWiJ,EAAIC,IAAKD,EAC7CD,EAAIF,EAAGG,GAAMJ,EAASM,MAAQH,EAAEG,OAASN,EAASM,MAASH,EAAEzG,OAASsG,EAAStG,KAGjFuG,IAAKzH,GAAK2H,EAFVvJ,KAAK2J,oBAAoBJ,EAAEG,KAAMH,EAAEZ,SAAUY,EAAEK,WAK7ChI,EAAGyH,EAAG9I,OAASqB,SACT5B,KAAKsJ,IATF,CAUjB,CACF,CAEA,SAASO,GAAMT,EAAUxF,EAAOgG,GAC9B,IAAIE,EAAOxB,GAAanF,eAAeiG,EAASM,MAAQhB,GAAwBG,GAChF,OAAO,SAASkB,EAAGnI,EAAGL,GACpB,IAAoBgI,EAAhBF,EAAKrJ,KAAKsJ,KAASX,EAAWmB,EAAKlG,EAAOhC,EAAGL,GACjD,GAAI8H,EAAI,IAAK,IAAIG,EAAI,EAAGC,EAAIJ,EAAG9I,OAAQiJ,EAAIC,IAAKD,EAC9C,IAAKD,EAAIF,EAAGG,IAAIE,OAASN,EAASM,MAAQH,EAAEzG,OAASsG,EAAStG,KAI5D,OAHA9C,KAAK2J,oBAAoBJ,EAAEG,KAAMH,EAAEZ,SAAUY,EAAEK,SAC/C5J,KAAKgK,iBAAiBT,EAAEG,KAAMH,EAAEZ,SAAWA,EAAUY,EAAEK,QAAUA,QACjEL,EAAE3F,MAAQA,GAId5D,KAAKgK,iBAAiBZ,EAASM,KAAMf,EAAUiB,GAC/CL,EAAI,CAACG,KAAMN,EAASM,KAAM5G,KAAMsG,EAAStG,KAAMc,MAAOA,EAAO+E,SAAUA,EAAUiB,QAASA,GACrFP,EACAA,EAAGnB,KAAKqB,GADJvJ,KAAKsJ,KAAO,CAACC,EAExB,CACF,CAuBO,SAASU,GAAYhB,EAAQN,EAAUuB,EAAMC,GAClD,IAAIjB,EAASX,GACbU,EAAOmB,YAAc7B,GACrBA,GAAQU,EACR,IACE,OAAON,EAASzE,MAAMgG,EAAMC,EAC9B,CAAE,QACA5B,GAAQW,CACV,CACF,CCxGA,SAASmB,GAAc1I,EAAM+H,EAAMY,GACjC,IAAIxL,EAASuF,EAAY1C,GACrB4G,EAAQzJ,EAAOyL,YAEE,oBAAVhC,EACTA,EAAQ,IAAIA,EAAMmB,EAAMY,IAExB/B,EAAQzJ,EAAOwF,SAASkG,YAAY,SAChCF,GAAQ/B,EAAMkC,UAAUf,EAAMY,EAAOI,QAASJ,EAAOK,YAAapC,EAAMqC,OAASN,EAAOM,QACvFrC,EAAMkC,UAAUf,GAAM,GAAO,IAGpC/H,EAAK0I,cAAc9B,EACrB,CAEA,SAASsC,GAAiBnB,EAAMY,GAC9B,OAAO,WACL,OAAOD,GAAcrK,KAAM0J,EAAMY,EACnC,CACF,CAEA,SAASQ,GAAiBpB,EAAMY,GAC9B,OAAO,WACL,OAAOD,GAAcrK,KAAM0J,EAAMY,EAAOpG,MAAMlE,KAAMmE,WACtD,CACF,CCKO,IAAI4G,GAAO,CAAC,MAEZ,SAASC,GAAUC,EAAQC,GAChClL,KAAKmL,QAAUF,EACfjL,KAAKoL,SAAWF,CAClB,CAEA,SAASG,KACP,OAAO,IAAIL,GAAU,CAAC,CAAC1G,SAASiD,kBAAmBwD,GACrD,CAEAC,GAAUhK,UAAYqK,GAAUrK,UAAY,CAC1CC,YAAa+J,GACbM,OC1Ca,SAASA,GACA,oBAAXA,IAAuBA,EAASvL,EAASuL,IAEpD,IAAK,IAAIL,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQgL,EAAY,IAAIjL,MAAMmJ,GAAID,EAAI,EAAGA,EAAIC,IAAKD,EAC3F,IAAK,IAAiF7H,EAAM6J,EAAnFjK,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAQkL,EAAWF,EAAU/B,GAAK,IAAIlJ,MAAM0F,GAAmBpE,EAAI,EAAGA,EAAIoE,IAAKpE,GAC9GD,EAAOJ,EAAMK,MAAQ4J,EAAUF,EAAOlJ,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,MAClE,aAAcI,IAAM6J,EAAQzK,SAAWY,EAAKZ,UAChD0K,EAAS7J,GAAK4J,GAKpB,OAAO,IAAIR,GAAUO,EAAWvL,KAAKoL,SACvC,ED8BEM,UE3Ca,SAASJ,GACA,oBAAXA,IAAuBA,EAASK,EAAYL,IAEvD,IAAK,IAAIL,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQgL,EAAY,GAAIL,EAAU,GAAI1B,EAAI,EAAGA,EAAIC,IAAKD,EAC/F,IAAK,IAAyC7H,EAArCJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAcqB,EAAI,EAAGA,EAAIoE,IAAKpE,GAC9DD,EAAOJ,EAAMK,MACf2J,EAAUrD,KAAKoD,EAAOlJ,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,IACnD2J,EAAQhD,KAAKvG,IAKnB,OAAO,IAAIqJ,GAAUO,EAAWL,EAClC,EF+BEU,OG5Ca,SAASC,GACD,oBAAVA,IAAsBA,EAAQC,EAAQD,IAEjD,IAAK,IAAIZ,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQgL,EAAY,IAAIjL,MAAMmJ,GAAID,EAAI,EAAGA,EAAIC,IAAKD,EAC3F,IAAK,IAAuE7H,EAAnEJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAQkL,EAAWF,EAAU/B,GAAK,GAAU5H,EAAI,EAAGA,EAAIoE,IAAKpE,GAC3FD,EAAOJ,EAAMK,KAAOiK,EAAMzJ,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,IAC1DkK,EAASvD,KAAKvG,GAKpB,OAAO,IAAIqJ,GAAUO,EAAWvL,KAAKoL,SACvC,EHiCE1J,KnB4Ba,SAASkC,EAAO5B,GAC7B,IAAK4B,EAGH,OAFAlC,EAAO,IAAIpB,MAAMN,KAAK+L,QAASvC,GAAK,EACpCxJ,KAAKgM,MAAK,SAASjC,GAAKrI,IAAO8H,GAAKO,CAAG,IAChCrI,EAGT,IuBnFsBuK,EvBmFlBC,EAAOlK,EAAMD,EAAUT,EACvB4J,EAAUlL,KAAKoL,SACfH,EAASjL,KAAKmL,QAEG,oBAAVvH,IuBvFWqI,EvBuF4BrI,EAAjBA,EuBtF1B,WACL,OAAOqI,CACT,GvBsFA,IAAK,IAAIxC,EAAIwB,EAAO1K,OAAQF,EAAS,IAAIC,MAAMmJ,GAAIjI,EAAQ,IAAIlB,MAAMmJ,GAAIhI,EAAO,IAAInB,MAAMmJ,GAAID,EAAI,EAAGA,EAAIC,IAAKD,EAAG,CAC/G,IAAI/I,EAASyK,EAAQ1B,GACjBjI,EAAQ0J,EAAOzB,GACf3H,EAAcN,EAAMhB,OACpBmB,EAAOkC,EAAMxB,KAAK3B,EAAQA,GAAUA,EAAOM,SAAUyI,EAAG0B,GACxDpJ,EAAaJ,EAAKnB,OAClB4L,EAAa3K,EAAMgI,GAAK,IAAIlJ,MAAMwB,GAClCsK,EAAc/L,EAAOmJ,GAAK,IAAIlJ,MAAMwB,GAGxCoK,EAAKzL,EAAQc,EAAO4K,EAAYC,EAFhB3K,EAAK+H,GAAK,IAAIlJ,MAAMuB,GAEoBH,EAAMM,GAK9D,IAAK,IAAoBqK,EAAUhL,EAA1BiL,EAAK,EAAGC,EAAK,EAAmBD,EAAKxK,IAAcwK,EAC1D,GAAID,EAAWF,EAAWG,GAAK,CAE7B,IADIA,GAAMC,IAAIA,EAAKD,EAAK,KACfjL,EAAO+K,EAAYG,OAAUA,EAAKzK,IAC3CuK,EAASxL,MAAQQ,GAAQ,IAC3B,CAEJ,CAKA,OAHAhB,EAAS,IAAI2K,GAAU3K,EAAQ6K,IACxBsB,OAAShL,EAChBnB,EAAOoM,MAAQhL,EACRpB,CACT,EmBpEEmB,MpB9Ca,WACb,OAAO,IAAIwJ,GAAUhL,KAAKwM,QAAUxM,KAAKmL,QAAQuB,IAAIC,GAAS3M,KAAKoL,SACrE,EoB6CE3J,KK/Ca,WACb,OAAO,IAAIuJ,GAAUhL,KAAKyM,OAASzM,KAAKmL,QAAQuB,IAAIC,GAAS3M,KAAKoL,SACpE,EL8CEjD,KMnDa,SAASyE,EAASC,EAAUC,GACzC,IAAItL,EAAQxB,KAAKwB,QAASnB,EAASL,KAAMyB,EAAOzB,KAAKyB,OAIrD,OAHAD,EAA2B,oBAAZoL,EAAyBA,EAAQpL,GAASA,EAAMuL,OAAOH,EAAU,IAChE,MAAZC,IAAkBxM,EAASwM,EAASxM,IAC1B,MAAVyM,EAAgBrL,EAAK0E,SAAe2G,EAAOrL,GACxCD,GAASnB,EAASmB,EAAMwL,MAAM3M,GAAQ4M,QAAU5M,CACzD,EN8CE2M,MOlDa,SAAS3B,GAEtB,IAAK,IAAI6B,EAAUlN,KAAKmL,QAASgC,EAAU9B,EAAUF,QAASiC,EAAKF,EAAQ3M,OAAQ8M,EAAKF,EAAQ5M,OAAQkJ,EAAIjK,KAAK8N,IAAIF,EAAIC,GAAKE,EAAS,IAAIjN,MAAM8M,GAAK5D,EAAI,EAAGA,EAAIC,IAAKD,EACpK,IAAK,IAAmG7H,EAA/F6L,EAASN,EAAQ1D,GAAIiE,EAASN,EAAQ3D,GAAIxD,EAAIwH,EAAOjN,OAAQyM,EAAQO,EAAO/D,GAAK,IAAIlJ,MAAM0F,GAAUpE,EAAI,EAAGA,EAAIoE,IAAKpE,GACxHD,EAAO6L,EAAO5L,IAAM6L,EAAO7L,MAC7BoL,EAAMpL,GAAKD,GAKjB,KAAO6H,EAAI4D,IAAM5D,EACf+D,EAAO/D,GAAK0D,EAAQ1D,GAGtB,OAAO,IAAIwB,GAAUuC,EAAQvN,KAAKoL,SACpC,EPoCE6B,MQrDa,WAEb,IAAK,IAAIhC,EAASjL,KAAKmL,QAAS3B,GAAK,EAAGC,EAAIwB,EAAO1K,SAAUiJ,EAAIC,GAC/D,IAAK,IAA8D9H,EAA1DJ,EAAQ0J,EAAOzB,GAAI5H,EAAIL,EAAMhB,OAAS,EAAGc,EAAOE,EAAMK,KAAYA,GAAK,IAC1ED,EAAOJ,EAAMK,MACXP,GAA6C,EAArCM,EAAKqH,wBAAwB3H,IAAWA,EAAK4F,WAAW7F,aAAaO,EAAMN,GACvFA,EAAOM,GAKb,OAAO3B,IACT,ER0CE0N,KlBpDa,SAASC,GAGtB,SAASC,EAAYtL,EAAGC,GACtB,OAAOD,GAAKC,EAAIoL,EAAQrL,EAAEvB,SAAUwB,EAAExB,WAAauB,GAAKC,CAC1D,CAJKoL,IAASA,EAAUtL,GAMxB,IAAK,IAAI4I,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQsN,EAAa,IAAIvN,MAAMmJ,GAAID,EAAI,EAAGA,EAAIC,IAAKD,EAAG,CAC/F,IAAK,IAAmF7H,EAA/EJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAQuN,EAAYD,EAAWrE,GAAK,IAAIlJ,MAAM0F,GAAUpE,EAAI,EAAGA,EAAIoE,IAAKpE,GACxGD,EAAOJ,EAAMK,MACfkM,EAAUlM,GAAKD,GAGnBmM,EAAUJ,KAAKE,EACjB,CAEA,OAAO,IAAI5C,GAAU6C,EAAY7N,KAAKoL,UAAU6B,OAClD,EkBoCE7K,KSvDa,WACb,IAAIjD,EAAWgF,UAAU,GAGzB,OAFAA,UAAU,GAAKnE,KACfb,EAAS+E,MAAM,KAAMC,WACdnE,IACT,ETmDE+N,MUxDa,WACb,IAAIA,EAAQ,IAAIzN,MAAMN,KAAK+L,QAASnK,GAAK,EAEzC,OADA5B,KAAKgM,MAAK,WAAa+B,IAAQnM,GAAK5B,IAAM,IACnC+N,CACT,EVqDEpM,KWzDa,WAEb,IAAK,IAAIsJ,EAASjL,KAAKmL,QAAS3B,EAAI,EAAGC,EAAIwB,EAAO1K,OAAQiJ,EAAIC,IAAKD,EACjE,IAAK,IAAIjI,EAAQ0J,EAAOzB,GAAI5H,EAAI,EAAGoE,EAAIzE,EAAMhB,OAAQqB,EAAIoE,IAAKpE,EAAG,CAC/D,IAAID,EAAOJ,EAAMK,GACjB,GAAID,EAAM,OAAOA,CACnB,CAGF,OAAO,IACT,EXgDEoK,KY1Da,WACb,IAAIA,EAAO,EAEX,OADA/L,KAAKgM,MAAK,aAAeD,CAAM,IACxBA,CACT,EZuDE7L,Ma3Da,WACb,OAAQF,KAAK2B,MACf,Eb0DEqK,Kc5Da,SAAS7M,GAEtB,IAAK,IAAI8L,EAASjL,KAAKmL,QAAS3B,EAAI,EAAGC,EAAIwB,EAAO1K,OAAQiJ,EAAIC,IAAKD,EACjE,IAAK,IAAgD7H,EAA5CJ,EAAQ0J,EAAOzB,GAAI5H,EAAI,EAAGoE,EAAIzE,EAAMhB,OAAcqB,EAAIoE,IAAKpE,GAC9DD,EAAOJ,EAAMK,KAAIzC,EAASiD,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,GAI/D,OAAOvB,IACT,EdoDEgO,KfnBa,SAASlL,EAAMc,GAC5B,IAAIH,EAAWkE,EAAU7E,GAEzB,GAAIqB,UAAU5D,OAAS,EAAG,CACxB,IAAIoB,EAAO3B,KAAK2B,OAChB,OAAO8B,EAASJ,MACV1B,EAAKsM,eAAexK,EAASL,MAAOK,EAASJ,OAC7C1B,EAAKiE,aAAanC,EAC1B,CAEA,OAAOzD,KAAKgM,MAAe,MAATpI,EACXH,EAASJ,MAAQG,EAAeF,EAAgC,oBAAVM,EACtDH,EAASJ,MAAQe,EAAiBJ,EAClCP,EAASJ,MAAQS,EAAiBH,GAAgBF,EAAUG,GACrE,EeMEY,MbxCa,SAAS1B,EAAMc,EAAOe,GACnC,OAAOR,UAAU5D,OAAS,EACpBP,KAAKgM,MAAe,MAATpI,EACLW,EAA+B,oBAAVX,EACrBiB,EACAH,GAAe5B,EAAMc,EAAmB,MAAZe,EAAmB,GAAKA,IAC1DG,EAAW9E,KAAK2B,OAAQmB,EAChC,EakCEoL,SZ3Ca,SAASpL,EAAMc,GAC5B,OAAOO,UAAU5D,OAAS,EACpBP,KAAKgM,MAAe,MAATpI,EACPqB,EAAkC,oBAAVrB,EACxBuB,EACAD,GAAkBpC,EAAMc,IAC5B5D,KAAK2B,OAAOmB,EACpB,EYqCEqL,QXHa,SAASrL,EAAMc,GAC5B,IAAIkC,EAAQV,EAAWtC,EAAO,IAE9B,GAAIqB,UAAU5D,OAAS,EAAG,CAExB,IADA,IAAIwF,EAAOP,EAAUxF,KAAK2B,QAASC,GAAK,EAAGoE,EAAIF,EAAMvF,SAC5CqB,EAAIoE,OAAQD,EAAKsC,SAASvC,EAAMlE,IAAK,OAAO,EACrD,OAAO,CACT,CAEA,OAAO5B,KAAKgM,MAAuB,oBAAVpI,EACnB0C,EAAkB1C,EAClBwC,EACAC,GAAcP,EAAOlC,GAC7B,EWTEwK,KVhDa,SAASxK,GACtB,OAAOO,UAAU5D,OACXP,KAAKgM,KAAc,MAATpI,EACN2C,GAA+B,oBAAV3C,EACrB8C,EACAD,GAAc7C,IAClB5D,KAAK2B,OAAO6E,WACpB,EU0CE6H,KTjDa,SAASzK,GACtB,OAAOO,UAAU5D,OACXP,KAAKgM,KAAc,MAATpI,EACN+C,GAA+B,oBAAV/C,EACrBkD,EACAD,GAAcjD,IAClB5D,KAAK2B,OAAOiF,SACpB,ES2CEG,MR/Da,WACb,OAAO/G,KAAKgM,KAAKjF,EACnB,EQ8DEG,MPhEa,WACb,OAAOlH,KAAKgM,KAAK9E,EACnB,EO+DE6F,OenEa,SAASjK,GACtB,IAAIwL,EAAyB,oBAATxL,EAAsBA,EAAOyL,EAAQzL,GACzD,OAAO9C,KAAKsL,QAAO,WACjB,OAAOtL,KAAKkB,YAAYoN,EAAOpK,MAAMlE,KAAMmE,WAC7C,GACF,Ef+DEqK,OL/Da,SAAS1L,EAAM2L,GAC5B,IAAIH,EAAyB,oBAATxL,EAAsBA,EAAOyL,EAAQzL,GACrDwI,EAAmB,MAAVmD,EAAiB7G,EAAiC,oBAAX6G,EAAwBA,EAAS1O,EAAS0O,GAC9F,OAAOzO,KAAKsL,QAAO,WACjB,OAAOtL,KAAKoB,aAAakN,EAAOpK,MAAMlE,KAAMmE,WAAYmH,EAAOpH,MAAMlE,KAAMmE,YAAc,KAC3F,GACF,EK0DEgC,OJlEa,WACb,OAAOnG,KAAKgM,KAAK7F,EACnB,EIiEE4B,MH9Da,SAAS2G,GACtB,OAAO1O,KAAKsL,OAAOoD,EAAOzG,EAAsBH,EAClD,EG6DEpH,MgBzEa,SAASkD,GACtB,OAAOO,UAAU5D,OACXP,KAAKkO,SAAS,WAAYtK,GAC1B5D,KAAK2B,OAAOZ,QACpB,EhBsEEsI,GFEa,SAASD,EAAUxF,EAAOgG,GACvC,IAA+ChI,EAAyB+M,EAApEC,EA5CN,SAAwBA,GACtB,OAAOA,EAAUtJ,OAAOC,MAAM,SAASmH,KAAI,SAASiC,GAClD,IAAI7L,EAAO,GAAIlB,EAAI+M,EAAE3L,QAAQ,KAE7B,OADIpB,GAAK,IAAGkB,EAAO6L,EAAE1L,MAAMrB,EAAI,GAAI+M,EAAIA,EAAE1L,MAAM,EAAGrB,IAC3C,CAAC8H,KAAMiF,EAAG7L,KAAMA,EACzB,GACF,CAsCkB+L,CAAezF,EAAW,IAAQpD,EAAI4I,EAAUrO,OAEhE,KAAI4D,UAAU5D,OAAS,GAAvB,CAcA,IAFA8I,EAAKzF,EAAQiG,GAAQV,GACN,MAAXS,IAAiBA,GAAU,GAC1BhI,EAAI,EAAGA,EAAIoE,IAAKpE,EAAG5B,KAAKgM,KAAK3C,EAAGuF,EAAUhN,GAAIgC,EAAOgG,IAC1D,OAAO5J,IALP,CATE,IAAIqJ,EAAKrJ,KAAK2B,OAAO2H,KACrB,GAAID,EAAI,IAAK,IAA0BE,EAAtBC,EAAI,EAAGC,EAAIJ,EAAG9I,OAAWiJ,EAAIC,IAAKD,EACjD,IAAK5H,EAAI,EAAG2H,EAAIF,EAAGG,GAAI5H,EAAIoE,IAAKpE,EAC9B,IAAK+M,EAAIC,EAAUhN,IAAI8H,OAASH,EAAEG,MAAQiF,EAAE7L,OAASyG,EAAEzG,KACrD,OAAOyG,EAAE3F,KAWnB,EEpBEkL,SD9Ca,SAASpF,EAAMY,GAC5B,OAAOtK,KAAKgM,MAAwB,oBAAX1B,EACnBQ,GACAD,IAAkBnB,EAAMY,GAChC,GC6CA,UiB5Ee,YAASvK,GACtB,MAA2B,kBAAbA,EACR,IAAIiL,GAAU,CAAC,CAAC1G,SAASrE,cAAcF,KAAa,CAACuE,SAASiD,kBAC9D,IAAIyD,GAAU,CAAC,CAACjL,IAAYgL,GACpC,CCJe,cAEb,IADA,IAAqBgE,EAAjBC,EAAUzG,GACPwG,EAASC,EAAQ5E,aAAa4E,EAAUD,EAC/C,OAAOC,CACT,CCNe,YAASrN,EAAM4G,GAC5B,IAAI7F,EAAMf,EAAKsN,iBAAmBtN,EAElC,GAAIe,EAAIwM,eAAgB,CACtB,IAAIC,EAAQzM,EAAIwM,iBAGhB,OAFAC,EAAMlD,EAAI1D,EAAM6G,QAASD,EAAME,EAAI9G,EAAM+G,QAElC,EADPH,EAAQA,EAAMI,gBAAgB5N,EAAK6N,eAAeC,YACpCxD,EAAGkD,EAAME,EACzB,CAEA,IAAIK,EAAO/N,EAAKgO,wBAChB,MAAO,CAACpH,EAAM6G,QAAUM,EAAKE,KAAOjO,EAAKkO,WAAYtH,EAAM+G,QAAUI,EAAKI,IAAMnO,EAAKoO,UACvF,CCTe,YAASpO,GACtB,IAAI4G,EAAQ6B,KAEZ,OADI7B,EAAMyH,iBAAgBzH,EAAQA,EAAMyH,eAAe,IAChDb,GAAMxN,EAAM4G,EACrB,CCLe,YAASxI,GACtB,MAA2B,kBAAbA,EACR,IAAIiL,GAAU,CAAC1G,SAASnE,iBAAiBJ,IAAY,CAACuE,SAASiD,kBAC/D,IAAIyD,GAAU,CAAa,MAAZjL,EAAmB,GAAKA,GAAWgL,GAC1D,C,gBCJO,SAASkF,KACd1H,GAAM2H,0BACR,CAEe,cACb3H,GAAM4H,iBACN5H,GAAM2H,0BACR,CCNe,YAASE,GACtB,IAAIrF,EAAOqF,EAAK9L,SAASiD,gBACrB8D,EAAYC,GAAO8E,GAAM/G,GAAG,iBAAkBgH,IAAS,GACvD,kBAAmBtF,EACrBM,EAAUhC,GAAG,mBAAoBgH,IAAS,IAE1CtF,EAAKuF,WAAavF,EAAKvG,MAAM+L,cAC7BxF,EAAKvG,MAAM+L,cAAgB,OAE/B,CAEO,SAASC,GAAQJ,EAAMK,GAC5B,IAAI1F,EAAOqF,EAAK9L,SAASiD,gBACrB8D,EAAYC,GAAO8E,GAAM/G,GAAG,iBAAkB,MAC9CoH,IACFpF,EAAUhC,GAAG,aAAcgH,IAAS,GACpC1Q,YAAW,WAAa0L,EAAUhC,GAAG,aAAc,KAAO,GAAG,IAE3D,kBAAmB0B,EACrBM,EAAUhC,GAAG,mBAAoB,OAEjC0B,EAAKvG,MAAM+L,cAAgBxF,EAAKuF,kBACzBvF,EAAKuF,WAEhB,C,gBCxBe,YAAS3O,EAAM+O,EAASC,GACjCxM,UAAU5D,OAAS,IAAGoQ,EAAaD,EAASA,EAAUtG,KAAc4F,gBAExE,IAAK,IAA6CY,EAAzChP,EAAI,EAAGoE,EAAI0K,EAAUA,EAAQnQ,OAAS,EAAUqB,EAAIoE,IAAKpE,EAChE,IAAKgP,EAAQF,EAAQ9O,IAAI+O,aAAeA,EACtC,OAAOxB,GAAMxN,EAAMiP,GAIvB,OAAO,IACT,CCbA,IAAIC,GAAO,CAACjN,MAAOA,QAEnB,SAASkL,KACP,IAAK,IAAyCH,EAArC/M,EAAI,EAAGoE,EAAI7B,UAAU5D,OAAQuQ,EAAI,CAAC,EAAMlP,EAAIoE,IAAKpE,EAAG,CAC3D,KAAM+M,EAAIxK,UAAUvC,GAAK,KAAQ+M,KAAKmC,GAAM,QAAQC,KAAKpC,GAAI,MAAM,IAAIqC,MAAM,iBAAmBrC,GAChGmC,EAAEnC,GAAK,EACT,CACA,OAAO,IAAIsC,GAASH,EACtB,CAEA,SAASG,GAASH,GAChB9Q,KAAK8Q,EAAIA,CACX,CAoDA,SAASI,GAAIxH,EAAM5G,GACjB,IAAK,IAA4BqO,EAAxBvP,EAAI,EAAGoE,EAAI0D,EAAKnJ,OAAWqB,EAAIoE,IAAKpE,EAC3C,IAAKuP,EAAIzH,EAAK9H,IAAIkB,OAASA,EACzB,OAAOqO,EAAEvN,KAGf,CAEA,SAASwN,GAAI1H,EAAM5G,EAAM3D,GACvB,IAAK,IAAIyC,EAAI,EAAGoE,EAAI0D,EAAKnJ,OAAQqB,EAAIoE,IAAKpE,EACxC,GAAI8H,EAAK9H,GAAGkB,OAASA,EAAM,CACzB4G,EAAK9H,GAAKiP,GAAMnH,EAAOA,EAAKzG,MAAM,EAAGrB,GAAGyP,OAAO3H,EAAKzG,MAAMrB,EAAI,IAC9D,KACF,CAGF,OADgB,MAAZzC,GAAkBuK,EAAKxB,KAAK,CAACpF,KAAMA,EAAMc,MAAOzE,IAC7CuK,CACT,CA1DAuH,GAASjQ,UAAY8N,GAAS9N,UAAY,CACxCC,YAAagQ,GACb5H,GAAI,SAASD,EAAUjK,GACrB,IAEIwP,EAd2B2C,EAY3BR,EAAI9Q,KAAK8Q,EACTS,GAb2BD,EAaOR,GAAf1H,EAAW,IAZnB9D,OAAOC,MAAM,SAASmH,KAAI,SAASiC,GAClD,IAAI7L,EAAO,GAAIlB,EAAI+M,EAAE3L,QAAQ,KAE7B,GADIpB,GAAK,IAAGkB,EAAO6L,EAAE1L,MAAMrB,EAAI,GAAI+M,EAAIA,EAAE1L,MAAM,EAAGrB,IAC9C+M,IAAM2C,EAAMnO,eAAewL,GAAI,MAAM,IAAIqC,MAAM,iBAAmBrC,GACtE,MAAO,CAACjF,KAAMiF,EAAG7L,KAAMA,EACzB,KASMlB,GAAK,EACLoE,EAAIuL,EAAEhR,OAGV,KAAI4D,UAAU5D,OAAS,GAAvB,CAOA,GAAgB,MAAZpB,GAAwC,oBAAbA,EAAyB,MAAM,IAAI6R,MAAM,qBAAuB7R,GAC/F,OAASyC,EAAIoE,GACX,GAAI2I,GAAKvF,EAAWmI,EAAE3P,IAAI8H,KAAMoH,EAAEnC,GAAKyC,GAAIN,EAAEnC,GAAIvF,EAAStG,KAAM3D,QAC3D,GAAgB,MAAZA,EAAkB,IAAKwP,KAAKmC,EAAGA,EAAEnC,GAAKyC,GAAIN,EAAEnC,GAAIvF,EAAStG,KAAM,MAG1E,OAAO9C,IAVP,CAFE,OAAS4B,EAAIoE,OAAQ2I,GAAKvF,EAAWmI,EAAE3P,IAAI8H,QAAUiF,EAAIuC,GAAIJ,EAAEnC,GAAIvF,EAAStG,OAAQ,OAAO6L,CAa/F,EACA6C,KAAM,WACJ,IAAIA,EAAO,CAAC,EAAGV,EAAI9Q,KAAK8Q,EACxB,IAAK,IAAInC,KAAKmC,EAAGU,EAAK7C,GAAKmC,EAAEnC,GAAG1L,QAChC,OAAO,IAAIgO,GAASO,EACtB,EACApP,KAAM,SAASsH,EAAMQ,GACnB,IAAKlE,EAAI7B,UAAU5D,OAAS,GAAK,EAAG,IAAK,IAAgCyF,EAAG2I,EAA/BxE,EAAO,IAAI7J,MAAM0F,GAAIpE,EAAI,EAASA,EAAIoE,IAAKpE,EAAGuI,EAAKvI,GAAKuC,UAAUvC,EAAI,GACnH,IAAK5B,KAAK8Q,EAAE3N,eAAeuG,GAAO,MAAM,IAAIsH,MAAM,iBAAmBtH,GACrE,IAAuB9H,EAAI,EAAGoE,GAAzB2I,EAAI3O,KAAK8Q,EAAEpH,IAAoBnJ,OAAQqB,EAAIoE,IAAKpE,EAAG+M,EAAE/M,GAAGgC,MAAMM,MAAMgG,EAAMC,EACjF,EACAjG,MAAO,SAASwF,EAAMQ,EAAMC,GAC1B,IAAKnK,KAAK8Q,EAAE3N,eAAeuG,GAAO,MAAM,IAAIsH,MAAM,iBAAmBtH,GACrE,IAAK,IAAIiF,EAAI3O,KAAK8Q,EAAEpH,GAAO9H,EAAI,EAAGoE,EAAI2I,EAAEpO,OAAQqB,EAAIoE,IAAKpE,EAAG+M,EAAE/M,GAAGgC,MAAMM,MAAMgG,EAAMC,EACrF,GAsBF,IC/EIsH,GACAC,GD8EJ,MCnFIC,GAAQ,EACRC,GAAU,EACVC,GAAW,EAIXC,GAAY,EACZC,GAAW,EACXC,GAAY,EACZC,GAA+B,kBAAhBC,aAA4BA,YAAYC,IAAMD,YAAc7S,KAC3E+S,GAA6B,kBAAXtT,QAAuBA,OAAOC,sBAAwBD,OAAOC,sBAAsBmN,KAAKpN,QAAU,SAASuT,GAAK1S,WAAW0S,EAAG,GAAK,EAElJ,SAASF,KACd,OAAOJ,KAAaK,GAASE,IAAWP,GAAWE,GAAME,MAAQH,GACnE,CAEA,SAASM,KACPP,GAAW,CACb,CAEO,SAASQ,KACdvS,KAAKwS,MACLxS,KAAKyS,MACLzS,KAAKa,MAAQ,IACf,CAyBO,SAAS6R,GAAMvT,EAAUwT,EAAOC,GACrC,IAAIjE,EAAI,IAAI4D,GAEZ,OADA5D,EAAEkE,QAAQ1T,EAAUwT,EAAOC,GACpBjE,CACT,CAaA,SAASmE,KACPf,IAAYD,GAAYG,GAAME,OAASH,GACvCL,GAAQC,GAAU,EAClB,KAdK,WACLO,OACER,GAEF,IADA,IAAkBoB,EAAdpE,EAAI8C,GACD9C,IACAoE,EAAIhB,GAAWpD,EAAE8D,QAAU,GAAG9D,EAAE6D,MAAMpQ,UAAK4Q,EAAWD,GAC3DpE,EAAIA,EAAE9N,QAEN8Q,EACJ,CAMIsB,EACF,CAAE,QACAtB,GAAQ,EAWZ,WACE,IAAIuB,EAAmBC,EAAfC,EAAK3B,GAAcmB,EAAOS,IAClC,KAAOD,GACDA,EAAGZ,OACDI,EAAOQ,EAAGX,QAAOG,EAAOQ,EAAGX,OAC/BS,EAAKE,EAAIA,EAAKA,EAAGvS,QAEjBsS,EAAKC,EAAGvS,MAAOuS,EAAGvS,MAAQ,KAC1BuS,EAAKF,EAAKA,EAAGrS,MAAQsS,EAAK1B,GAAW0B,GAGzCzB,GAAWwB,EACXI,GAAMV,EACR,CAvBIW,GACAxB,GAAW,CACb,CACF,CAEA,SAASyB,KACP,IAAIrB,EAAMF,GAAME,MAAOQ,EAAQR,EAAML,GACjCa,EA7EU,MA6ESX,IAAaW,EAAOb,GAAYK,EACzD,CAiBA,SAASmB,GAAMV,GACTjB,KACAC,KAASA,GAAUhS,aAAagS,KACxBgB,EAAOb,GACP,IACNa,EAAOS,MAAUzB,GAAUjS,WAAWmT,GAAMF,EAAOX,GAAME,MAAQH,KACjEH,KAAUA,GAAW4B,cAAc5B,OAElCA,KAAUC,GAAYG,GAAME,MAAON,GAAW6B,YAAYF,GAvGnD,MAwGZ7B,GAAQ,EAAGS,GAASU,KAExB,CC3Ge,YAAS3T,EAAUwT,EAAOC,GACvC,IAAIjE,EAAI,IAAI4D,GAMZ,OALAI,EAAiB,MAATA,EAAgB,GAAKA,EAC7BhE,EAAEkE,SAAQc,IACRhF,EAAEiF,OACFzU,EAASwU,EAAUhB,EAAM,GACxBA,EAAOC,GACHjE,CACT,CDgBA4D,GAAMvR,UAAY0R,GAAM1R,UAAY,CAClCC,YAAasR,GACbM,QAAS,SAAS1T,EAAUwT,EAAOC,GACjC,GAAwB,oBAAbzT,EAAyB,MAAM,IAAI0U,UAAU,8BACxDjB,GAAgB,MAARA,EAAeT,MAASS,IAAkB,MAATD,EAAgB,GAAKA,GACzD3S,KAAKa,OAAS6Q,KAAa1R,OAC1B0R,GAAUA,GAAS7Q,MAAQb,KAC1ByR,GAAWzR,KAChB0R,GAAW1R,MAEbA,KAAKwS,MAAQrT,EACba,KAAKyS,MAAQG,EACbU,IACF,EACAM,KAAM,WACA5T,KAAKwS,QACPxS,KAAKwS,MAAQ,KACbxS,KAAKyS,MAAQY,IACbC,KAEJ,GE3CF,IAAIQ,GAAUhF,GAAS,QAAS,MAAO,SAAU,aAC7CiF,GAAa,GAUF,YAASpS,EAAMmB,EAAMpD,EAAIkJ,EAAOrH,EAAOyS,GACpD,IAAIC,EAAYtS,EAAKuS,aACrB,GAAKD,GACA,GAAIvU,KAAMuU,EAAW,YADVtS,EAAKuS,aAAe,CAAC,GAmCvC,SAAgBvS,EAAMjC,EAAIyU,GACxB,IACIC,EADAH,EAAYtS,EAAKuS,aAQrB,SAASG,EAASV,GAChBQ,EAAKG,MAtDc,EAuDnBH,EAAKzB,MAAMG,QAAQ0B,EAAOJ,EAAKxB,MAAOwB,EAAKvB,MAGvCuB,EAAKxB,OAASgB,GAASY,EAAMZ,EAAUQ,EAAKxB,MAClD,CAEA,SAAS4B,EAAMZ,GACb,IAAI/R,EAAG4H,EAAGxD,EAAGuD,EAGb,GAjEmB,IAiEf4K,EAAKG,MAAqB,OAAOV,IAErC,IAAKhS,KAAKqS,EAER,IADA1K,EAAI0K,EAAUrS,IACRkB,OAASqR,EAAKrR,KAApB,CAKA,GAxEe,IAwEXyG,EAAE+K,MAAmB,OAAO1C,GAAQ2C,GAvEzB,IA0EXhL,EAAE+K,OACJ/K,EAAE+K,MAzES,EA0EX/K,EAAEmJ,MAAMkB,OACRrK,EAAEF,GAAGjH,KAAK,YAAaT,EAAMA,EAAKZ,SAAUwI,EAAEX,MAAOW,EAAEhI,cAChD0S,EAAUrS,KAITA,EAAIlC,IACZ6J,EAAE+K,MAjFS,EAkFX/K,EAAEmJ,MAAMkB,OACRrK,EAAEF,GAAGjH,KAAK,SAAUT,EAAMA,EAAKZ,SAAUwI,EAAEX,MAAOW,EAAEhI,cAC7C0S,EAAUrS,GApBe,CAwCpC,GAZAgQ,IAAQ,WA/FS,IAgGXuC,EAAKG,QACPH,EAAKG,MAhGQ,EAiGbH,EAAKzB,MAAMG,QAAQ2B,EAAML,EAAKxB,MAAOwB,EAAKvB,MAC1C4B,EAAKb,GAET,IAIAQ,EAAKG,MA1Ga,EA2GlBH,EAAK9K,GAAGjH,KAAK,QAAST,EAAMA,EAAKZ,SAAUoT,EAAKvL,MAAOuL,EAAK5S,OA3G1C,IA4Gd4S,EAAKG,MAAT,CAKA,IAJAH,EAAKG,MA5GY,EA+GjBF,EAAQ,IAAI9T,MAAM0F,EAAImO,EAAKC,MAAM7T,QAC5BqB,EAAI,EAAG4H,GAAK,EAAG5H,EAAIoE,IAAKpE,GACvB2H,EAAI4K,EAAKC,MAAMxS,GAAGgC,MAAMxB,KAAKT,EAAMA,EAAKZ,SAAUoT,EAAKvL,MAAOuL,EAAK5S,UACrE6S,IAAQ5K,GAAKD,GAGjB6K,EAAM7T,OAASiJ,EAAI,CAVgB,CAWrC,CAEA,SAASgL,EAAKb,GAKZ,IAJA,IAAIhF,EAAIgF,EAAUQ,EAAKM,SAAWN,EAAKO,KAAKtS,KAAK,KAAMuR,EAAUQ,EAAKM,WAAaN,EAAKzB,MAAMG,QAAQe,GAAOO,EAAKG,MAvHlG,EAuHkH,GAC9H1S,GAAK,EACLoE,EAAIoO,EAAM7T,SAELqB,EAAIoE,GACXoO,EAAMxS,GAAGQ,KAAKT,EAAMgN,GA5HN,IAgIZwF,EAAKG,QACPH,EAAK9K,GAAGjH,KAAK,MAAOT,EAAMA,EAAKZ,SAAUoT,EAAKvL,MAAOuL,EAAK5S,OAC1DqS,IAEJ,CAEA,SAASA,IAIP,IAAK,IAAIhS,KAHTuS,EAAKG,MAtIU,EAuIfH,EAAKzB,MAAMkB,cACJK,EAAUvU,GACHuU,EAAW,cAClBtS,EAAKuS,YACd,CA9FAD,EAAUvU,GAAMyU,EAChBA,EAAKzB,MAAQA,GAAM2B,EAAU,EAAGF,EAAKvB,KA8FvC,CAtIEtE,CAAO3M,EAAMjC,EAAI,CACfoD,KAAMA,EACN8F,MAAOA,EACPrH,MAAOA,EACP8H,GAAIyK,GACJM,MAAOL,GACPnB,KAAMoB,EAAOpB,KACbD,MAAOqB,EAAOrB,MACd8B,SAAUT,EAAOS,SACjBC,KAAMV,EAAOU,KACbhC,MAAO,KACP4B,MAvBiB,GAyBrB,CAEO,SAASK,GAAKhT,EAAMjC,GACzB,IAAI2U,EAAWnD,GAAIvP,EAAMjC,GACzB,GAAI2U,EAASC,MA7BM,EA6BW,MAAM,IAAItD,MAAM,+BAC9C,OAAOqD,CACT,CAEO,SAASjD,GAAIzP,EAAMjC,GACxB,IAAI2U,EAAWnD,GAAIvP,EAAMjC,GACzB,GAAI2U,EAASC,MAhCM,EAgCW,MAAM,IAAItD,MAAM,6BAC9C,OAAOqD,CACT,CAEO,SAASnD,GAAIvP,EAAMjC,GACxB,IAAI2U,EAAW1S,EAAKuS,aACpB,IAAKG,KAAcA,EAAWA,EAAS3U,IAAM,MAAM,IAAIsR,MAAM,wBAC7D,OAAOqD,CACT,CC/Ce,YAAS1S,EAAMmB,GAC5B,IACIuR,EACAO,EAEAhT,EAJAqS,EAAYtS,EAAKuS,aAGjBhU,GAAQ,EAGZ,GAAK+T,EAAL,CAIA,IAAKrS,KAFLkB,EAAe,MAARA,EAAe,KAAOA,EAAO,GAE1BmR,GACHI,EAAWJ,EAAUrS,IAAIkB,OAASA,GACvC8R,EAASP,EAASC,MDPA,GCOoBD,EAASC,MDJ/B,ECKhBD,EAASC,MDJM,ECKfD,EAAS3B,MAAMkB,OACfS,EAAShL,GAAGjH,KAAKwS,EAAS,YAAc,SAAUjT,EAAMA,EAAKZ,SAAUsT,EAASzL,MAAOyL,EAAS9S,cACzF0S,EAAUrS,IAL8B1B,GAAQ,EAQrDA,UAAcyB,EAAKuS,YAbD,CAcxB,CCvBe,YAAS5R,EAAGC,GACzB,OAAOD,GAAKA,EAAGC,GAAKA,EAAG,SAASoM,GAC9B,OAAOrM,GAAK,EAAIqM,GAAKpM,EAAIoM,CAC3B,CACF,CCJA,ICEIkG,GDFAC,GAAU,IAAMtV,KAAKuV,GAEdC,GAAW,CACpBC,WAAY,EACZC,WAAY,EACZC,OAAQ,EACRC,MAAO,EACPC,OAAQ,EACRC,OAAQ,GAGK,YAAShT,EAAGC,EAAG4O,EAAGpH,EAAGgJ,EAAGV,GACrC,IAAIgD,EAAQC,EAAQF,EAKpB,OAJIC,EAAS7V,KAAK+V,KAAKjT,EAAIA,EAAIC,EAAIA,MAAID,GAAK+S,EAAQ9S,GAAK8S,IACrDD,EAAQ9S,EAAI6O,EAAI5O,EAAIwH,KAAGoH,GAAK7O,EAAI8S,EAAOrL,GAAKxH,EAAI6S,IAChDE,EAAS9V,KAAK+V,KAAKpE,EAAIA,EAAIpH,EAAIA,MAAIoH,GAAKmE,EAAQvL,GAAKuL,EAAQF,GAASE,GACtEhT,EAAIyH,EAAIxH,EAAI4O,IAAG7O,GAAKA,EAAGC,GAAKA,EAAG6S,GAASA,EAAOC,GAAUA,GACtD,CACLJ,WAAYlC,EACZmC,WAAY7C,EACZ8C,OAAQ3V,KAAKgW,MAAMjT,EAAGD,GAAKwS,GAC3BM,MAAO5V,KAAKiW,KAAKL,GAASN,GAC1BO,OAAQA,EACRC,OAAQA,EAEZ,CEtBA,SAASI,GAAqBC,EAAOC,EAASC,EAASC,GAErD,SAASC,EAAIC,GACX,OAAOA,EAAEzV,OAASyV,EAAED,MAAQ,IAAM,EACpC,CAqCA,OAAO,SAASzT,EAAGC,GACjB,IAAIyT,EAAI,GACJC,EAAI,GAOR,OANA3T,EAAIqT,EAAMrT,GAAIC,EAAIoT,EAAMpT,GAtC1B,SAAmB2T,EAAIC,EAAIC,EAAIC,EAAIL,EAAGC,GACpC,GAAIC,IAAOE,GAAMD,IAAOE,EAAI,CAC1B,IAAIzU,EAAIoU,EAAE9N,KAAK,aAAc,KAAM0N,EAAS,KAAMC,GAClDI,EAAE/N,KAAK,CAACtG,EAAGA,EAAI,EAAGqK,EAAGqK,GAAOJ,EAAIE,IAAM,CAACxU,EAAGA,EAAI,EAAGqK,EAAGqK,GAAOH,EAAIE,IACjE,MAAWD,GAAMC,IACfL,EAAE9N,KAAK,aAAekO,EAAKR,EAAUS,EAAKR,EAE9C,CAgCEU,CAAUjU,EAAE2S,WAAY3S,EAAE4S,WAAY3S,EAAE0S,WAAY1S,EAAE2S,WAAYc,EAAGC,GA9BvE,SAAgB3T,EAAGC,EAAGyT,EAAGC,GACnB3T,IAAMC,GACJD,EAAIC,EAAI,IAAKA,GAAK,IAAcA,EAAID,EAAI,MAAKA,GAAK,KACtD2T,EAAE/N,KAAK,CAACtG,EAAGoU,EAAE9N,KAAK6N,EAAIC,GAAK,UAAW,KAAMF,GAAY,EAAG7J,EAAGqK,GAAOhU,EAAGC,MAC/DA,GACTyT,EAAE9N,KAAK6N,EAAIC,GAAK,UAAYzT,EAAIuT,EAEpC,CAwBEX,CAAO7S,EAAE6S,OAAQ5S,EAAE4S,OAAQa,EAAGC,GAtBhC,SAAe3T,EAAGC,EAAGyT,EAAGC,GAClB3T,IAAMC,EACR0T,EAAE/N,KAAK,CAACtG,EAAGoU,EAAE9N,KAAK6N,EAAIC,GAAK,SAAU,KAAMF,GAAY,EAAG7J,EAAGqK,GAAOhU,EAAGC,KAC9DA,GACTyT,EAAE9N,KAAK6N,EAAIC,GAAK,SAAWzT,EAAIuT,EAEnC,CAiBEV,CAAM9S,EAAE8S,MAAO7S,EAAE6S,MAAOY,EAAGC,GAf7B,SAAeC,EAAIC,EAAIC,EAAIC,EAAIL,EAAGC,GAChC,GAAIC,IAAOE,GAAMD,IAAOE,EAAI,CAC1B,IAAIzU,EAAIoU,EAAE9N,KAAK6N,EAAIC,GAAK,SAAU,KAAM,IAAK,KAAM,KACnDC,EAAE/N,KAAK,CAACtG,EAAGA,EAAI,EAAGqK,EAAGqK,GAAOJ,EAAIE,IAAM,CAACxU,EAAGA,EAAI,EAAGqK,EAAGqK,GAAOH,EAAIE,IACjE,MAAkB,IAAPD,GAAmB,IAAPC,GACrBL,EAAE9N,KAAK6N,EAAIC,GAAK,SAAWI,EAAK,IAAMC,EAAK,IAE/C,CASEG,CAAMlU,EAAE+S,OAAQ/S,EAAEgT,OAAQ/S,EAAE8S,OAAQ9S,EAAE+S,OAAQU,EAAGC,GACjD3T,EAAIC,EAAI,KACD,SAASoM,GAEd,IADA,IAA0BpF,EAAtB3H,GAAK,EAAGoE,EAAIiQ,EAAE1V,SACTqB,EAAIoE,GAAGgQ,GAAGzM,EAAI0M,EAAErU,IAAIA,GAAK2H,EAAE0C,EAAE0C,GACtC,OAAOqH,EAAE7N,KAAK,GAChB,CACF,CACF,CAEO,IAAIsO,GAA0Bf,IDxD9B,SAAkB9R,GACvB,MAAM6F,EAAI,IAA0B,oBAAdiN,UAA2BA,UAAYC,iBAAiB/S,EAAQ,IACtF,OAAO6F,EAAEmN,WAAa5B,GAAW6B,GAAUpN,EAAEnH,EAAGmH,EAAElH,EAAGkH,EAAE0H,EAAG1H,EAAEM,EAAGN,EAAEsJ,EAAGtJ,EAAE4I,EACxE,GCqDoE,OAAQ,MAAO,QACxEyE,GAA0BpB,IDpD9B,SAAkB9R,GACvB,OAAa,MAATA,EAAsBoR,IACrBH,KAASA,GAAUvQ,SAASmD,gBAAgB,6BAA8B,MAC/EoN,GAAQhR,aAAa,YAAaD,IAC5BA,EAAQiR,GAAQkC,UAAUC,QAAQC,eAEjCJ,IADPjT,EAAQA,EAAMsT,QACS5U,EAAGsB,EAAMrB,EAAGqB,EAAMuN,EAAGvN,EAAMmG,EAAGnG,EAAMmP,EAAGnP,EAAMyO,GAFL2C,GAGjE,GC6CoE,KAAM,IAAK,KC5D/E,SAASmC,GAAYzX,EAAIoD,GACvB,IAAIsU,EAAQC,EACZ,OAAO,WACL,IAAIhD,EAAWjD,GAAIpR,KAAMN,GACrB0U,EAAQC,EAASD,MAKrB,GAAIA,IAAUgD,EAEZ,IAAK,IAAIxV,EAAI,EAAGoE,GADhBqR,EAASD,EAAShD,GACS7T,OAAQqB,EAAIoE,IAAKpE,EAC1C,GAAIyV,EAAOzV,GAAGkB,OAASA,EAAM,EAC3BuU,EAASA,EAAOpU,SACTmF,OAAOxG,EAAG,GACjB,KACF,CAIJyS,EAASD,MAAQiD,CACnB,CACF,CAEA,SAASC,GAAc5X,EAAIoD,EAAMc,GAC/B,IAAIwT,EAAQC,EACZ,GAAqB,oBAAVzT,EAAsB,MAAM,IAAIoN,MAC3C,OAAO,WACL,IAAIqD,EAAWjD,GAAIpR,KAAMN,GACrB0U,EAAQC,EAASD,MAKrB,GAAIA,IAAUgD,EAAQ,CACpBC,GAAUD,EAAShD,GAAOnR,QAC1B,IAAK,IAAI0L,EAAI,CAAC7L,KAAMA,EAAMc,MAAOA,GAAQhC,EAAI,EAAGoE,EAAIqR,EAAO9W,OAAQqB,EAAIoE,IAAKpE,EAC1E,GAAIyV,EAAOzV,GAAGkB,OAASA,EAAM,CAC3BuU,EAAOzV,GAAK+M,EACZ,KACF,CAEE/M,IAAMoE,GAAGqR,EAAOnP,KAAKyG,EAC3B,CAEA0F,EAASD,MAAQiD,CACnB,CACF,CAoBO,SAASE,GAAWC,EAAY1U,EAAMc,GAC3C,IAAIlE,EAAK8X,EAAWC,IAOpB,OALAD,EAAWxL,MAAK,WACd,IAAIqI,EAAWjD,GAAIpR,KAAMN,IACxB2U,EAASzQ,QAAUyQ,EAASzQ,MAAQ,CAAC,IAAId,GAAQc,EAAMM,MAAMlE,KAAMmE,UACtE,IAEO,SAASxC,GACd,OAAOuP,GAAIvP,EAAMjC,GAAIkE,MAAMd,EAC7B,CACF,CChFe,YAAS7B,EAAayW,EAAS1W,GAC5CC,EAAYD,UAAY0W,EAAQ1W,UAAYA,EAC5CA,EAAUC,YAAcA,CAC1B,CAEO,SAAS0W,GAAOlX,EAAQmX,GAC7B,IAAI5W,EAAY6W,OAAOvJ,OAAO7N,EAAOO,WACrC,IAAK,IAAIgB,KAAO4V,EAAY5W,EAAUgB,GAAO4V,EAAW5V,GACxD,OAAOhB,CACT,CCPO,SAAS8W,KAAS,CAElB,IAAIC,GAAS,GACTC,GAAW,EAAID,GAEtBE,GAAM,sBACNC,GAAM,oDACNC,GAAM,qDACNC,GAAQ,qBACRC,GAAe,IAAIC,OAAO,UAAUL,MAAOA,MAAOA,UAClDM,GAAe,IAAID,OAAO,UAAUH,MAAOA,MAAOA,UAClDK,GAAgB,IAAIF,OAAO,WAAWL,MAAOA,MAAOA,MAAOC,UAC3DO,GAAgB,IAAIH,OAAO,WAAWH,MAAOA,MAAOA,MAAOD,UAC3DQ,GAAe,IAAIJ,OAAO,UAAUJ,MAAOC,MAAOA,UAClDQ,GAAgB,IAAIL,OAAO,WAAWJ,MAAOC,MAAOA,MAAOD,UAE3DU,GAAQ,CACVC,UAAW,SACXC,aAAc,SACdC,KAAM,MACNC,WAAY,QACZC,MAAO,SACPC,MAAO,SACPC,OAAQ,SACRC,MAAO,EACPC,eAAgB,SAChBC,KAAM,IACNC,WAAY,QACZC,MAAO,SACPC,UAAW,SACXC,UAAW,QACXC,WAAY,QACZC,UAAW,SACXC,MAAO,SACPC,eAAgB,QAChBC,SAAU,SACVC,QAAS,SACTC,KAAM,MACNC,SAAU,IACVC,SAAU,MACVC,cAAe,SACfC,SAAU,SACVC,UAAW,MACXC,SAAU,SACVC,UAAW,SACXC,YAAa,QACbC,eAAgB,QAChBC,WAAY,SACZC,WAAY,SACZC,QAAS,QACTC,WAAY,SACZC,aAAc,QACdC,cAAe,QACfC,cAAe,QACfC,cAAe,QACfC,cAAe,MACfC,WAAY,QACZC,SAAU,SACVC,YAAa,MACbC,QAAS,QACTC,QAAS,QACTC,WAAY,QACZC,UAAW,SACXC,YAAa,SACbC,YAAa,QACbC,QAAS,SACTC,UAAW,SACXC,WAAY,SACZC,KAAM,SACNC,UAAW,SACXC,KAAM,QACNC,MAAO,MACPC,YAAa,SACbC,KAAM,QACNC,SAAU,SACVC,QAAS,SACTC,UAAW,SACXC,OAAQ,QACRC,MAAO,SACPC,MAAO,SACPC,SAAU,SACVC,cAAe,SACfC,UAAW,QACXC,aAAc,SACdC,UAAW,SACXC,WAAY,SACZC,UAAW,SACXC,qBAAsB,SACtBC,UAAW,SACXC,WAAY,QACZC,UAAW,SACXC,UAAW,SACXC,YAAa,SACbC,cAAe,QACfC,aAAc,QACdC,eAAgB,QAChBC,eAAgB,QAChBC,eAAgB,SAChBC,YAAa,SACbC,KAAM,MACNC,UAAW,QACXC,MAAO,SACPC,QAAS,SACTC,OAAQ,QACRC,iBAAkB,QAClBC,WAAY,IACZC,aAAc,SACdC,aAAc,QACdC,eAAgB,QAChBC,gBAAiB,QACjBC,kBAAmB,MACnBC,gBAAiB,QACjBC,gBAAiB,SACjBC,aAAc,QACdC,UAAW,SACXC,UAAW,SACXC,SAAU,SACVC,YAAa,SACbC,KAAM,IACNC,QAAS,SACTC,MAAO,QACPC,UAAW,QACXC,OAAQ,SACRC,UAAW,SACXC,OAAQ,SACRC,cAAe,SACfC,UAAW,SACXC,cAAe,SACfC,cAAe,SACfC,WAAY,SACZC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,WAAY,SACZC,OAAQ,QACRC,cAAe,QACfC,IAAK,SACLC,UAAW,SACXC,UAAW,QACXC,YAAa,QACbC,OAAQ,SACRC,WAAY,SACZC,SAAU,QACVC,SAAU,SACVC,OAAQ,SACRC,OAAQ,SACRC,QAAS,QACTC,UAAW,QACXC,UAAW,QACXC,UAAW,QACXC,KAAM,SACNC,YAAa,MACbC,UAAW,QACXC,IAAK,SACLC,KAAM,MACNC,QAAS,SACTC,OAAQ,SACRC,UAAW,QACXC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,WAAY,SACZC,OAAQ,SACRC,YAAa,UAkBf,SAASC,KACP,OAAOjiB,KAAKkiB,MAAMC,WACpB,CAUA,SAASC,KACP,OAAOpiB,KAAKkiB,MAAMG,WACpB,CAEe,SAASC,GAAMC,GAC5B,IAAI9Y,EAAG+Y,EAEP,OADAD,GAAUA,EAAS,IAAIjd,OAAOmd,eACtBhZ,EAAI2O,GAAMsK,KAAKH,KAAYC,EAAI/Y,EAAE,GAAGlJ,OAAQkJ,EAAIkZ,SAASlZ,EAAE,GAAI,IAAW,IAAN+Y,EAAUI,GAAKnZ,GAC/E,IAAN+Y,EAAU,IAAIK,GAAKpZ,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAY,IAAJA,GAAiB,GAAJA,IAAY,EAAU,GAAJA,EAAU,GACzG,IAAN+Y,EAAUM,GAAKrZ,GAAK,GAAK,IAAMA,GAAK,GAAK,IAAMA,GAAK,EAAI,KAAW,IAAJA,GAAY,KACrE,IAAN+Y,EAAUM,GAAMrZ,GAAK,GAAK,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAY,IAAJA,IAAkB,GAAJA,IAAY,EAAU,GAAJA,GAAY,KAClJ,OACCA,EAAI4O,GAAaqK,KAAKH,IAAW,IAAIM,GAAIpZ,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAI,IAC3DA,EAAI8O,GAAamK,KAAKH,IAAW,IAAIM,GAAW,IAAPpZ,EAAE,GAAW,IAAY,IAAPA,EAAE,GAAW,IAAY,IAAPA,EAAE,GAAW,IAAK,IAC/FA,EAAI+O,GAAckK,KAAKH,IAAWO,GAAKrZ,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,KAC3DA,EAAIgP,GAAciK,KAAKH,IAAWO,GAAY,IAAPrZ,EAAE,GAAW,IAAY,IAAPA,EAAE,GAAW,IAAY,IAAPA,EAAE,GAAW,IAAKA,EAAE,KAC/FA,EAAIiP,GAAagK,KAAKH,IAAWQ,GAAKtZ,EAAE,GAAIA,EAAE,GAAK,IAAKA,EAAE,GAAK,IAAK,IACpEA,EAAIkP,GAAc+J,KAAKH,IAAWQ,GAAKtZ,EAAE,GAAIA,EAAE,GAAK,IAAKA,EAAE,GAAK,IAAKA,EAAE,IACxEmP,GAAMzV,eAAeof,GAAUK,GAAKhK,GAAM2J,IAC/B,gBAAXA,EAA2B,IAAIM,GAAIrgB,IAAKA,IAAKA,IAAK,GAClD,IACR,CAEA,SAASogB,GAAK5c,GACZ,OAAO,IAAI6c,GAAI7c,GAAK,GAAK,IAAMA,GAAK,EAAI,IAAU,IAAJA,EAAU,EAC1D,CAEA,SAAS8c,GAAKE,EAAGC,EAAG1gB,EAAGD,GAErB,OADIA,GAAK,IAAG0gB,EAAIC,EAAI1gB,EAAIC,KACjB,IAAIqgB,GAAIG,EAAGC,EAAG1gB,EAAGD,EAC1B,CASO,SAAS4f,GAAIc,EAAGC,EAAG1gB,EAAG2gB,GAC3B,OAA4B,IAArB/e,UAAU5D,SARQgJ,EAQkByZ,aAPxBlL,KAAQvO,EAAI+Y,GAAM/Y,IAChCA,EAEE,IAAIsZ,IADXtZ,EAAIA,EAAE2Y,OACWc,EAAGzZ,EAAE0Z,EAAG1Z,EAAEhH,EAAGgH,EAAE2Z,SAFjB,IAAIL,IAM6B,IAAIA,GAAIG,EAAGC,EAAG1gB,EAAc,MAAX2gB,EAAkB,EAAIA,GARlF,IAAoB3Z,CAS3B,CAEO,SAASsZ,GAAIG,EAAGC,EAAG1gB,EAAG2gB,GAC3BljB,KAAKgjB,GAAKA,EACVhjB,KAAKijB,GAAKA,EACVjjB,KAAKuC,GAAKA,EACVvC,KAAKkjB,SAAWA,CAClB,CA8BA,SAASC,KACP,MAAO,IAAIC,GAAIpjB,KAAKgjB,KAAKI,GAAIpjB,KAAKijB,KAAKG,GAAIpjB,KAAKuC,IAClD,CAMA,SAAS8gB,KACP,MAAM/gB,EAAIghB,GAAOtjB,KAAKkjB,SACtB,MAAO,GAAS,IAAN5gB,EAAU,OAAS,UAAUihB,GAAOvjB,KAAKgjB,OAAOO,GAAOvjB,KAAKijB,OAAOM,GAAOvjB,KAAKuC,KAAW,IAAND,EAAU,IAAM,KAAKA,MACrH,CAEA,SAASghB,GAAOJ,GACd,OAAOM,MAAMN,GAAW,EAAI1jB,KAAKC,IAAI,EAAGD,KAAK8N,IAAI,EAAG4V,GACtD,CAEA,SAASK,GAAO3f,GACd,OAAOpE,KAAKC,IAAI,EAAGD,KAAK8N,IAAI,IAAK9N,KAAKikB,MAAM7f,IAAU,GACxD,CAEA,SAASwf,GAAIxf,GAEX,QADAA,EAAQ2f,GAAO3f,IACC,GAAK,IAAM,IAAMA,EAAM8f,SAAS,GAClD,CAEA,SAASX,GAAKY,EAAG3N,EAAGwM,EAAGlgB,GAIrB,OAHIA,GAAK,EAAGqhB,EAAI3N,EAAIwM,EAAIhgB,IACfggB,GAAK,GAAKA,GAAK,EAAGmB,EAAI3N,EAAIxT,IAC1BwT,GAAK,IAAG2N,EAAInhB,KACd,IAAIohB,GAAID,EAAG3N,EAAGwM,EAAGlgB,EAC1B,CAEO,SAASuhB,GAAWta,GACzB,GAAIA,aAAaqa,GAAK,OAAO,IAAIA,GAAIra,EAAEoa,EAAGpa,EAAEyM,EAAGzM,EAAEiZ,EAAGjZ,EAAE2Z,SAEtD,GADM3Z,aAAauO,KAAQvO,EAAI+Y,GAAM/Y,KAChCA,EAAG,OAAO,IAAIqa,GACnB,GAAIra,aAAaqa,GAAK,OAAOra,EAE7B,IAAIyZ,GADJzZ,EAAIA,EAAE2Y,OACIc,EAAI,IACVC,EAAI1Z,EAAE0Z,EAAI,IACV1gB,EAAIgH,EAAEhH,EAAI,IACV+K,EAAM9N,KAAK8N,IAAI0V,EAAGC,EAAG1gB,GACrB9C,EAAMD,KAAKC,IAAIujB,EAAGC,EAAG1gB,GACrBohB,EAAInhB,IACJwT,EAAIvW,EAAM6N,EACVkV,GAAK/iB,EAAM6N,GAAO,EAUtB,OATI0I,GACa2N,EAAXX,IAAMvjB,GAAUwjB,EAAI1gB,GAAKyT,EAAc,GAATiN,EAAI1gB,GAC7B0gB,IAAMxjB,GAAU8C,EAAIygB,GAAKhN,EAAI,GAC5BgN,EAAIC,GAAKjN,EAAI,EACvBA,GAAKwM,EAAI,GAAM/iB,EAAM6N,EAAM,EAAI7N,EAAM6N,EACrCqW,GAAK,IAEL3N,EAAIwM,EAAI,GAAKA,EAAI,EAAI,EAAImB,EAEpB,IAAIC,GAAID,EAAG3N,EAAGwM,EAAGjZ,EAAE2Z,QAC5B,CAMA,SAASU,GAAID,EAAG3N,EAAGwM,EAAGU,GACpBljB,KAAK2jB,GAAKA,EACV3jB,KAAKgW,GAAKA,EACVhW,KAAKwiB,GAAKA,EACVxiB,KAAKkjB,SAAWA,CAClB,CAsCA,SAASY,GAAOlgB,GAEd,OADAA,GAASA,GAAS,GAAK,KACR,EAAIA,EAAQ,IAAMA,CACnC,CAEA,SAASmgB,GAAOngB,GACd,OAAOpE,KAAKC,IAAI,EAAGD,KAAK8N,IAAI,EAAG1J,GAAS,GAC1C,CAGA,SAASogB,GAAQL,EAAGtW,EAAI4W,GACtB,OAGY,KAHJN,EAAI,GAAKtW,GAAM4W,EAAK5W,GAAMsW,EAAI,GAChCA,EAAI,IAAMM,EACVN,EAAI,IAAMtW,GAAM4W,EAAK5W,IAAO,IAAMsW,GAAK,GACvCtW,EACR,CC3YO,SAAS6W,GAAM9Q,EAAI+Q,EAAIC,EAAIC,EAAIC,GACpC,IAAInR,EAAKC,EAAKA,EAAImR,EAAKpR,EAAKC,EAC5B,QAAS,EAAI,EAAIA,EAAK,EAAID,EAAKoR,GAAMJ,GAC9B,EAAI,EAAIhR,EAAK,EAAIoR,GAAMH,GACvB,EAAI,EAAIhR,EAAK,EAAID,EAAK,EAAIoR,GAAMF,EACjCE,EAAKD,GAAM,CACnB,CDmKAzkB,GAAOiY,GAAOwK,GAAO,CACnB9Q,IAAAA,CAAKgT,GACH,OAAO3M,OAAO4M,OAAO,IAAIzkB,KAAKiB,YAAajB,KAAMwkB,EACnD,EACAE,WAAAA,GACE,OAAO1kB,KAAKkiB,MAAMwC,aACpB,EACAtB,IAAKnB,GACLE,UAAWF,GACX0C,WAUF,WACE,OAAO3kB,KAAKkiB,MAAMyC,YACpB,EAXEC,UAaF,WACE,OAAOf,GAAW7jB,MAAM4kB,WAC1B,EAdEvC,UAAWD,GACXsB,SAAUtB,KAiEZviB,GAAOgjB,GAAKX,GAAKvK,GAAOG,GAAO,CAC7BE,QAAAA,CAAS6M,GAEP,OADAA,EAAS,MAALA,EAAY7M,GAAWxY,KAAKslB,IAAI9M,GAAU6M,GACvC,IAAIhC,GAAI7iB,KAAKgjB,EAAI6B,EAAG7kB,KAAKijB,EAAI4B,EAAG7kB,KAAKuC,EAAIsiB,EAAG7kB,KAAKkjB,QAC1D,EACAnL,MAAAA,CAAO8M,GAEL,OADAA,EAAS,MAALA,EAAY9M,GAASvY,KAAKslB,IAAI/M,GAAQ8M,GACnC,IAAIhC,GAAI7iB,KAAKgjB,EAAI6B,EAAG7kB,KAAKijB,EAAI4B,EAAG7kB,KAAKuC,EAAIsiB,EAAG7kB,KAAKkjB,QAC1D,EACAhB,GAAAA,GACE,OAAOliB,IACT,EACA+kB,KAAAA,GACE,OAAO,IAAIlC,GAAIU,GAAOvjB,KAAKgjB,GAAIO,GAAOvjB,KAAKijB,GAAIM,GAAOvjB,KAAKuC,GAAI+gB,GAAOtjB,KAAKkjB,SAC7E,EACAwB,WAAAA,GACE,OAAS,IAAO1kB,KAAKgjB,GAAKhjB,KAAKgjB,EAAI,QAC1B,IAAOhjB,KAAKijB,GAAKjjB,KAAKijB,EAAI,QAC1B,IAAOjjB,KAAKuC,GAAKvC,KAAKuC,EAAI,OAC3B,GAAKvC,KAAKkjB,SAAWljB,KAAKkjB,SAAW,CAC/C,EACAE,IAAKD,GACLhB,UAAWgB,GACXwB,WASF,WACE,MAAO,IAAIvB,GAAIpjB,KAAKgjB,KAAKI,GAAIpjB,KAAKijB,KAAKG,GAAIpjB,KAAKuC,KAAK6gB,GAA+C,KAA1CI,MAAMxjB,KAAKkjB,SAAW,EAAIljB,KAAKkjB,WAC3F,EAVEb,UAAWgB,GACXK,SAAUL,MAyEZxjB,GAAO+jB,IAXA,SAAaD,EAAG3N,EAAGwM,EAAGU,GAC3B,OAA4B,IAArB/e,UAAU5D,OAAesjB,GAAWF,GAAK,IAAIC,GAAID,EAAG3N,EAAGwM,EAAc,MAAXU,EAAkB,EAAIA,EACzF,GASiBvL,GAAOG,GAAO,CAC7BE,QAAAA,CAAS6M,GAEP,OADAA,EAAS,MAALA,EAAY7M,GAAWxY,KAAKslB,IAAI9M,GAAU6M,GACvC,IAAIjB,GAAI5jB,KAAK2jB,EAAG3jB,KAAKgW,EAAGhW,KAAKwiB,EAAIqC,EAAG7kB,KAAKkjB,QAClD,EACAnL,MAAAA,CAAO8M,GAEL,OADAA,EAAS,MAALA,EAAY9M,GAASvY,KAAKslB,IAAI/M,GAAQ8M,GACnC,IAAIjB,GAAI5jB,KAAK2jB,EAAG3jB,KAAKgW,EAAGhW,KAAKwiB,EAAIqC,EAAG7kB,KAAKkjB,QAClD,EACAhB,GAAAA,GACE,IAAIyB,EAAI3jB,KAAK2jB,EAAI,IAAqB,KAAd3jB,KAAK2jB,EAAI,GAC7B3N,EAAIwN,MAAMG,IAAMH,MAAMxjB,KAAKgW,GAAK,EAAIhW,KAAKgW,EACzCwM,EAAIxiB,KAAKwiB,EACTyB,EAAKzB,GAAKA,EAAI,GAAMA,EAAI,EAAIA,GAAKxM,EACjC3I,EAAK,EAAImV,EAAIyB,EACjB,OAAO,IAAIpB,GACTmB,GAAQL,GAAK,IAAMA,EAAI,IAAMA,EAAI,IAAKtW,EAAI4W,GAC1CD,GAAQL,EAAGtW,EAAI4W,GACfD,GAAQL,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAKtW,EAAI4W,GACzCjkB,KAAKkjB,QAET,EACA6B,KAAAA,GACE,OAAO,IAAInB,GAAIE,GAAO9jB,KAAK2jB,GAAII,GAAO/jB,KAAKgW,GAAI+N,GAAO/jB,KAAKwiB,GAAIc,GAAOtjB,KAAKkjB,SAC7E,EACAwB,WAAAA,GACE,OAAQ,GAAK1kB,KAAKgW,GAAKhW,KAAKgW,GAAK,GAAKwN,MAAMxjB,KAAKgW,KACzC,GAAKhW,KAAKwiB,GAAKxiB,KAAKwiB,GAAK,GACzB,GAAKxiB,KAAKkjB,SAAWljB,KAAKkjB,SAAW,CAC/C,EACA0B,SAAAA,GACE,MAAMtiB,EAAIghB,GAAOtjB,KAAKkjB,SACtB,MAAO,GAAS,IAAN5gB,EAAU,OAAS,UAAUwhB,GAAO9jB,KAAK2jB,OAAwB,IAAjBI,GAAO/jB,KAAKgW,QAA+B,IAAjB+N,GAAO/jB,KAAKwiB,MAAkB,IAANlgB,EAAU,IAAM,KAAKA,MACnI,KEzXF,OAAe2J,GAAK,IAAMA,ECE1B,SAAS+Y,GAAO1iB,EAAGyH,GACjB,OAAO,SAAS4E,GACd,OAAOrM,EAAIqM,EAAI5E,CACjB,CACF,CAaO,SAASkb,GAAM5V,GACpB,OAAoB,KAAZA,GAAKA,GAAW6V,GAAU,SAAS5iB,EAAGC,GAC5C,OAAOA,EAAID,EAbf,SAAqBA,EAAGC,EAAG8M,GACzB,OAAO/M,EAAI9C,KAAKslB,IAAIxiB,EAAG+M,GAAI9M,EAAI/C,KAAKslB,IAAIviB,EAAG8M,GAAK/M,EAAG+M,EAAI,EAAIA,EAAG,SAASV,GACrE,OAAOnP,KAAKslB,IAAIxiB,EAAIqM,EAAIpM,EAAG8M,EAC7B,CACF,CASmB8V,CAAY7iB,EAAGC,EAAG8M,GAAK+V,GAAS5B,MAAMlhB,GAAKC,EAAID,EAChE,CACF,CAEe,SAAS4iB,GAAQ5iB,EAAGC,GACjC,IAAIwH,EAAIxH,EAAID,EACZ,OAAOyH,EAAIib,GAAO1iB,EAAGyH,GAAKqb,GAAS5B,MAAMlhB,GAAKC,EAAID,EACpD,CCvBA,OAAe,SAAU+iB,EAAShW,GAChC,IAAIiT,EAAQ2C,GAAM5V,GAElB,SAAS6S,EAAI3N,EAAO+Q,GAClB,IAAItC,EAAIV,GAAO/N,EAAQgR,GAAShR,IAAQyO,GAAIsC,EAAMC,GAASD,IAAMtC,GAC7DC,EAAIX,EAAM/N,EAAM0O,EAAGqC,EAAIrC,GACvB1gB,EAAI+f,EAAM/N,EAAMhS,EAAG+iB,EAAI/iB,GACvB2gB,EAAUgC,GAAQ3Q,EAAM2O,QAASoC,EAAIpC,SACzC,OAAO,SAASvU,GAKd,OAJA4F,EAAMyO,EAAIA,EAAErU,GACZ4F,EAAM0O,EAAIA,EAAEtU,GACZ4F,EAAMhS,EAAIA,EAAEoM,GACZ4F,EAAM2O,QAAUA,EAAQvU,GACjB4F,EAAQ,EACjB,CACF,CAIA,OAFA2N,EAAI+C,MAAQI,EAELnD,CACR,CApBD,CAoBG,GAEH,SAASsD,GAAUC,GACjB,OAAO,SAASC,GACd,IAII9jB,EAAG0gB,EAJHtc,EAAI0f,EAAOnlB,OACXyiB,EAAI,IAAI1iB,MAAM0F,GACdid,EAAI,IAAI3iB,MAAM0F,GACdzD,EAAI,IAAIjC,MAAM0F,GAElB,IAAKpE,EAAI,EAAGA,EAAIoE,IAAKpE,EACnB0gB,EAAQiD,GAASG,EAAO9jB,IACxBohB,EAAEphB,GAAK0gB,EAAMU,GAAK,EAClBC,EAAErhB,GAAK0gB,EAAMW,GAAK,EAClB1gB,EAAEX,GAAK0gB,EAAM/f,GAAK,EAMpB,OAJAygB,EAAIyC,EAAOzC,GACXC,EAAIwC,EAAOxC,GACX1gB,EAAIkjB,EAAOljB,GACX+f,EAAMY,QAAU,EACT,SAASvU,GAId,OAHA2T,EAAMU,EAAIA,EAAErU,GACZ2T,EAAMW,EAAIA,EAAEtU,GACZ2T,EAAM/f,EAAIA,EAAEoM,GACL2T,EAAQ,EACjB,CACF,CACF,CAEsBkD,IH7CP,SAASG,GACtB,IAAI3f,EAAI2f,EAAOplB,OAAS,EACxB,OAAO,SAASoO,GACd,IAAI/M,EAAI+M,GAAK,EAAKA,EAAI,EAAKA,GAAK,GAAKA,EAAI,EAAG3I,EAAI,GAAKxG,KAAKomB,MAAMjX,EAAI3I,GAChEoe,EAAKuB,EAAO/jB,GACZyiB,EAAKsB,EAAO/jB,EAAI,GAChBuiB,EAAKviB,EAAI,EAAI+jB,EAAO/jB,EAAI,GAAK,EAAIwiB,EAAKC,EACtCC,EAAK1iB,EAAIoE,EAAI,EAAI2f,EAAO/jB,EAAI,GAAK,EAAIyiB,EAAKD,EAC9C,OAAOF,IAAOvV,EAAI/M,EAAIoE,GAAKA,EAAGme,EAAIC,EAAIC,EAAIC,EAC5C,CACF,IGoC4BkB,ICpDb,SAASG,GACtB,IAAI3f,EAAI2f,EAAOplB,OACf,OAAO,SAASoO,GACd,IAAI/M,EAAIpC,KAAKomB,QAAQjX,GAAK,GAAK,IAAMA,EAAIA,GAAK3I,GAC1Cme,EAAKwB,GAAQ/jB,EAAIoE,EAAI,GAAKA,GAC1Boe,EAAKuB,EAAO/jB,EAAIoE,GAChBqe,EAAKsB,GAAQ/jB,EAAI,GAAKoE,GACtBse,EAAKqB,GAAQ/jB,EAAI,GAAKoE,GAC1B,OAAOke,IAAOvV,EAAI/M,EAAIoE,GAAKA,EAAGme,EAAIC,EAAIC,EAAIC,EAC5C,CACF,IDyCO,IEnDHuB,GAAM,8CACNC,GAAM,IAAIxN,OAAOuN,GAAI9W,OAAQ,KAclB,YAASzM,EAAGC,GACzB,IACIwjB,EACAC,EACAC,EAHAC,EAAKL,GAAIM,UAAYL,GAAIK,UAAY,EAIrCvkB,GAAK,EACLoU,EAAI,GACJC,EAAI,GAMR,IAHA3T,GAAQ,GAAIC,GAAQ,IAGZwjB,EAAKF,GAAInD,KAAKpgB,MACd0jB,EAAKF,GAAIpD,KAAKngB,MACf0jB,EAAKD,EAAGpd,OAASsd,IACpBD,EAAK1jB,EAAEU,MAAMijB,EAAID,GACbjQ,EAAEpU,GAAIoU,EAAEpU,IAAMqkB,EACbjQ,IAAIpU,GAAKqkB,IAEXF,EAAKA,EAAG,OAASC,EAAKA,EAAG,IACxBhQ,EAAEpU,GAAIoU,EAAEpU,IAAMokB,EACbhQ,IAAIpU,GAAKokB,GAEdhQ,IAAIpU,GAAK,KACTqU,EAAE/N,KAAK,CAACtG,EAAGA,EAAGqK,EAAGqK,GAAOyP,EAAIC,MAE9BE,EAAKJ,GAAIK,UAYX,OARID,EAAK3jB,EAAEhC,SACT0lB,EAAK1jB,EAAEU,MAAMijB,GACTlQ,EAAEpU,GAAIoU,EAAEpU,IAAMqkB,EACbjQ,IAAIpU,GAAKqkB,GAKTjQ,EAAEzV,OAAS,EAAK0V,EAAE,GA7C3B,SAAa1T,GACX,OAAO,SAASoM,GACd,OAAOpM,EAAEoM,GAAK,EAChB,CACF,CA0CQyX,CAAInQ,EAAE,GAAGhK,GApDjB,SAAc1J,GACZ,OAAO,WACL,OAAOA,CACT,CACF,CAiDQ8jB,CAAK9jB,IACJA,EAAI0T,EAAE1V,OAAQ,SAASoO,GACtB,IAAK,IAAWpF,EAAP3H,EAAI,EAAMA,EAAIW,IAAKX,EAAGoU,GAAGzM,EAAI0M,EAAErU,IAAIA,GAAK2H,EAAE0C,EAAE0C,GACrD,OAAOqH,EAAE7N,KAAK,GAChB,EACR,CC5De,YAAS7F,EAAGC,GACzB,IAAI4O,EACJ,OAAqB,kBAAN5O,EAAiB+jB,GAC1B/jB,aAAa+f,GAAQiE,IACpBpV,EAAImR,GAAM/f,KAAOA,EAAI4O,EAAGoV,IACzBC,IAAmBlkB,EAAGC,EAC9B,CCJA,SAASe,GAAWR,GAClB,OAAO,WACL9C,KAAKuD,gBAAgBT,EACvB,CACF,CAEA,SAASU,GAAaC,GACpB,OAAO,WACLzD,KAAK0D,kBAAkBD,EAASL,MAAOK,EAASJ,MAClD,CACF,CAEA,SAASM,GAAab,EAAM2jB,EAAaC,GACvC,IAAIC,EAEAC,EADAC,EAAUH,EAAS,GAEvB,OAAO,WACL,IAAII,EAAU9mB,KAAK4F,aAAa9C,GAChC,OAAOgkB,IAAYD,EAAU,KACvBC,IAAYH,EAAWC,EACvBA,EAAeH,EAAYE,EAAWG,EAASJ,EACvD,CACF,CAEA,SAAS5iB,GAAeL,EAAUgjB,EAAaC,GAC7C,IAAIC,EAEAC,EADAC,EAAUH,EAAS,GAEvB,OAAO,WACL,IAAII,EAAU9mB,KAAKiO,eAAexK,EAASL,MAAOK,EAASJ,OAC3D,OAAOyjB,IAAYD,EAAU,KACvBC,IAAYH,EAAWC,EACvBA,EAAeH,EAAYE,EAAWG,EAASJ,EACvD,CACF,CAEA,SAAS1iB,GAAalB,EAAM2jB,EAAa7iB,GACvC,IAAI+iB,EACAI,EACAH,EACJ,OAAO,WACL,IAAIE,EAA+BD,EAAtBH,EAAS9iB,EAAM5D,MAC5B,GAAc,MAAV0mB,EAGJ,OAFAI,EAAU9mB,KAAK4F,aAAa9C,OAC5B+jB,EAAUH,EAAS,IACU,KACvBI,IAAYH,GAAYE,IAAYE,EAAWH,GAC9CG,EAAWF,EAASD,EAAeH,EAAYE,EAAWG,EAASJ,IAL1C1mB,KAAKuD,gBAAgBT,EAMvD,CACF,CAEA,SAASsB,GAAeX,EAAUgjB,EAAa7iB,GAC7C,IAAI+iB,EACAI,EACAH,EACJ,OAAO,WACL,IAAIE,EAA+BD,EAAtBH,EAAS9iB,EAAM5D,MAC5B,GAAc,MAAV0mB,EAGJ,OAFAI,EAAU9mB,KAAKiO,eAAexK,EAASL,MAAOK,EAASJ,WACvDwjB,EAAUH,EAAS,IACU,KACvBI,IAAYH,GAAYE,IAAYE,EAAWH,GAC9CG,EAAWF,EAASD,EAAeH,EAAYE,EAAWG,EAASJ,IAL1C1mB,KAAK0D,kBAAkBD,EAASL,MAAOK,EAASJ,MAMlF,CACF,CCvDA,SAAS2jB,GAAYvjB,EAAUG,GAC7B,IAAIsP,EAAI5G,EACR,SAAS8H,IACP,IAAIxS,EAAIgC,EAAMM,MAAMlE,KAAMmE,WAE1B,OADIvC,IAAM0K,IAAI4G,GAAM5G,EAAK1K,IAV7B,SAA2B6B,EAAU7B,GACnC,OAAO,SAAS+M,GACd3O,KAAK+D,eAAeN,EAASL,MAAOK,EAASJ,MAAOzB,EAAEQ,KAAKpC,KAAM2O,GACnE,CACF,CAMmCsY,CAAkBxjB,EAAU7B,IACpDsR,CACT,CAEA,OADAkB,EAAM8S,OAAStjB,EACRwQ,CACT,CAEA,SAAS+S,GAAUrkB,EAAMc,GACvB,IAAIsP,EAAI5G,EACR,SAAS8H,IACP,IAAIxS,EAAIgC,EAAMM,MAAMlE,KAAMmE,WAE1B,OADIvC,IAAM0K,IAAI4G,GAAM5G,EAAK1K,IA3B7B,SAAyBkB,EAAMlB,GAC7B,OAAO,SAAS+M,GACd3O,KAAK6D,aAAaf,EAAMlB,EAAEQ,KAAKpC,KAAM2O,GACvC,CACF,CAuBmCyY,CAAgBtkB,EAAMlB,IAC9CsR,CACT,CAEA,OADAkB,EAAM8S,OAAStjB,EACRwQ,CACT,CChCA,SAASiT,GAAc3nB,EAAIkE,GACzB,OAAO,WACL+Q,GAAK3U,KAAMN,GAAIiT,OAAS/O,EAAMM,MAAMlE,KAAMmE,UAC5C,CACF,CAEA,SAASmjB,GAAc5nB,EAAIkE,GACzB,OAAOA,GAASA,EAAO,WACrB+Q,GAAK3U,KAAMN,GAAIiT,MAAQ/O,CACzB,CACF,CCVA,SAAS2jB,GAAiB7nB,EAAIkE,GAC5B,OAAO,WACLwN,GAAIpR,KAAMN,GAAI+U,UAAY7Q,EAAMM,MAAMlE,KAAMmE,UAC9C,CACF,CAEA,SAASqjB,GAAiB9nB,EAAIkE,GAC5B,OAAOA,GAASA,EAAO,WACrBwN,GAAIpR,KAAMN,GAAI+U,SAAW7Q,CAC3B,CACF,CCVA,IAAIoH,GAAYK,GAAUrK,UAAUC,YCiBpC,SAASsD,GAAYzB,GACnB,OAAO,WACL9C,KAAKwE,MAAMC,eAAe3B,EAC5B,CACF,CCDA,IAAIpD,GAAK,EAEF,SAAS+nB,GAAWxc,EAAQC,EAASpI,EAAMpD,GAChDM,KAAKmL,QAAUF,EACfjL,KAAKoL,SAAWF,EAChBlL,KAAK0nB,MAAQ5kB,EACb9C,KAAKyX,IAAM/X,CACb,CAMO,SAASioB,KACd,QAASjoB,EACX,CAEA,IAAIkoB,GAAsBvc,GAAUrK,UAEpCymB,GAAWzmB,UAVI,SAAoB8B,GACjC,OAAOuI,KAAYmM,WAAW1U,EAChC,EAQkC9B,UAAY,CAC5CC,YAAawmB,GACbnc,OCvCa,SAASA,GACtB,IAAIxI,EAAO9C,KAAK0nB,MACZhoB,EAAKM,KAAKyX,IAEQ,oBAAXnM,IAAuBA,EAASvL,EAASuL,IAEpD,IAAK,IAAIL,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQgL,EAAY,IAAIjL,MAAMmJ,GAAID,EAAI,EAAGA,EAAIC,IAAKD,EAC3F,IAAK,IAAiF7H,EAAM6J,EAAnFjK,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAQkL,EAAWF,EAAU/B,GAAK,IAAIlJ,MAAM0F,GAAmBpE,EAAI,EAAGA,EAAIoE,IAAKpE,GAC9GD,EAAOJ,EAAMK,MAAQ4J,EAAUF,EAAOlJ,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,MAClE,aAAcI,IAAM6J,EAAQzK,SAAWY,EAAKZ,UAChD0K,EAAS7J,GAAK4J,EACd6I,GAAS5I,EAAS7J,GAAIkB,EAAMpD,EAAIkC,EAAG6J,EAAUyF,GAAIvP,EAAMjC,KAK7D,OAAO,IAAI+nB,GAAWlc,EAAWvL,KAAKoL,SAAUtI,EAAMpD,EACxD,EDuBEgM,UExCa,SAASJ,GACtB,IAAIxI,EAAO9C,KAAK0nB,MACZhoB,EAAKM,KAAKyX,IAEQ,oBAAXnM,IAAuBA,EAASK,EAAYL,IAEvD,IAAK,IAAIL,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQgL,EAAY,GAAIL,EAAU,GAAI1B,EAAI,EAAGA,EAAIC,IAAKD,EAC/F,IAAK,IAAyC7H,EAArCJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAcqB,EAAI,EAAGA,EAAIoE,IAAKpE,EAClE,GAAID,EAAOJ,EAAMK,GAAI,CACnB,IAAK,IAA2DT,EAAvD0mB,EAAWvc,EAAOlJ,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,GAAeumB,EAAU5W,GAAIvP,EAAMjC,GAAKmlB,EAAI,EAAGrC,EAAIqF,EAAStnB,OAAQskB,EAAIrC,IAAKqC,GAC/H1jB,EAAQ0mB,EAAShD,KACnBxQ,GAASlT,EAAO2B,EAAMpD,EAAImlB,EAAGgD,EAAUC,GAG3Cvc,EAAUrD,KAAK2f,GACf3c,EAAQhD,KAAKvG,EACf,CAIJ,OAAO,IAAI8lB,GAAWlc,EAAWL,EAASpI,EAAMpD,EAClD,EFoBEqoB,YAAaH,GAAoBG,YACjCC,eAAgBJ,GAAoBI,eACpCpc,OG5Ca,SAASC,GACD,oBAAVA,IAAsBA,EAAQC,EAAQD,IAEjD,IAAK,IAAIZ,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQgL,EAAY,IAAIjL,MAAMmJ,GAAID,EAAI,EAAGA,EAAIC,IAAKD,EAC3F,IAAK,IAAuE7H,EAAnEJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAQkL,EAAWF,EAAU/B,GAAK,GAAU5H,EAAI,EAAGA,EAAIoE,IAAKpE,GAC3FD,EAAOJ,EAAMK,KAAOiK,EAAMzJ,KAAKT,EAAMA,EAAKZ,SAAUa,EAAGL,IAC1DkK,EAASvD,KAAKvG,GAKpB,OAAO,IAAI8lB,GAAWlc,EAAWvL,KAAKoL,SAAUpL,KAAK0nB,MAAO1nB,KAAKyX,IACnE,EHiCEzK,MI9Ca,SAASwK,GACtB,GAAIA,EAAWC,MAAQzX,KAAKyX,IAAK,MAAM,IAAIzG,MAE3C,IAAK,IAAI9D,EAAUlN,KAAKmL,QAASgC,EAAUqK,EAAWrM,QAASiC,EAAKF,EAAQ3M,OAAQ8M,EAAKF,EAAQ5M,OAAQkJ,EAAIjK,KAAK8N,IAAIF,EAAIC,GAAKE,EAAS,IAAIjN,MAAM8M,GAAK5D,EAAI,EAAGA,EAAIC,IAAKD,EACrK,IAAK,IAAmG7H,EAA/F6L,EAASN,EAAQ1D,GAAIiE,EAASN,EAAQ3D,GAAIxD,EAAIwH,EAAOjN,OAAQyM,EAAQO,EAAO/D,GAAK,IAAIlJ,MAAM0F,GAAUpE,EAAI,EAAGA,EAAIoE,IAAKpE,GACxHD,EAAO6L,EAAO5L,IAAM6L,EAAO7L,MAC7BoL,EAAMpL,GAAKD,GAKjB,KAAO6H,EAAI4D,IAAM5D,EACf+D,EAAO/D,GAAK0D,EAAQ1D,GAGtB,OAAO,IAAIie,GAAWla,EAAQvN,KAAKoL,SAAUpL,KAAK0nB,MAAO1nB,KAAKyX,IAChE,EJ+BEpM,UF7Ca,WACb,OAAO,IAAIL,GAAUhL,KAAKmL,QAASnL,KAAKoL,SAC1C,EE4CEoM,WK/Ca,WAKb,IAJA,IAAI1U,EAAO9C,KAAK0nB,MACZO,EAAMjoB,KAAKyX,IACXyQ,EAAMP,KAED1c,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQiJ,EAAI,EAAGA,EAAIC,IAAKD,EACjE,IAAK,IAAyC7H,EAArCJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAcqB,EAAI,EAAGA,EAAIoE,IAAKpE,EAClE,GAAID,EAAOJ,EAAMK,GAAI,CACnB,IAAIkmB,EAAU5W,GAAIvP,EAAMsmB,GACxB5T,GAAS1S,EAAMmB,EAAMolB,EAAKtmB,EAAGL,EAAO,CAClCqR,KAAMkV,EAAQlV,KAAOkV,EAAQnV,MAAQmV,EAAQrT,SAC7C9B,MAAO,EACP8B,SAAUqT,EAAQrT,SAClBC,KAAMoT,EAAQpT,MAElB,CAIJ,OAAO,IAAI+S,GAAWxc,EAAQjL,KAAKoL,SAAUtI,EAAMolB,EACrD,EL4BE9lB,KAAMwlB,GAAoBxlB,KAC1B2L,MAAO6Z,GAAoB7Z,MAC3BpM,KAAMimB,GAAoBjmB,KAC1BoK,KAAM6b,GAAoB7b,KAC1B7L,MAAO0nB,GAAoB1nB,MAC3B8L,KAAM4b,GAAoB5b,KAC1B3C,GMhCa,SAASvG,EAAM6F,GAC5B,IAAIjJ,EAAKM,KAAKyX,IAEd,OAAOtT,UAAU5D,OAAS,EACpB2Q,GAAIlR,KAAK2B,OAAQjC,GAAI2J,GAAGA,GAAGvG,GAC3B9C,KAAKgM,KApBb,SAAoBtM,EAAIoD,EAAM6F,GAC5B,IAAIwf,EAAKC,EAAKC,EAThB,SAAevlB,GACb,OAAQA,EAAO,IAAIwC,OAAOC,MAAM,SAAS+iB,OAAM,SAAS3Z,GACtD,IAAI/M,EAAI+M,EAAE3L,QAAQ,KAElB,OADIpB,GAAK,IAAG+M,EAAIA,EAAE1L,MAAM,EAAGrB,KACnB+M,GAAW,UAANA,CACf,GACF,CAGsB4F,CAAMzR,GAAQ6R,GAAOvD,GACzC,OAAO,WACL,IAAIiD,EAAWgU,EAAIroB,KAAMN,GACrB2J,EAAKgL,EAAShL,GAKdA,IAAO8e,IAAMC,GAAOD,EAAM9e,GAAImI,QAAQnI,GAAGvG,EAAM6F,GAEnD0L,EAAShL,GAAK+e,CAChB,CACF,CAOkBG,CAAW7oB,EAAIoD,EAAM6F,GACvC,EN2BEqF,KNaa,SAASlL,EAAMc,GAC5B,IAAIH,EAAWkE,EAAU7E,GAAOlB,EAAiB,cAAb6B,EAA2BiS,GAAuB+Q,GACtF,OAAOzmB,KAAKmnB,UAAUrkB,EAAuB,oBAAVc,GAC5BH,EAASJ,MAAQe,GAAiBJ,IAAcP,EAAU7B,EAAG2V,GAAWvX,KAAM,QAAU8C,EAAMc,IACtF,MAATA,GAAiBH,EAASJ,MAAQG,GAAeF,IAAYG,IAC5DA,EAASJ,MAAQS,GAAiBH,IAAcF,EAAU7B,EAAGgC,GACtE,EMlBEujB,ULvBa,SAASrkB,EAAMc,GAC5B,IAAI5B,EAAM,QAAUc,EACpB,GAAIqB,UAAU5D,OAAS,EAAG,OAAQyB,EAAMhC,KAAKoU,MAAMpS,KAASA,EAAIklB,OAChE,GAAa,MAATtjB,EAAe,OAAO5D,KAAKoU,MAAMpS,EAAK,MAC1C,GAAqB,oBAAV4B,EAAsB,MAAM,IAAIoN,MAC3C,IAAIvN,EAAWkE,EAAU7E,GACzB,OAAO9C,KAAKoU,MAAMpS,GAAMyB,EAASJ,MAAQ2jB,GAAcG,IAAW1jB,EAAUG,GAC9E,EKiBEY,MDQa,SAAS1B,EAAMc,EAAOe,GACnC,IAAI/C,EAAqB,eAAhBkB,GAAQ,IAAsB4S,GAAuB+Q,GAC9D,OAAgB,MAAT7iB,EAAgB5D,KAClBwoB,WAAW1lB,EAjElB,SAAmBA,EAAM2jB,GACvB,IAAIE,EACAI,EACAH,EACJ,OAAO,WACL,IAAIE,EAAUtiB,EAAMxE,KAAM8C,GACtB+jB,GAAW7mB,KAAKwE,MAAMC,eAAe3B,GAAO0B,EAAMxE,KAAM8C,IAC5D,OAAOgkB,IAAYD,EAAU,KACvBC,IAAYH,GAAYE,IAAYE,EAAWH,EAC/CA,EAAeH,EAAYE,EAAWG,EAASC,EAAWF,EAClE,CACF,CAsDwB4B,CAAU3lB,EAAMlB,IACjCyH,GAAG,aAAevG,EAAMyB,GAAYzB,IACpB,oBAAVc,EAAuB5D,KAC7BwoB,WAAW1lB,EArClB,SAAuBA,EAAM2jB,EAAa7iB,GACxC,IAAI+iB,EACAI,EACAH,EACJ,OAAO,WACL,IAAIE,EAAUtiB,EAAMxE,KAAM8C,GACtB4jB,EAAS9iB,EAAM5D,MACf6mB,EAAUH,EAAS,GAEvB,OADc,MAAVA,IAAoC1mB,KAAKwE,MAAMC,eAAe3B,GAA9C+jB,EAAUH,EAA2CliB,EAAMxE,KAAM8C,IAC9EgkB,IAAYD,EAAU,KACvBC,IAAYH,GAAYE,IAAYE,EAAWH,GAC9CG,EAAWF,EAASD,EAAeH,EAAYE,EAAWG,EAASJ,GAC5E,CACF,CAwBwB7hB,CAAc/B,EAAMlB,EAAG2V,GAAWvX,KAAM,SAAW8C,EAAMc,KAC1EoI,KAvBP,SAA0BtM,EAAIoD,GAC5B,IAAIqlB,EAAKC,EAAKM,EAAwDviB,EAA7CnE,EAAM,SAAWc,EAAMyF,EAAQ,OAASvG,EACjE,OAAO,WACL,IAAIqS,EAAWjD,GAAIpR,KAAMN,GACrB2J,EAAKgL,EAAShL,GACdV,EAAkC,MAAvB0L,EAASzQ,MAAM5B,GAAemE,IAAWA,EAAS5B,GAAYzB,SAASkQ,EAKlF3J,IAAO8e,GAAOO,IAAc/f,IAAWyf,GAAOD,EAAM9e,GAAImI,QAAQnI,GAAGd,EAAOmgB,EAAY/f,GAE1F0L,EAAShL,GAAK+e,CAChB,CACF,CASYO,CAAiB3oB,KAAKyX,IAAK3U,IACjC9C,KACCwoB,WAAW1lB,EApDlB,SAAuBA,EAAM2jB,EAAaC,GACxC,IAAIC,EAEAC,EADAC,EAAUH,EAAS,GAEvB,OAAO,WACL,IAAII,EAAUtiB,EAAMxE,KAAM8C,GAC1B,OAAOgkB,IAAYD,EAAU,KACvBC,IAAYH,EAAWC,EACvBA,EAAeH,EAAYE,EAAWG,EAASJ,EACvD,CACF,CA0CwBhiB,CAAc5B,EAAMlB,EAAGgC,GAAQe,GAChD0E,GAAG,aAAevG,EAAM,KAC/B,EClBE0lB,WO5Ca,SAAS1lB,EAAMc,EAAOe,GACnC,IAAI3C,EAAM,UAAYc,GAAQ,IAC9B,GAAIqB,UAAU5D,OAAS,EAAG,OAAQyB,EAAMhC,KAAKoU,MAAMpS,KAASA,EAAIklB,OAChE,GAAa,MAATtjB,EAAe,OAAO5D,KAAKoU,MAAMpS,EAAK,MAC1C,GAAqB,oBAAV4B,EAAsB,MAAM,IAAIoN,MAC3C,OAAOhR,KAAKoU,MAAMpS,EAhBpB,SAAoBc,EAAMc,EAAOe,GAC/B,IAAIgK,EAAGrC,EACP,SAAS8H,IACP,IAAIxS,EAAIgC,EAAMM,MAAMlE,KAAMmE,WAE1B,OADIvC,IAAM0K,IAAIqC,GAAKrC,EAAK1K,IAV5B,SAA0BkB,EAAMlB,EAAG+C,GACjC,OAAO,SAASgK,GACd3O,KAAKwE,MAAMI,YAAY9B,EAAMlB,EAAEQ,KAAKpC,KAAM2O,GAAIhK,EAChD,CACF,CAMkCikB,CAAiB9lB,EAAMlB,EAAG+C,IACjDgK,CACT,CAEA,OADAyF,EAAM8S,OAAStjB,EACRwQ,CACT,CAOyBoU,CAAW1lB,EAAMc,EAAmB,MAAZe,EAAmB,GAAKA,GACzE,EPuCEyJ,KQ/Ca,SAASxK,GACtB,OAAO5D,KAAKoU,MAAM,OAAyB,oBAAVxQ,EARnC,SAAsBA,GACpB,OAAO,WACL,IAAI8iB,EAAS9iB,EAAM5D,MACnBA,KAAKwG,YAAwB,MAAVkgB,EAAiB,GAAKA,CAC3C,CACF,CAIQhgB,CAAa6Q,GAAWvX,KAAM,OAAQ4D,IAf9C,SAAsBA,GACpB,OAAO,WACL5D,KAAKwG,YAAc5C,CACrB,CACF,CAYQ6C,CAAsB,MAAT7C,EAAgB,GAAKA,EAAQ,IAClD,ER4CEilB,US9Ca,SAASjlB,GACtB,IAAI5B,EAAM,OACV,GAAImC,UAAU5D,OAAS,EAAG,OAAQyB,EAAMhC,KAAKoU,MAAMpS,KAASA,EAAIklB,OAChE,GAAa,MAATtjB,EAAe,OAAO5D,KAAKoU,MAAMpS,EAAK,MAC1C,GAAqB,oBAAV4B,EAAsB,MAAM,IAAIoN,MAC3C,OAAOhR,KAAKoU,MAAMpS,EAhBpB,SAAmB4B,GACjB,IAAIsP,EAAI5G,EACR,SAAS8H,IACP,IAAIxS,EAAIgC,EAAMM,MAAMlE,KAAMmE,WAE1B,OADIvC,IAAM0K,IAAI4G,GAAM5G,EAAK1K,IAV7B,SAAyBA,GACvB,OAAO,SAAS+M,GACd3O,KAAKwG,YAAc5E,EAAEQ,KAAKpC,KAAM2O,EAClC,CACF,CAMmCma,CAAgBlnB,IACxCsR,CACT,CAEA,OADAkB,EAAM8S,OAAStjB,EACRwQ,CACT,CAOyByU,CAAUjlB,GACnC,ETyCEuC,OUxDa,WACb,OAAOnG,KAAKqJ,GAAG,aATjB,SAAwB3J,GACtB,OAAO,WACL,IAAIe,EAAST,KAAKiH,WAClB,IAAK,IAAIrF,KAAK5B,KAAKkU,aAAc,IAAKtS,IAAMlC,EAAI,OAC5Ce,GAAQA,EAAOoH,YAAY7H,KACjC,CACF,CAG+B+oB,CAAe/oB,KAAKyX,KACnD,EVuDErD,MhBda,SAAStR,EAAMc,GAC5B,IAAIlE,EAAKM,KAAKyX,IAId,GAFA3U,GAAQ,GAEJqB,UAAU5D,OAAS,EAAG,CAExB,IADA,IACkCoO,EAD9ByF,EAAQlD,GAAIlR,KAAK2B,OAAQjC,GAAI0U,MACxBxS,EAAI,EAAGoE,EAAIoO,EAAM7T,OAAWqB,EAAIoE,IAAKpE,EAC5C,IAAK+M,EAAIyF,EAAMxS,IAAIkB,OAASA,EAC1B,OAAO6L,EAAE/K,MAGb,OAAO,IACT,CAEA,OAAO5D,KAAKgM,MAAe,MAATpI,EAAgBuT,GAAcG,IAAe5X,EAAIoD,EAAMc,GAC3E,EgBDE+O,MJpDa,SAAS/O,GACtB,IAAIlE,EAAKM,KAAKyX,IAEd,OAAOtT,UAAU5D,OACXP,KAAKgM,MAAuB,oBAAVpI,EACdyjB,GACAC,IAAe5nB,EAAIkE,IACvBsN,GAAIlR,KAAK2B,OAAQjC,GAAIiT,KAC7B,EI6CE8B,SHrDa,SAAS7Q,GACtB,IAAIlE,EAAKM,KAAKyX,IAEd,OAAOtT,UAAU5D,OACXP,KAAKgM,MAAuB,oBAAVpI,EACd2jB,GACAC,IAAkB9nB,EAAIkE,IAC1BsN,GAAIlR,KAAK2B,OAAQjC,GAAI+U,QAC7B,EG8CEC,KW3Da,SAAS9Q,GACtB,IAAIlE,EAAKM,KAAKyX,IAEd,OAAOtT,UAAU5D,OACXP,KAAKgM,KAXb,SAAsBtM,EAAIkE,GACxB,GAAqB,oBAAVA,EAAsB,MAAM,IAAIoN,MAC3C,OAAO,WACLI,GAAIpR,KAAMN,GAAIgV,KAAO9Q,CACvB,CACF,CAMkBolB,CAAatpB,EAAIkE,IAC3BsN,GAAIlR,KAAK2B,OAAQjC,GAAIgV,IAC7B,EXsDEuU,YY3Da,SAASrlB,GACtB,GAAqB,oBAAVA,EAAsB,MAAM,IAAIoN,MAC3C,OAAOhR,KAAKgM,KAVd,SAAqBtM,EAAIkE,GACvB,OAAO,WACL,IAAIK,EAAIL,EAAMM,MAAMlE,KAAMmE,WAC1B,GAAiB,oBAANF,EAAkB,MAAM,IAAI+M,MACvCI,GAAIpR,KAAMN,GAAIgV,KAAOzQ,CACvB,CACF,CAImBglB,CAAYjpB,KAAKyX,IAAK7T,GACzC,EZyDE0hB,IapEa,WACb,IAAI6C,EAAKC,EAAKle,EAAOlK,KAAMN,EAAKwK,EAAKuN,IAAK1L,EAAO7B,EAAK6B,OACtD,OAAO,IAAImd,SAAQ,SAASC,EAASC,GACnC,IAAIC,EAAS,CAACzlB,MAAOwlB,GACjB9D,EAAM,CAAC1hB,MAAO,WAA4B,MAATmI,GAAYod,GAAW,GAE5Djf,EAAK8B,MAAK,WACR,IAAIqI,EAAWjD,GAAIpR,KAAMN,GACrB2J,EAAKgL,EAAShL,GAKdA,IAAO8e,KACTC,GAAOD,EAAM9e,GAAImI,QACbV,EAAEuY,OAAOnhB,KAAKmhB,GAClBjB,EAAItX,EAAEwY,UAAUphB,KAAKmhB,GACrBjB,EAAItX,EAAEwU,IAAIpd,KAAKod,IAGjBjR,EAAShL,GAAK+e,CAChB,IAGa,IAATrc,GAAYod,GAClB,GACF,Eb2CE,CAACI,OAAOC,UAAW5B,GAAoB2B,OAAOC,WclEhD,IAAIC,GAAgB,CAClB7W,KAAM,KACND,MAAO,EACP8B,SAAU,IACVC,KCDK,SAAoB/F,GACzB,QAASA,GAAK,IAAM,EAAIA,EAAIA,EAAIA,GAAKA,GAAK,GAAKA,EAAIA,EAAI,GAAK,CAC9D,GDEA,SAASmZ,GAAQnmB,EAAMjC,GAErB,IADA,IAAIsU,IACKA,EAASrS,EAAKuS,iBAAmBF,EAASA,EAAOtU,KACxD,KAAMiC,EAAOA,EAAKsF,YAChB,MAAM,IAAI+J,MAAM,cAActR,eAGlC,OAAOsU,CACT,CEpBe,YAAS/H,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CCJe,YAASyd,EAAQhgB,EAAM2B,GACpCrL,KAAK0pB,OAASA,EACd1pB,KAAK0J,KAAOA,EACZ1J,KAAKqL,UAAYA,CACnB,CCFO,SAAS4E,KACd1H,GAAM2H,0BACR,CAEe,cACb3H,GAAM4H,iBACN5H,GAAM2H,0BACR,CCLA7E,GAAUrK,UAAUsoB,UCFL,SAASxmB,GACtB,OAAO9C,KAAKgM,MAAK,WACfsd,GAAUtpB,KAAM8C,EAClB,GACF,EDDAuI,GAAUrK,UAAUwW,WLiBL,SAAS1U,GACtB,IAAIpD,EACAsU,EAEAlR,aAAgB2kB,IAClB/nB,EAAKoD,EAAK2U,IAAK3U,EAAOA,EAAK4kB,QAE3BhoB,EAAKioB,MAAU3T,EAASyV,IAAe7W,KAAOT,KAAOrP,EAAe,MAARA,EAAe,KAAOA,EAAO,IAG3F,IAAK,IAAImI,EAASjL,KAAKmL,QAAS1B,EAAIwB,EAAO1K,OAAQiJ,EAAI,EAAGA,EAAIC,IAAKD,EACjE,IAAK,IAAyC7H,EAArCJ,EAAQ0J,EAAOzB,GAAIxD,EAAIzE,EAAMhB,OAAcqB,EAAI,EAAGA,EAAIoE,IAAKpE,GAC9DD,EAAOJ,EAAMK,KACfyS,GAAS1S,EAAMmB,EAAMpD,EAAIkC,EAAGL,EAAOyS,GAAU8T,GAAQnmB,EAAMjC,IAKjE,OAAO,IAAI+nB,GAAWxc,EAAQjL,KAAKoL,SAAUtI,EAAMpD,EACrD,EOhCA,IAAIiqB,GAAY,CAAC7mB,KAAM,QACnB8mB,GAAa,CAAC9mB,KAAM,SACpB+mB,GAAc,CAAC/mB,KAAM,UACrBgnB,GAAc,CAAChnB,KAAM,UAEzB,SAASinB,GAAQhX,GACf,MAAO,EAAEA,EAAE,IAAKA,EAAE,GACpB,CAEA,SAASiX,GAAQjX,GACf,MAAO,CAACgX,GAAQhX,EAAE,IAAKgX,GAAQhX,EAAE,IACnC,CAQA,IAAIkX,GAAI,CACNnnB,KAAM,IACNonB,QAAS,CAAC,IAAK,KAAKxd,IAAIhD,IACxBygB,MAAO,SAASle,EAAG8G,GAAK,OAAY,MAAL9G,EAAY,KAAO,CAAC,EAAEA,EAAE,GAAI8G,EAAE,GAAG,IAAK,EAAE9G,EAAE,GAAI8G,EAAE,GAAG,IAAM,EACxFqX,OAAQ,SAASC,GAAM,OAAOA,GAAM,CAACA,EAAG,GAAG,GAAIA,EAAG,GAAG,GAAK,GAGxDC,GAAI,CACNxnB,KAAM,IACNonB,QAAS,CAAC,IAAK,KAAKxd,IAAIhD,IACxBygB,MAAO,SAAS9a,EAAG0D,GAAK,OAAY,MAAL1D,EAAY,KAAO,CAAC,CAAC0D,EAAE,GAAG,IAAK1D,EAAE,IAAK,CAAC0D,EAAE,GAAG,IAAK1D,EAAE,IAAM,EACxF+a,OAAQ,SAASC,GAAM,OAAOA,GAAM,CAACA,EAAG,GAAG,GAAIA,EAAG,GAAG,GAAK,GAUxDE,IALO,CAAC,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,MAAM7d,IAAIhD,IAK9C,CACZ8gB,QAAS,YACTnf,UAAW,OACXrF,EAAG,YACH+M,EAAG,YACHiD,EAAG,YACHyU,EAAG,YACHC,GAAI,cACJC,GAAI,cACJC,GAAI,cACJC,GAAI,gBAGFC,GAAQ,CACV/X,EAAG,IACH0X,EAAG,IACHC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,MAGFE,GAAQ,CACV/kB,EAAG,IACHgQ,EAAG,IACH0U,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,MAGFG,GAAS,CACXR,QAAS,EACTnf,UAAW,EACXrF,EAAG,KACH+M,EAAG,EACHiD,EAAG,KACHyU,GAAI,EACJC,IAAK,EACLC,GAAI,EACJC,GAAI,EACJC,IAAK,GAGHI,GAAS,CACXT,QAAS,EACTnf,UAAW,EACXrF,GAAI,EACJ+M,EAAG,KACHiD,EAAG,EACHyU,EAAG,KACHC,IAAK,EACLC,IAAK,EACLC,GAAI,EACJC,GAAI,GAGN,SAASnhB,GAAKiF,GACZ,MAAO,CAACjF,KAAMiF,EAChB,CAGA,SAASuc,KACP,OAAQ3iB,GAAM4iB,UAAY5iB,GAAM6iB,MAClC,CAEA,SAASC,KACP,IAAI3oB,EAAM1C,KAAKiP,iBAAmBjP,KAClC,OAAI0C,EAAI4oB,aAAa,WAEZ,CAAC,EADR5oB,EAAMA,EAAI6oB,QAAQvU,SACL/K,EAAGvJ,EAAI2M,GAAI,CAAC3M,EAAIuJ,EAAIvJ,EAAI8oB,MAAO9oB,EAAI2M,EAAI3M,EAAI+oB,SAEnD,CAAC,CAAC,EAAG,GAAI,CAAC/oB,EAAI8oB,MAAMxU,QAAQpT,MAAOlB,EAAI+oB,OAAOzU,QAAQpT,OAC/D,CAEA,SAAS8nB,KACP,OAAOC,UAAUC,gBAAmB,iBAAkB5rB,IACxD,CAGA,SAASqD,GAAM1B,GACb,MAAQA,EAAKkqB,cAAelqB,EAAOA,EAAKsF,YAAa,OACrD,OAAOtF,EAAKkqB,OACd,CAOO,SAASC,GAAenqB,GAC7B,IAAI2S,EAAQ3S,EAAKkqB,QACjB,OAAOvX,EAAQA,EAAMyX,IAAI3B,OAAO9V,EAAMjJ,WAAa,IACrD,CAMO,SAAS2gB,KACd,OAAOC,GAAM3B,GACf,CAMA,SAAS2B,GAAMF,GACb,IAMIG,EANAC,EAASd,GACTzf,EAASsf,GACTkB,EAAYV,GACZW,GAAO,EACPC,GAAYxd,EAAAA,GAAAA,GAAS,QAAS,QAAS,OACvCyd,EAAa,EAGjB,SAASN,EAAM1qB,GACb,IAAIipB,EAAUjpB,EACT2M,SAAS,UAAWse,GACtB9gB,UAAU,YACVhK,KAAK,CAACgI,GAAK,aAEd8gB,EAAQhpB,QAAQuL,OAAO,QAClBiB,KAAK,QAAS,WACdA,KAAK,iBAAkB,OACvBA,KAAK,SAAUuc,GAAQC,SACzBxd,MAAMwd,GACJxe,MAAK,WACJ,IAAImgB,EAAS9oB,GAAMrD,MAAMmsB,OACzB7gB,GAAOtL,MACFgO,KAAK,IAAKme,EAAO,GAAG,IACpBne,KAAK,IAAKme,EAAO,GAAG,IACpBne,KAAK,QAASme,EAAO,GAAG,GAAKA,EAAO,GAAG,IACvCne,KAAK,SAAUme,EAAO,GAAG,GAAKA,EAAO,GAAG,GAC/C,IAEJ5qB,EAAMmK,UAAU,cACbhK,KAAK,CAACgI,GAAK,eACXlI,QAAQuL,OAAO,QACbiB,KAAK,QAAS,aACdA,KAAK,SAAUuc,GAAQlf,WACvB2C,KAAK,OAAQ,QACbA,KAAK,eAAgB,IACrBA,KAAK,SAAU,QACfA,KAAK,kBAAmB,cAE7B,IAAIye,EAASlrB,EAAMmK,UAAU,WAC1BhK,KAAKqqB,EAAI7B,SAAS,SAASngB,GAAK,OAAOA,EAAEL,IAAM,IAElD+iB,EAAOhrB,OAAO0E,SAEdsmB,EAAOjrB,QAAQuL,OAAO,QACjBiB,KAAK,SAAS,SAASjE,GAAK,MAAO,kBAAoBA,EAAEL,IAAM,IAC/DsE,KAAK,UAAU,SAASjE,GAAK,OAAOwgB,GAAQxgB,EAAEL,KAAO,IAE1DnI,EACKyK,KAAK0gB,GACL1e,KAAK,OAAQ,QACbA,KAAK,iBAAkB,OACvB3E,GAAG,kBAAmBsjB,GACxB/gB,OAAOwgB,GACL/iB,GAAG,mBAAoBsjB,GACvBtjB,GAAG,kBAAmBujB,GACtBvjB,GAAG,mCAAoCwjB,GACvCroB,MAAM,eAAgB,QACtBA,MAAM,8BAA+B,gBAC5C,CA4CA,SAASkoB,IACP,IAAInrB,EAAQ+J,GAAOtL,MACfqL,EAAYhI,GAAMrD,MAAMqL,UAExBA,GACF9J,EAAMmK,UAAU,cACXlH,MAAM,UAAW,MACjBwJ,KAAK,IAAK3C,EAAU,GAAG,IACvB2C,KAAK,IAAK3C,EAAU,GAAG,IACvB2C,KAAK,QAAS3C,EAAU,GAAG,GAAKA,EAAU,GAAG,IAC7C2C,KAAK,SAAU3C,EAAU,GAAG,GAAKA,EAAU,GAAG,IAEnD9J,EAAMmK,UAAU,WACXlH,MAAM,UAAW,MACjBwJ,KAAK,KAAK,SAASjE,GAAK,MAAqC,MAA9BA,EAAEL,KAAKK,EAAEL,KAAKnJ,OAAS,GAAa8K,EAAU,GAAG,GAAKkhB,EAAa,EAAIlhB,EAAU,GAAG,GAAKkhB,EAAa,CAAG,IACxIve,KAAK,KAAK,SAASjE,GAAK,MAAqB,MAAdA,EAAEL,KAAK,GAAa2B,EAAU,GAAG,GAAKkhB,EAAa,EAAIlhB,EAAU,GAAG,GAAKkhB,EAAa,CAAG,IACxHve,KAAK,SAAS,SAASjE,GAAK,MAAkB,MAAXA,EAAEL,MAA2B,MAAXK,EAAEL,KAAe2B,EAAU,GAAG,GAAKA,EAAU,GAAG,GAAKkhB,EAAaA,CAAY,IACnIve,KAAK,UAAU,SAASjE,GAAK,MAAkB,MAAXA,EAAEL,MAA2B,MAAXK,EAAEL,KAAe2B,EAAU,GAAG,GAAKA,EAAU,GAAG,GAAKkhB,EAAaA,CAAY,KAIzIhrB,EAAMmK,UAAU,sBACXlH,MAAM,UAAW,QACjBwJ,KAAK,IAAK,MACVA,KAAK,IAAK,MACVA,KAAK,QAAS,MACdA,KAAK,SAAU,KAExB,CAEA,SAAS8e,EAAQ5iB,EAAMC,EAAM4iB,GAC3B,IAAIC,EAAO9iB,EAAK2hB,QAAQiB,QACxB,OAAOE,GAAUD,GAAUC,EAAKD,MAAgB,IAAIE,EAAQ/iB,EAAMC,EAAM4iB,GAA/BC,CAC3C,CAEA,SAASC,EAAQ/iB,EAAMC,EAAM4iB,GAC3B/sB,KAAKkK,KAAOA,EACZlK,KAAKmK,KAAOA,EACZnK,KAAKsU,MAAQpK,EAAK2hB,QAClB7rB,KAAK4U,OAAS,EACd5U,KAAK+sB,MAAQA,CACf,CAyBA,SAASJ,IACP,KAAIT,GAAgB3jB,GAAMmI,UACrB9E,EAAO1H,MAAMlE,KAAMmE,WAAxB,CAEA,IAQsB+oB,EAAIC,EACJC,EAAIC,EACJC,EAAIC,EACJC,EAAIC,EAGtBC,EAEAC,EACAC,EApUSjd,EAmTTzG,EAAOlK,KACP0J,EAAOnB,GAAMmhB,OAAO3oB,SAAS2I,KAC7BmkB,EAA6D,eAArDxB,GAAQ9jB,GAAMulB,QAAUpkB,EAAO,UAAYA,GAAwBigB,GAAa0C,GAAQ9jB,GAAMwlB,OAASjE,GAAcD,GAC7HmE,EAAQjC,IAAQzB,GAAI,KAAOU,GAAOthB,GAClCukB,EAAQlC,IAAQ9B,GAAI,KAAOgB,GAAOvhB,GAClC4K,EAAQjR,GAAM6G,GACdiiB,EAAS7X,EAAM6X,OACf9gB,EAAYiJ,EAAMjJ,UAClB6iB,EAAI/B,EAAO,GAAG,GACdgC,EAAIhC,EAAO,GAAG,GACdiC,EAAIjC,EAAO,GAAG,GACdkC,EAAIlC,EAAO,GAAG,GACdmC,EAAK,EACLC,EAAK,EAELC,EAAWR,GAASC,GAAS5B,GAAQ9jB,GAAMkmB,SAG3CC,EAAUnmB,GAAMmI,SArUPC,EAqUyBpI,GAAMyH,eAAe,GAAGW,WApUzD,SAAS+Y,GACd,OAAO9Y,GAAM8Y,EAAQnhB,GAAMmI,QAASC,EACtC,GAkU8Ege,GACxEC,EAASF,EAAQxkB,GACjBiF,EAAQyf,EACR5B,EAAOF,EAAQ5iB,EAAM/F,WAAW,GAAM0qB,cAE7B,YAATnlB,GACE2B,IAAWqiB,GAAS,GACxBpZ,EAAMjJ,UAAYA,EAAY,CAC5B,CAAC6hB,EAAKnB,IAAQzB,GAAI4D,EAAIU,EAAO,GAAIxB,EAAKrB,IAAQ9B,GAAIkE,EAAIS,EAAO,IAC7D,CAACtB,EAAKvB,IAAQzB,GAAI8D,EAAIlB,EAAIM,EAAKzB,IAAQ9B,GAAIoE,EAAIjB,MAGjDF,EAAK7hB,EAAU,GAAG,GAClB+hB,EAAK/hB,EAAU,GAAG,GAClBiiB,EAAKjiB,EAAU,GAAG,GAClBmiB,EAAKniB,EAAU,GAAG,IAGpB8hB,EAAKD,EACLG,EAAKD,EACLG,EAAKD,EACLG,EAAKD,EAEL,IAAIjsB,EAAQ+J,GAAOpB,GACd8D,KAAK,iBAAkB,QAExBwc,EAAUjpB,EAAMmK,UAAU,YACzBsC,KAAK,SAAUuc,GAAQ7gB,IAE5B,GAAInB,GAAMmI,QACRsc,EAAK8B,MAAQA,EACb9B,EAAK+B,MAAQA,MACR,CACL,IAAI3e,EAAO9E,GAAO/C,GAAM6H,MACnB/G,GAAG,kBAAmBylB,GAAO,GAC7BzlB,GAAG,gBAAiB0lB,GAAO,GAC5B1C,GAAMjc,EACL/G,GAAG,iBA+FV,WACE,OAAQd,GAAMymB,SACZ,KAAK,GACHR,EAAWR,GAASC,EACpB,MAEF,KAAK,GACCJ,IAAShE,KACPmE,IAAOV,EAAKC,EAAKe,EAAKN,EAAOd,EAAKC,EAAKmB,EAAKN,GAC5CC,IAAOT,EAAKC,EAAKc,EAAKN,EAAOb,EAAKC,EAAKkB,EAAKN,GAChDJ,EAAO/D,GACPmF,KAEF,MAEF,KAAK,GACCpB,IAAShE,IAAegE,IAAS/D,KAC/BkE,EAAQ,EAAGV,EAAKC,EAAKe,EAAaN,EAAQ,IAAGd,EAAKC,EAAKmB,GACvDL,EAAQ,EAAGT,EAAKC,EAAKc,EAAaN,EAAQ,IAAGb,EAAKC,EAAKkB,GAC3DV,EAAOjE,GACPY,EAAQxc,KAAK,SAAUuc,GAAQlf,WAC/B4jB,KAEF,MAEF,QAAS,OAEX5e,IACF,IA3HsC,GAC/BhH,GAAG,eA4HV,WACE,OAAQd,GAAMymB,SACZ,KAAK,GACCR,IACFb,EAAQC,EAAQY,GAAW,EAC3BS,KAEF,MAEF,KAAK,GACCpB,IAAS/D,KACPkE,EAAQ,EAAGV,EAAKC,EAAaS,EAAQ,IAAGd,EAAKC,GAC7Cc,EAAQ,EAAGT,EAAKC,EAAaQ,EAAQ,IAAGb,EAAKC,GACjDQ,EAAOhE,GACPoF,KAEF,MAEF,KAAK,GACCpB,IAASjE,KACPrhB,GAAMwlB,QACJC,IAAOV,EAAKC,EAAKe,EAAKN,EAAOd,EAAKC,EAAKmB,EAAKN,GAC5CC,IAAOT,EAAKC,EAAKc,EAAKN,EAAOb,EAAKC,EAAKkB,EAAKN,GAChDJ,EAAO/D,KAEHkE,EAAQ,EAAGV,EAAKC,EAAaS,EAAQ,IAAGd,EAAKC,GAC7Cc,EAAQ,EAAGT,EAAKC,EAAaQ,EAAQ,IAAGb,EAAKC,GACjDQ,EAAOhE,IAETW,EAAQxc,KAAK,SAAUuc,GAAQ7gB,IAC/BulB,KAEF,MAEF,QAAS,OAEX5e,IACF,IAjKmC,GAEjC6e,GAAY3mB,GAAM6H,KACpB,CAEAH,KACAqZ,GAAUpf,GACVwiB,EAAOtqB,KAAK8H,GACZ8iB,EAAKzY,OAlEqC,CAoE1C,SAASua,IACP,IAAIK,EAAST,EAAQxkB,IACjBskB,GAAab,GAAUC,IACrBpuB,KAAK4vB,IAAID,EAAO,GAAKhgB,EAAM,IAAM3P,KAAK4vB,IAAID,EAAO,GAAKhgB,EAAM,IAAKye,GAAQ,EACxED,GAAQ,GAEfxe,EAAQggB,EACRzB,GAAS,EACTrd,KACA4e,GACF,CAEA,SAASA,IACP,IAAItgB,EAKJ,OAHA2f,EAAKnf,EAAM,GAAKyf,EAAO,GACvBL,EAAKpf,EAAM,GAAKyf,EAAO,GAEff,GACN,KAAKjE,GACL,KAAKD,GACCqE,IAAOM,EAAK9uB,KAAKC,IAAIyuB,EAAIhB,EAAI1tB,KAAK8N,IAAI8gB,EAAId,EAAIgB,IAAMnB,EAAKD,EAAKoB,EAAIf,EAAKD,EAAKgB,GAC5EL,IAAOM,EAAK/uB,KAAKC,IAAI0uB,EAAIf,EAAI5tB,KAAK8N,IAAI+gB,EAAIb,EAAIe,IAAMlB,EAAKD,EAAKmB,EAAId,EAAKD,EAAKe,GAChF,MAEF,KAAK1E,GACCmE,EAAQ,GAAGM,EAAK9uB,KAAKC,IAAIyuB,EAAIhB,EAAI1tB,KAAK8N,IAAI8gB,EAAIlB,EAAIoB,IAAMnB,EAAKD,EAAKoB,EAAIf,EAAKD,GACtEU,EAAQ,IAAGM,EAAK9uB,KAAKC,IAAIyuB,EAAIZ,EAAI9tB,KAAK8N,IAAI8gB,EAAId,EAAIgB,IAAMnB,EAAKD,EAAIK,EAAKD,EAAKgB,GAChFL,EAAQ,GAAGM,EAAK/uB,KAAKC,IAAI0uB,EAAIf,EAAI5tB,KAAK8N,IAAI+gB,EAAIjB,EAAImB,IAAMlB,EAAKD,EAAKmB,EAAId,EAAKD,GACtES,EAAQ,IAAGM,EAAK/uB,KAAKC,IAAI0uB,EAAIX,EAAIhuB,KAAK8N,IAAI+gB,EAAIb,EAAIe,IAAMlB,EAAKD,EAAIK,EAAKD,EAAKe,GACpF,MAEF,KAAKzE,GACCkE,IAAOb,EAAK3tB,KAAKC,IAAIyuB,EAAG1uB,KAAK8N,IAAI8gB,EAAGlB,EAAKoB,EAAKN,IAAST,EAAK/tB,KAAKC,IAAIyuB,EAAG1uB,KAAK8N,IAAI8gB,EAAGd,EAAKgB,EAAKN,KAC9FC,IAAOZ,EAAK7tB,KAAKC,IAAI0uB,EAAG3uB,KAAK8N,IAAI+gB,EAAGjB,EAAKmB,EAAKN,IAASR,EAAKjuB,KAAKC,IAAI0uB,EAAG3uB,KAAK8N,IAAI+gB,EAAGb,EAAKe,EAAKN,KAKlGV,EAAKJ,IACPa,IAAU,EACVrf,EAAIue,EAAIA,EAAKI,EAAIA,EAAK3e,EACtBA,EAAIwe,EAAIA,EAAKI,EAAIA,EAAK5e,EAClBjF,KAAQohB,IAAON,EAAQxc,KAAK,SAAUuc,GAAQ7gB,EAAOohB,GAAMphB,MAG7D+jB,EAAKJ,IACPY,IAAU,EACVtf,EAAIye,EAAIA,EAAKI,EAAIA,EAAK7e,EACtBA,EAAI0e,EAAIA,EAAKI,EAAIA,EAAK9e,EAClBjF,KAAQqhB,IAAOP,EAAQxc,KAAK,SAAUuc,GAAQ7gB,EAAOqhB,GAAMrhB,MAG7D4K,EAAMjJ,YAAWA,EAAYiJ,EAAMjJ,WACnCsiB,IAAOR,EAAK9hB,EAAU,GAAG,GAAIkiB,EAAKliB,EAAU,GAAG,IAC/CuiB,IAAOP,EAAKhiB,EAAU,GAAG,GAAIoiB,EAAKpiB,EAAU,GAAG,IAE/CA,EAAU,GAAG,KAAO8hB,GACjB9hB,EAAU,GAAG,KAAOgiB,GACpBhiB,EAAU,GAAG,KAAOkiB,GACpBliB,EAAU,GAAG,KAAOoiB,IACzBnZ,EAAMjJ,UAAY,CAAC,CAAC8hB,EAAIE,GAAK,CAACE,EAAIE,IAClCf,EAAOtqB,KAAK8H,GACZ8iB,EAAKf,QAET,CAEA,SAAS8C,IAEP,GADA9e,KACI1H,GAAMmI,QAAS,CACjB,GAAInI,GAAMmI,QAAQnQ,OAAQ,OACtB2rB,GAAatsB,aAAassB,GAC9BA,EAAcvsB,YAAW,WAAausB,EAAc,IAAM,GAAG,IAC/D,MACEmD,GAAW9mB,GAAM6H,KAAMsd,GACvBtd,EAAK/G,GAAG,0DAA2D,MAErE9H,EAAMyM,KAAK,iBAAkB,OAC7Bwc,EAAQxc,KAAK,SAAUuc,GAAQC,SAC3BlW,EAAMjJ,YAAWA,EAAYiJ,EAAMjJ,WApV7C,SAAe8gB,GACb,OAAOA,EAAO,GAAG,KAAOA,EAAO,GAAG,IAC3BA,EAAO,GAAG,KAAOA,EAAO,GAAG,EACpC,CAkVUjsB,CAAMmL,KAAYiJ,EAAMjJ,UAAY,KAAMqhB,EAAOtqB,KAAK8H,IAC1D8iB,EAAK1H,KACP,CAsEF,CAEA,SAASsH,IACPE,EAAQ9sB,KAAMmE,WAAW2qB,OAC3B,CAEA,SAASjC,IACPC,EAAQ9sB,KAAMmE,WAAW4qB,OAC3B,CAEA,SAASvC,IACP,IAAIlY,EAAQtU,KAAK6rB,SAAW,CAACxgB,UAAW,MAGxC,OAFAiJ,EAAM6X,OAASnC,GAAQmC,EAAOjoB,MAAMlE,KAAMmE,YAC1CmQ,EAAMyX,IAAMA,EACLzX,CACT,CA2BA,OApXA2X,EAAMgD,KAAO,SAAS1tB,EAAO8J,GACvB9J,EAAM8J,UACR9J,EACK8H,GAAG,eAAe,WAAayjB,EAAQ9sB,KAAMmE,WAAW0qB,cAActa,OAAS,IAC/ElL,GAAG,6BAA6B,WAAayjB,EAAQ9sB,KAAMmE,WAAWmhB,KAAO,IAC7ElR,MAAM,SAAS,WACd,IAAIlK,EAAOlK,KACPsU,EAAQpK,EAAK2hB,QACbmB,EAAOF,EAAQ5iB,EAAM/F,WACrBmrB,EAAahb,EAAMjJ,UACnBkkB,EAAaxD,EAAI5B,MAA2B,oBAAd9e,EAA2BA,EAAUnH,MAAMlE,KAAMmE,WAAakH,EAAWiJ,EAAM6X,QAC7GvqB,GAAI6kB,EAAAA,GAAAA,GAAY6I,EAAYC,GAEhC,SAASnb,EAAMzF,GACb2F,EAAMjJ,UAAkB,IAANsD,GAA0B,OAAf4gB,EAAsB,KAAO3tB,EAAE+M,GAC5D+d,EAAOtqB,KAAK8H,GACZ8iB,EAAKf,OACP,CAEA,OAAsB,OAAfqD,GAAsC,OAAfC,EAAsBnb,EAAQA,EAAM,EACpE,IAEJ7S,EACKyK,MAAK,WACJ,IAAI9B,EAAOlK,KACPmK,EAAOhG,UACPmQ,EAAQpK,EAAK2hB,QACb0D,EAAaxD,EAAI5B,MAA2B,oBAAd9e,EAA2BA,EAAUnH,MAAMgG,EAAMC,GAAQkB,EAAWiJ,EAAM6X,QACxGa,EAAOF,EAAQ5iB,EAAMC,GAAM0kB,cAE/BvF,GAAUpf,GACVoK,EAAMjJ,UAA2B,OAAfkkB,EAAsB,KAAOA,EAC/C7C,EAAOtqB,KAAK8H,GACZ8iB,EAAKzY,QAAQ0X,QAAQ3G,KACvB,GAER,EAEA2G,EAAMuD,MAAQ,SAASjuB,GACrB0qB,EAAMgD,KAAK1tB,EAAO,KACpB,EA6CA0rB,EAAQjsB,UAAY,CAClB6tB,YAAa,WAEX,OADsB,MAAhB7uB,KAAK4U,SAAc5U,KAAKsU,MAAMwY,QAAU9sB,KAAMA,KAAKyvB,UAAW,GAC7DzvB,IACT,EACAuU,MAAO,WAGL,OAFIvU,KAAKyvB,UAAUzvB,KAAKyvB,UAAW,EAAOzvB,KAAKgtB,KAAK,UAC/ChtB,KAAKgtB,KAAK,SACRhtB,IACT,EACAisB,MAAO,WAEL,OADAjsB,KAAKgtB,KAAK,SACHhtB,IACT,EACAslB,IAAK,WAEH,OADsB,MAAhBtlB,KAAK4U,gBAAqB5U,KAAKsU,MAAMwY,QAAS9sB,KAAKgtB,KAAK,QACvDhtB,IACT,EACAgtB,KAAM,SAAStjB,GACbO,GAAY,IAAIylB,GAAWzD,EAAOviB,EAAMqiB,EAAI3B,OAAOpqB,KAAKsU,MAAMjJ,YAAaihB,EAAUpoB,MAAOooB,EAAW,CAAC5iB,EAAM1J,KAAKkK,KAAMlK,KAAKmK,MAChI,GAkPF8hB,EAAME,OAAS,SAASrb,GACtB,OAAO3M,UAAU5D,QAAU4rB,EAAsB,oBAANrb,EAAmBA,EAAIsU,GAAS4E,GAAQlZ,IAAKmb,GAASE,CACnG,EAEAF,EAAMrgB,OAAS,SAASkF,GACtB,OAAO3M,UAAU5D,QAAUqL,EAAsB,oBAANkF,EAAmBA,EAAIsU,KAAWtU,GAAImb,GAASrgB,CAC5F,EAEAqgB,EAAMG,UAAY,SAAStb,GACzB,OAAO3M,UAAU5D,QAAU6rB,EAAyB,oBAANtb,EAAmBA,EAAIsU,KAAWtU,GAAImb,GAASG,CAC/F,EAEAH,EAAMM,WAAa,SAASzb,GAC1B,OAAO3M,UAAU5D,QAAUgsB,GAAczb,EAAGmb,GAASM,CACvD,EAEAN,EAAM0D,aAAe,SAAS7e,GAC5B,OAAO3M,UAAU5D,QAAU8rB,IAASvb,EAAGmb,GAASI,CAClD,EAEAJ,EAAM5iB,GAAK,WACT,IAAIzF,EAAQ0oB,EAAUjjB,GAAGnF,MAAMooB,EAAWnoB,WAC1C,OAAOP,IAAU0oB,EAAYL,EAAQroB,CACvC,EAEOqoB,CACT,CC9kBe,YAAShgB,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CCJe,SAAS2jB,GAAUlG,EAAQhgB,EAAMmmB,EAASnwB,EAAIkV,EAAQ3I,EAAGoD,EAAGif,EAAIC,EAAIzf,GACjF9O,KAAK0pB,OAASA,EACd1pB,KAAK0J,KAAOA,EACZ1J,KAAK6vB,QAAUA,EACf7vB,KAAK2Q,WAAajR,EAClBM,KAAK4U,OAASA,EACd5U,KAAKiM,EAAIA,EACTjM,KAAKqP,EAAIA,EACTrP,KAAKsuB,GAAKA,EACVtuB,KAAKuuB,GAAKA,EACVvuB,KAAK8Q,EAAIhC,CACX,CCHA,SAASoc,KACP,OAAQ3iB,GAAM4iB,UAAY5iB,GAAM6iB,MAClC,CAEA,SAAS0E,KACP,OAAO9vB,KAAKiH,UACd,CAEA,SAAS8oB,GAAehmB,GACtB,OAAY,MAALA,EAAY,CAACkC,EAAG1D,GAAM0D,EAAGoD,EAAG9G,GAAM8G,GAAKtF,CAChD,CAEA,SAAS2hB,KACP,OAAOC,UAAUC,gBAAmB,iBAAkB5rB,IACxD,CAEe,cACb,IAOIgwB,EACAC,EACAC,EACAhE,EAVAtgB,EAASsf,GACTiF,EAAYL,GACZD,EAAUE,GACV3D,EAAYV,GACZ0E,EAAW,CAAC,EACZ9D,GAAYxd,EAAAA,GAAAA,GAAS,QAAS,OAAQ,OACtC8F,EAAS,EAKTyb,EAAiB,EAErB,SAASC,EAAKjlB,GACZA,EACKhC,GAAG,iBAAkBknB,GACvB3kB,OAAOwgB,GACL/iB,GAAG,kBAAmBmnB,GACtBnnB,GAAG,iBAAkBujB,GACrBvjB,GAAG,iCAAkCwjB,GACrCroB,MAAM,eAAgB,QACtBA,MAAM,8BAA+B,gBAC5C,CAEA,SAAS+rB,IACP,IAAIrE,GAAgBtgB,EAAO1H,MAAMlE,KAAMmE,WAAvC,CACA,IAAIssB,EAAU5B,EAAY,QAASsB,EAAUjsB,MAAMlE,KAAMmE,WAAYwqB,GAAO3uB,KAAMmE,WAC7EssB,IACLnlB,GAAO/C,GAAM6H,MAAM/G,GAAG,iBAAkBqnB,GAAY,GAAMrnB,GAAG,eAAgBsnB,GAAY,GACzFC,GAAOroB,GAAM6H,MACbH,KACAigB,GAAc,EACdF,EAAaznB,GAAM6G,QACnB6gB,EAAa1nB,GAAM+G,QACnBmhB,EAAQ,SATiD,CAU3D,CAEA,SAASC,IAEP,GADArgB,MACK6f,EAAa,CAChB,IAAI5B,EAAK/lB,GAAM6G,QAAU4gB,EAAYzB,EAAKhmB,GAAM+G,QAAU2gB,EAC1DC,EAAc5B,EAAKA,EAAKC,EAAKA,EAAK8B,CACpC,CACAD,EAASzB,MAAM,OACjB,CAEA,SAASgC,IACPrlB,GAAO/C,GAAM6H,MAAM/G,GAAG,8BAA+B,MACrDmH,GAAQjI,GAAM6H,KAAM8f,GACpB7f,KACA+f,EAASzB,MAAM,MACjB,CAEA,SAAS6B,IACP,GAAK5kB,EAAO1H,MAAMlE,KAAMmE,WAAxB,CACA,IAEwBvC,EAAG6uB,EAFvB/f,EAAUnI,GAAMyH,eAChBmB,EAAIgf,EAAUjsB,MAAMlE,KAAMmE,WAC1B6B,EAAI0K,EAAQnQ,OAEhB,IAAKqB,EAAI,EAAGA,EAAIoE,IAAKpE,GACf6uB,EAAU5B,EAAYne,EAAQ9O,GAAG+O,WAAYQ,EAAGP,GAAO5Q,KAAMmE,cAC/D8L,KACAwgB,EAAQ,SAR8B,CAW5C,CAEA,SAAS7D,IACP,IACwBhrB,EAAG6uB,EADvB/f,EAAUnI,GAAMyH,eAChBhK,EAAI0K,EAAQnQ,OAEhB,IAAKqB,EAAI,EAAGA,EAAIoE,IAAKpE,GACf6uB,EAAUL,EAAS1f,EAAQ9O,GAAG+O,eAChCN,KACAogB,EAAQ,QAGd,CAEA,SAAS5D,IACP,IACwBjrB,EAAG6uB,EADvB/f,EAAUnI,GAAMyH,eAChBhK,EAAI0K,EAAQnQ,OAIhB,IAFI2rB,GAAatsB,aAAassB,GAC9BA,EAAcvsB,YAAW,WAAausB,EAAc,IAAM,GAAG,KACxDtqB,EAAI,EAAGA,EAAIoE,IAAKpE,GACf6uB,EAAUL,EAAS1f,EAAQ9O,GAAG+O,eAChCV,KACAwgB,EAAQ,OAGd,CAEA,SAAS5B,EAAYnvB,EAAIywB,EAAWhhB,EAAOjF,EAAMC,GAC/C,IAA8B6L,EAAGsY,EAAIC,EAAjCsC,EAAI1hB,EAAMghB,EAAWzwB,GACrBoxB,EAAexE,EAAU9a,OAE7B,GAAKvH,GAAY,IAAI2lB,GAAUU,EAAM,cAAeta,EAAGtW,EAAIkV,EAAQic,EAAE,GAAIA,EAAE,GAAI,EAAG,EAAGC,IAAe,WAClG,OAAuD,OAAlDvoB,GAAMsnB,QAAU7Z,EAAI6Z,EAAQ3rB,MAAMgG,EAAMC,MAC7CmkB,EAAKtY,EAAE/J,EAAI4kB,EAAE,IAAM,EACnBtC,EAAKvY,EAAE3G,EAAIwhB,EAAE,IAAM,GACZ,EACT,IAEA,OAAO,SAASJ,EAAQ/mB,GACtB,IAAY1D,EAAR+qB,EAAKF,EACT,OAAQnnB,GACN,IAAK,QAAS0mB,EAAS1wB,GAAM+wB,EAASzqB,EAAI4O,IAAU,MACpD,IAAK,aAAcwb,EAAS1wB,KAAOkV,EACnC,IAAK,OAAQic,EAAI1hB,EAAMghB,EAAWzwB,GAAKsG,EAAI4O,EAE7C3K,GAAY,IAAI2lB,GAAUU,EAAM5mB,EAAMsM,EAAGtW,EAAIsG,EAAG6qB,EAAE,GAAKvC,EAAIuC,EAAE,GAAKtC,EAAIsC,EAAE,GAAKE,EAAG,GAAIF,EAAE,GAAKE,EAAG,GAAID,GAAeA,EAAa5sB,MAAO4sB,EAAc,CAACpnB,EAAMQ,EAAMC,GAClK,CACF,CA2BA,OAzBAmmB,EAAK1kB,OAAS,SAASkF,GACrB,OAAO3M,UAAU5D,QAAUqL,EAAsB,oBAANkF,EAAmBA,EAAIsU,KAAWtU,GAAIwf,GAAQ1kB,CAC3F,EAEA0kB,EAAKH,UAAY,SAASrf,GACxB,OAAO3M,UAAU5D,QAAU4vB,EAAyB,oBAANrf,EAAmBA,EAAIsU,GAAStU,GAAIwf,GAAQH,CAC5F,EAEAG,EAAKT,QAAU,SAAS/e,GACtB,OAAO3M,UAAU5D,QAAUsvB,EAAuB,oBAAN/e,EAAmBA,EAAIsU,GAAStU,GAAIwf,GAAQT,CAC1F,EAEAS,EAAKlE,UAAY,SAAStb,GACxB,OAAO3M,UAAU5D,QAAU6rB,EAAyB,oBAANtb,EAAmBA,EAAIsU,KAAWtU,GAAIwf,GAAQlE,CAC9F,EAEAkE,EAAKjnB,GAAK,WACR,IAAIzF,EAAQ0oB,EAAUjjB,GAAGnF,MAAMooB,EAAWnoB,WAC1C,OAAOP,IAAU0oB,EAAYgE,EAAO1sB,CACtC,EAEA0sB,EAAKU,cAAgB,SAASlgB,GAC5B,OAAO3M,UAAU5D,QAAU8vB,GAAkBvf,GAAKA,GAAKA,EAAGwf,GAAQ9wB,KAAK+V,KAAK8a,EAC9E,EAEOC,CACT,CDzJAV,GAAU5uB,UAAUqI,GAAK,WACvB,IAAIzF,EAAQ5D,KAAK8Q,EAAEzH,GAAGnF,MAAMlE,KAAK8Q,EAAG3M,WACpC,OAAOP,IAAU5D,KAAK8Q,EAAI9Q,KAAO4D,CACnC,E,2BEhBWwrB,GAAM5vB,KAAK4vB,IACX5Z,GAAQhW,KAAKgW,MACbyb,GAAMzxB,KAAKyxB,IACXxxB,GAAMD,KAAKC,IACX6N,GAAM9N,KAAK8N,IACX4jB,GAAM1xB,KAAK0xB,IACX3b,GAAO/V,KAAK+V,KAEZ4b,GAAU,MACVC,GAAK5xB,KAAKuV,GACVsc,GAASD,GAAK,EACdE,GAAM,EAAIF,GAMd,SAASG,GAAKtlB,GACnB,OAAOA,GAAK,EAAIolB,GAASplB,IAAM,GAAKolB,GAAS7xB,KAAK+xB,KAAKtlB,EACzD,CCfA,SAASulB,GAAeznB,GACtB,OAAOA,EAAE0nB,WACX,CAEA,SAASC,GAAe3nB,GACtB,OAAOA,EAAE4nB,WACX,CAEA,SAASC,GAAc7nB,GACrB,OAAOA,EAAE8nB,UACX,CAEA,SAASC,GAAY/nB,GACnB,OAAOA,EAAEgoB,QACX,CAEA,SAASC,GAAYjoB,GACnB,OAAOA,GAAKA,EAAEkoB,QAChB,CAaA,SAASC,GAAeC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAC9C,IAAIC,EAAMP,EAAKE,EACXM,EAAMP,EAAKE,EACXM,GAAMH,EAAKD,GAAMA,GAAMjd,GAAKmd,EAAMA,EAAMC,EAAMA,GAC9CE,EAAKD,EAAKD,EACVG,GAAMF,EAAKF,EACXK,EAAMZ,EAAKU,EACXG,EAAMZ,EAAKU,EACXG,EAAMZ,EAAKQ,EACXK,EAAMZ,EAAKQ,EACXK,GAAOJ,EAAME,GAAO,EACpBG,GAAOJ,EAAME,GAAO,EACpB5E,EAAK2E,EAAMF,EACXxE,EAAK2E,EAAMF,EACXK,EAAK/E,EAAKA,EAAKC,EAAKA,EACpBvL,EAAIuP,EAAKC,EACTc,EAAIP,EAAMG,EAAMD,EAAMD,EACtBjpB,GAAKwkB,EAAK,GAAK,EAAI,GAAKhZ,GAAK9V,GAAI,EAAGujB,EAAIA,EAAIqQ,EAAKC,EAAIA,IACrDC,GAAOD,EAAI/E,EAAKD,EAAKvkB,GAAKspB,EAC1BG,IAAQF,EAAIhF,EAAKC,EAAKxkB,GAAKspB,EAC3BI,GAAOH,EAAI/E,EAAKD,EAAKvkB,GAAKspB,EAC1BK,IAAQJ,EAAIhF,EAAKC,EAAKxkB,GAAKspB,EAC3BM,EAAMJ,EAAMJ,EACZS,EAAMJ,EAAMJ,EACZS,EAAMJ,EAAMN,EACZW,EAAMJ,EAAMN,EAMhB,OAFIO,EAAMA,EAAMC,EAAMA,EAAMC,EAAMA,EAAMC,EAAMA,IAAKP,EAAME,EAAKD,EAAME,GAE7D,CACLK,GAAIR,EACJS,GAAIR,EACJd,KAAMG,EACNF,KAAMG,EACNC,IAAKQ,GAAOhB,EAAKvP,EAAI,GACrBgQ,IAAKQ,GAAOjB,EAAKvP,EAAI,GAEzB,CAEe,cACb,IAAIyO,EAAcD,GACdG,EAAcD,GACduC,GAAe7O,EAAAA,GAAAA,GAAS,GACxB8O,EAAY,KACZrC,EAAaD,GACbG,EAAWD,GACXG,EAAWD,GACXmC,EAAU,KAEd,SAASC,IACP,IAAIC,EACArR,ED3Ea/W,EC4EbqoB,GAAM7C,EAAYvtB,MAAMlE,KAAMmE,WAC9BouB,GAAMZ,EAAYztB,MAAMlE,KAAMmE,WAC9BowB,EAAK1C,EAAW3tB,MAAMlE,KAAMmE,WAAaktB,GACzCmD,EAAKzC,EAAS7tB,MAAMlE,KAAMmE,WAAaktB,GACvCoD,EAAKrF,GAAIoF,EAAKD,GACd9B,EAAK+B,EAAKD,EAQd,GANKJ,IAASA,EAAUE,GAASK,EAAAA,GAAAA,MAG7BnC,EAAK+B,IAAItR,EAAIuP,EAAIA,EAAK+B,EAAIA,EAAKtR,GAG7BuP,EAAKpB,GAGN,GAAIsD,EAAKnD,GAAMH,GAClBgD,EAAQQ,OAAOpC,EAAKtB,GAAIsD,GAAKhC,EAAKrB,GAAIqD,IACtCJ,EAAQC,IAAI,EAAG,EAAG7B,EAAIgC,EAAIC,GAAK/B,GAC3B6B,EAAKnD,KACPgD,EAAQQ,OAAOL,EAAKrD,GAAIuD,GAAKF,EAAKpD,GAAIsD,IACtCL,EAAQC,IAAI,EAAG,EAAGE,EAAIE,EAAID,EAAI9B,QAK7B,CACH,IAWIvf,EACAE,EAZAwhB,EAAML,EACNM,EAAML,EACNM,EAAMP,EACNQ,EAAMP,EACNQ,EAAMP,EACNQ,EAAMR,EACNS,EAAKjD,EAAS/tB,MAAMlE,KAAMmE,WAAa,EACvCgxB,EAAMD,EAAK/D,KAAa+C,GAAaA,EAAUhwB,MAAMlE,KAAMmE,WAAaoR,GAAK+e,EAAKA,EAAK/B,EAAKA,IAC5FC,EAAKllB,GAAI8hB,GAAImD,EAAK+B,GAAM,GAAIL,EAAa/vB,MAAMlE,KAAMmE,YACrDixB,EAAM5C,EACN6C,EAAM7C,EAKV,GAAI2C,EAAKhE,GAAS,CAChB,IAAIJ,EAAKQ,GAAK4D,EAAKb,EAAKpD,GAAIgE,IACxBI,EAAK/D,GAAK4D,EAAK5C,EAAKrB,GAAIgE,KACvBF,GAAY,EAALjE,GAAUI,IAA8B2D,GAArB/D,GAAO0B,EAAK,GAAK,EAAesC,GAAOhE,IACjEiE,EAAM,EAAGF,EAAMC,GAAOR,EAAKC,GAAM,IACjCS,GAAY,EAALK,GAAUnE,IAA8ByD,GAArBU,GAAO7C,EAAK,GAAK,EAAeoC,GAAOS,IACjEL,EAAM,EAAGL,EAAMC,GAAON,EAAKC,GAAM,EACxC,CAEA,IAAI9B,EAAMH,EAAKtB,GAAI2D,GACfjC,EAAMJ,EAAKrB,GAAI0D,GACf3B,EAAMqB,EAAKrD,GAAI8D,GACf7B,EAAMoB,EAAKpD,GAAI6D,GAGnB,GAAIvC,EAAKrB,GAAS,CAChB,IAIIoE,EAJAxC,EAAMR,EAAKtB,GAAI4D,GACf7B,EAAMT,EAAKrB,GAAI2D,GACf1B,EAAMmB,EAAKrD,GAAI6D,GACf1B,EAAMkB,EAAKpD,GAAI4D,GAInB,GAAIL,EAAKrD,KAAOmE,EAlIxB,SAAmBpD,EAAIC,EAAIC,EAAIC,EAAIkD,EAAIC,EAAIC,EAAIC,GAC7C,IAAI1C,EAAMZ,EAAKF,EAAIe,EAAMZ,EAAKF,EAC1BwD,EAAMF,EAAKF,EAAIK,EAAMF,EAAKF,EAC1B9mB,EAAIknB,EAAM5C,EAAM2C,EAAM1C,EAC1B,KAAIvkB,EAAIA,EAAIwiB,IAEZ,MAAO,CAACgB,GADRxjB,GAAKinB,GAAOxD,EAAKqD,GAAMI,GAAO1D,EAAKqD,IAAO7mB,GACzBskB,EAAKb,EAAKzjB,EAAIukB,EACjC,CA2H6B4C,CAAUpD,EAAKC,EAAKQ,EAAKC,EAAKL,EAAKC,EAAKC,EAAKC,IAAO,CACvE,IAAI6C,EAAKrD,EAAM6C,EAAG,GACdS,EAAKrD,EAAM4C,EAAG,GACdU,EAAKlD,EAAMwC,EAAG,GACdW,EAAKlD,EAAMuC,EAAG,GACdY,EAAK,EAAIjF,KDlJFjlB,GCkJY8pB,EAAKE,EAAKD,EAAKE,IAAO3gB,GAAKwgB,EAAKA,EAAKC,EAAKA,GAAMzgB,GAAK0gB,EAAKA,EAAKC,EAAKA,KDjJxF,EAAI,EAAIjqB,GAAK,EAAImlB,GAAK5xB,KAAK42B,KAAKnqB,ICiJgE,GAC/FoqB,EAAK9gB,GAAKggB,EAAG,GAAKA,EAAG,GAAKA,EAAG,GAAKA,EAAG,IACzCH,EAAM9nB,GAAIklB,GAAK8B,EAAK+B,IAAOF,EAAK,IAChCd,EAAM/nB,GAAIklB,GAAKD,EAAK8D,IAAOF,EAAK,GAClC,CACF,CAGMlB,EAAM9D,GAGHkE,EAAMlE,IACbje,EAAKgf,GAAeiB,EAAKC,EAAKV,EAAKC,EAAKJ,EAAI8C,EAAK5C,GACjDrf,EAAK8e,GAAea,EAAKC,EAAKC,EAAKC,EAAKX,EAAI8C,EAAK5C,GAEjD0B,EAAQQ,OAAOzhB,EAAG6gB,GAAK7gB,EAAGwf,IAAKxf,EAAG8gB,GAAK9gB,EAAGyf,KAGtC0C,EAAM7C,EAAI2B,EAAQC,IAAIlhB,EAAG6gB,GAAI7gB,EAAG8gB,GAAIqB,EAAK7f,GAAMtC,EAAGyf,IAAKzf,EAAGwf,KAAMld,GAAMpC,EAAGuf,IAAKvf,EAAGsf,MAAOD,IAI1F0B,EAAQC,IAAIlhB,EAAG6gB,GAAI7gB,EAAG8gB,GAAIqB,EAAK7f,GAAMtC,EAAGyf,IAAKzf,EAAGwf,KAAMld,GAAMtC,EAAG8f,IAAK9f,EAAG6f,MAAON,GAC9E0B,EAAQC,IAAI,EAAG,EAAG7B,EAAI/c,GAAMtC,EAAG8gB,GAAK9gB,EAAG8f,IAAK9f,EAAG6gB,GAAK7gB,EAAG6f,KAAMvd,GAAMpC,EAAG4gB,GAAK5gB,EAAG4f,IAAK5f,EAAG2gB,GAAK3gB,EAAG2f,MAAON,GACrG0B,EAAQC,IAAIhhB,EAAG2gB,GAAI3gB,EAAG4gB,GAAIqB,EAAK7f,GAAMpC,EAAG4f,IAAK5f,EAAG2f,KAAMvd,GAAMpC,EAAGuf,IAAKvf,EAAGsf,MAAOD,MAK7E0B,EAAQQ,OAAOjC,EAAKC,GAAMwB,EAAQC,IAAI,EAAG,EAAG7B,EAAIqC,EAAKC,GAAMpC,IArB1C0B,EAAQQ,OAAOjC,EAAKC,GAyBpC2B,EAAKnD,IAAc6D,EAAM7D,GAGtBiE,EAAMjE,IACbje,EAAKgf,GAAee,EAAKC,EAAKH,EAAKC,EAAKsB,GAAKc,EAAK3C,GAClDrf,EAAK8e,GAAeQ,EAAKC,EAAKQ,EAAKC,EAAKkB,GAAKc,EAAK3C,GAElD0B,EAAQmC,OAAOpjB,EAAG6gB,GAAK7gB,EAAGwf,IAAKxf,EAAG8gB,GAAK9gB,EAAGyf,KAGtCyC,EAAM5C,EAAI2B,EAAQC,IAAIlhB,EAAG6gB,GAAI7gB,EAAG8gB,GAAIoB,EAAK5f,GAAMtC,EAAGyf,IAAKzf,EAAGwf,KAAMld,GAAMpC,EAAGuf,IAAKvf,EAAGsf,MAAOD,IAI1F0B,EAAQC,IAAIlhB,EAAG6gB,GAAI7gB,EAAG8gB,GAAIoB,EAAK5f,GAAMtC,EAAGyf,IAAKzf,EAAGwf,KAAMld,GAAMtC,EAAG8f,IAAK9f,EAAG6f,MAAON,GAC9E0B,EAAQC,IAAI,EAAG,EAAGE,EAAI9e,GAAMtC,EAAG8gB,GAAK9gB,EAAG8f,IAAK9f,EAAG6gB,GAAK7gB,EAAG6f,KAAMvd,GAAMpC,EAAG4gB,GAAK5gB,EAAG4f,IAAK5f,EAAG2gB,GAAK3gB,EAAG2f,KAAMN,GACpG0B,EAAQC,IAAIhhB,EAAG2gB,GAAI3gB,EAAG4gB,GAAIoB,EAAK5f,GAAMpC,EAAG4f,IAAK5f,EAAG2f,KAAMvd,GAAMpC,EAAGuf,IAAKvf,EAAGsf,MAAOD,KAK7E0B,EAAQC,IAAI,EAAG,EAAGE,EAAIS,EAAKD,EAAKrC,GArBI0B,EAAQmC,OAAOrD,EAAKC,EAsB/D,MAhHqBiB,EAAQQ,OAAO,EAAG,GAoHvC,GAFAR,EAAQoC,YAEJlC,EAAQ,OAAOF,EAAU,KAAME,EAAS,IAAM,IACpD,CAwCA,OAtCAD,EAAIoC,SAAW,WACb,IAAIxT,IAAMyO,EAAYvtB,MAAMlE,KAAMmE,aAAcwtB,EAAYztB,MAAMlE,KAAMmE,YAAc,EAClF7B,IAAMuvB,EAAW3tB,MAAMlE,KAAMmE,aAAc4tB,EAAS7tB,MAAMlE,KAAMmE,YAAc,EAAIitB,GAAK,EAC3F,MAAO,CAACH,GAAI3uB,GAAK0gB,EAAGkO,GAAI5uB,GAAK0gB,EAC/B,EAEAoR,EAAI3C,YAAc,SAAS3gB,GACzB,OAAO3M,UAAU5D,QAAUkxB,EAA2B,oBAAN3gB,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAO3C,CAC9F,EAEA2C,EAAIzC,YAAc,SAAS7gB,GACzB,OAAO3M,UAAU5D,QAAUoxB,EAA2B,oBAAN7gB,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAOzC,CAC9F,EAEAyC,EAAIH,aAAe,SAASnjB,GAC1B,OAAO3M,UAAU5D,QAAU0zB,EAA4B,oBAANnjB,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAOH,CAC/F,EAEAG,EAAIF,UAAY,SAASpjB,GACvB,OAAO3M,UAAU5D,QAAU2zB,EAAiB,MAALpjB,EAAY,KAAoB,oBAANA,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAOF,CAC/G,EAEAE,EAAIvC,WAAa,SAAS/gB,GACxB,OAAO3M,UAAU5D,QAAUsxB,EAA0B,oBAAN/gB,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAOvC,CAC7F,EAEAuC,EAAIrC,SAAW,SAASjhB,GACtB,OAAO3M,UAAU5D,QAAUwxB,EAAwB,oBAANjhB,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAOrC,CAC3F,EAEAqC,EAAInC,SAAW,SAASnhB,GACtB,OAAO3M,UAAU5D,QAAU0xB,EAAwB,oBAANnhB,EAAmBA,GAAIsU,EAAAA,GAAAA,IAAUtU,GAAIsjB,GAAOnC,CAC3F,EAEAmC,EAAID,QAAU,SAASrjB,GACrB,OAAO3M,UAAU5D,QAAW4zB,EAAe,MAALrjB,EAAY,KAAOA,EAAIsjB,GAAOD,CACtE,EAEOC,CACT,C,gBCpQe,YAAS9xB,EAAGC,GACzB,OAAOD,EAAIC,GAAK,EAAID,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAIC,GAC/C,CCCA,IAAIi0B,GCDW,SAAS9oB,GA0BxB,IAA6B0E,EAxB3B,OADuB,IAAnB1E,EAAQpN,SAyBe8R,EAzB6B1E,EAA9BA,EA0BnB,SAAS5D,EAAGkC,GACjB,OAAO5J,GAAUgQ,EAAEtI,GAAIkC,EACzB,GA3BO,CACL2D,KAAM,SAAStN,EAAG2J,EAAG2mB,EAAI8D,GAGvB,IAFU,MAAN9D,IAAYA,EAAK,GACX,MAAN8D,IAAYA,EAAKp0B,EAAE/B,QAChBqyB,EAAK8D,GAAI,CACd,IAAIC,EAAM/D,EAAK8D,IAAO,EAClB/oB,EAAQrL,EAAEq0B,GAAM1qB,GAAK,EAAG2mB,EAAK+D,EAAM,EAClCD,EAAKC,CACZ,CACA,OAAO/D,CACT,EACAgE,MAAO,SAASt0B,EAAG2J,EAAG2mB,EAAI8D,GAGxB,IAFU,MAAN9D,IAAYA,EAAK,GACX,MAAN8D,IAAYA,EAAKp0B,EAAE/B,QAChBqyB,EAAK8D,GAAI,CACd,IAAIC,EAAM/D,EAAK8D,IAAO,EAClB/oB,EAAQrL,EAAEq0B,GAAM1qB,GAAK,EAAGyqB,EAAKC,EAC5B/D,EAAK+D,EAAM,CAClB,CACA,OAAO/D,CACT,EAEJ,CDvBsBiE,CAASx0B,IACNo0B,GAAgBG,MACjBH,GAAgB7mB,KELzB,YAAS+V,EAAQmR,GAC9B,IAEIlzB,EACA0J,EACA7N,EAJAuG,EAAI2f,EAAOplB,OACXqB,GAAK,EAKT,GAAe,MAAXk1B,GACF,OAASl1B,EAAIoE,GACX,GAA2B,OAAtBpC,EAAQ+hB,EAAO/jB,KAAegC,GAASA,EAE1C,IADA0J,EAAM7N,EAAMmE,IACHhC,EAAIoE,GACgB,OAAtBpC,EAAQ+hB,EAAO/jB,MACd0L,EAAM1J,IAAO0J,EAAM1J,GACnBnE,EAAMmE,IAAOnE,EAAMmE,SAQ/B,OAAShC,EAAIoE,GACX,GAA+C,OAA1CpC,EAAQkzB,EAAQnR,EAAO/jB,GAAIA,EAAG+jB,KAAoB/hB,GAASA,EAE9D,IADA0J,EAAM7N,EAAMmE,IACHhC,EAAIoE,GACoC,OAA1CpC,EAAQkzB,EAAQnR,EAAO/jB,GAAIA,EAAG+jB,MAC7BrY,EAAM1J,IAAO0J,EAAM1J,GACnBnE,EAAMmE,IAAOnE,EAAMmE,IAOjC,MAAO,CAAC0J,EAAK7N,EACf,CCpCA,IAAIs3B,GAAQz2B,MAAMU,UAEC+1B,GAAM9zB,MACR8zB,GAAMrqB,ICHblN,KAAK+V,KAAK,IACX/V,KAAK+V,KAAK,IACV/V,KAAK+V,KAAK,G,gBCFRtS,GAAQ3C,MAAMU,UAAUiC,MCApB,YAASgJ,GACtB,OAAOA,CACT,CCCA,IAIIklB,GAAU,KAEd,SAASlc,GAAWhJ,GAClB,MAAO,cAAgBA,EAAI,IAAO,KACpC,CAEA,SAASiJ,GAAW7F,GAClB,MAAO,gBAAkBA,EAAI,IAAO,GACtC,CAEA,SAASiH,GAAOE,GACd,OAAO,SAASzM,GACd,OAAQyM,EAAMzM,EAChB,CACF,CAEA,SAASitB,GAAOxgB,GACd,IAAIygB,EAASz3B,KAAKC,IAAI,EAAG+W,EAAM0gB,YAAc,GAAK,EAElD,OADI1gB,EAAMiN,UAASwT,EAASz3B,KAAKikB,MAAMwT,IAChC,SAASltB,GACd,OAAQyM,EAAMzM,GAAKktB,CACrB,CACF,CAEA,SAASE,KACP,OAAQn3B,KAAKo3B,MACf,CAEA,SAASC,GAAKC,EAAQ9gB,GACpB,IAAI+gB,EAAgB,GAChBC,EAAa,KACbC,EAAa,KACbC,EAAgB,EAChBC,EAAgB,EAChBC,EAAc,EACd/S,EAvCI,IAuCAyS,GApCC,IAoCiBA,GAAmB,EAAI,EAC7CrrB,EArCK,IAqCDqrB,GAvCE,IAuCiBA,EAAmB,IAAM,IAChDvgB,EAzCI,IAyCQugB,GAvCL,IAuCuBA,EAAoBriB,GAAaC,GAEnE,SAASmiB,EAAKlD,GACZ,IAAIxO,EAAuB,MAAd6R,EAAsBhhB,EAAMqhB,MAAQrhB,EAAMqhB,MAAM3zB,MAAMsS,EAAO+gB,GAAiB/gB,EAAMshB,SAAYN,EACzGjV,EAAuB,MAAdkV,EAAsBjhB,EAAMihB,WAAajhB,EAAMihB,WAAWvzB,MAAMsS,EAAO+gB,GAAiBviB,GAAYyiB,EAC7GM,EAAUv4B,KAAKC,IAAIi4B,EAAe,GAAKE,EACvCI,EAAQxhB,EAAMwhB,QACdC,GAAUD,EAAM,GAAK,GACrBE,GAAUF,EAAMA,EAAMz3B,OAAS,GAAK,GACpC43B,GAAY3hB,EAAM0gB,UAAYF,GAAS1gB,IAAQE,EAAMhF,QACrDnG,EAAY8oB,EAAQ9oB,UAAY8oB,EAAQ9oB,YAAc8oB,EACtDO,EAAOrpB,EAAUK,UAAU,WAAWhK,KAAK,CAAC,OAC5C8S,EAAOnJ,EAAUK,UAAU,SAAShK,KAAKikB,EAAQnP,GAAOvJ,QACxDmrB,EAAW5jB,EAAK/S,OAChB42B,EAAY7jB,EAAKhT,QAAQuL,OAAO,KAAKiB,KAAK,QAAS,QACnDsqB,EAAO9jB,EAAKlJ,OAAO,QACnB8C,EAAOoG,EAAKlJ,OAAO,QAEvBopB,EAAOA,EAAK1nB,MAAM0nB,EAAKlzB,QAAQgN,OAAO,OAAQ,SACzCR,KAAK,QAAS,UACdA,KAAK,SAAU,iBAEpBwG,EAAOA,EAAKxH,MAAMqrB,GAElBC,EAAOA,EAAKtrB,MAAMqrB,EAAUtrB,OAAO,QAC9BiB,KAAK,SAAU,gBACfA,KAAK/B,EAAI,IAAK4Y,EAAI6S,IAEvBtpB,EAAOA,EAAKpB,MAAMqrB,EAAUtrB,OAAO,QAC9BiB,KAAK,OAAQ,gBACbA,KAAK/B,EAAG4Y,EAAIkT,GACZ/pB,KAAK,KAxEJ,IAwEUspB,EAAiB,MAtExB,IAsEgCA,EAAoB,SAAW,WAEpEnD,IAAY9oB,IACdqpB,EAAOA,EAAKld,WAAW2c,GACvB3f,EAAOA,EAAKgD,WAAW2c,GACvBmE,EAAOA,EAAK9gB,WAAW2c,GACvB/lB,EAAOA,EAAKoJ,WAAW2c,GAEvBiE,EAAWA,EAAS5gB,WAAW2c,GAC1BnmB,KAAK,UAAWmjB,IAChBnjB,KAAK,aAAa,SAASjE,GAAK,OAAOwuB,SAASxuB,EAAIouB,EAASpuB,IAAMgN,EAAUhN,GAAK/J,KAAK4F,aAAa,YAAc,IAEvHyyB,EACKrqB,KAAK,UAAWmjB,IAChBnjB,KAAK,aAAa,SAASjE,GAAK,IAAI8mB,EAAI7wB,KAAKiH,WAAWmwB,OAAQ,OAAOrgB,EAAU8Z,GAAK0H,SAAS1H,EAAIA,EAAE9mB,IAAM8mB,EAAIsH,EAASpuB,GAAK,KAGpIquB,EAASjyB,SAETuuB,EACK1mB,KAAK,IAzFH,IAyFQspB,GA3FP,GA2F0BA,EACvBK,EAAgB,IAAM9S,EAAI8S,EAAgB,IAAMM,EAAS,QAAUC,EAAS,IAAMrT,EAAI8S,EAAgB,QAAUM,EAAS,IAAMC,EAC/HP,EAAgB,IAAMM,EAAS,IAAMpT,EAAI8S,EAAgB,QAAUO,EAAS,IAAMrT,EAAI8S,EAAgB,IAAMM,EAAS,QAAUC,GAE1I1jB,EACKxG,KAAK,UAAW,GAChBA,KAAK,aAAa,SAASjE,GAAK,OAAOgN,EAAUohB,EAASpuB,GAAK,IAEpEuuB,EACKtqB,KAAK/B,EAAI,IAAK4Y,EAAI6S,GAEvBtpB,EACKJ,KAAK/B,EAAG4Y,EAAIkT,GACZ3pB,KAAKmU,GAEVlX,EAAUO,OAAOurB,IACZnpB,KAAK,OAAQ,QACbA,KAAK,YAAa,IAClBA,KAAK,cAAe,cACpBA,KAAK,cA9GF,IA8GiBspB,EAAmB,QA5GrC,IA4G+CA,EAAkB,MAAQ,UAEhFjsB,EACKW,MAAK,WAAahM,KAAKo3B,OAASe,CAAU,GACjD,CAsCA,OApCAd,EAAK7gB,MAAQ,SAAS1F,GACpB,OAAO3M,UAAU5D,QAAUiW,EAAQ1F,EAAGumB,GAAQ7gB,CAChD,EAEA6gB,EAAKQ,MAAQ,WACX,OAAON,EAAgBt0B,GAAMb,KAAK+B,WAAYkzB,CAChD,EAEAA,EAAKE,cAAgB,SAASzmB,GAC5B,OAAO3M,UAAU5D,QAAUg3B,EAAqB,MAALzmB,EAAY,GAAK7N,GAAMb,KAAK0O,GAAIumB,GAAQE,EAAct0B,OACnG,EAEAo0B,EAAKG,WAAa,SAAS1mB,GACzB,OAAO3M,UAAU5D,QAAUi3B,EAAkB,MAAL1mB,EAAY,KAAO7N,GAAMb,KAAK0O,GAAIumB,GAAQG,GAAcA,EAAWv0B,OAC7G,EAEAo0B,EAAKI,WAAa,SAAS3mB,GACzB,OAAO3M,UAAU5D,QAAUk3B,EAAa3mB,EAAGumB,GAAQI,CACrD,EAEAJ,EAAKmB,SAAW,SAAS1nB,GACvB,OAAO3M,UAAU5D,QAAUm3B,EAAgBC,GAAiB7mB,EAAGumB,GAAQK,CACzE,EAEAL,EAAKK,cAAgB,SAAS5mB,GAC5B,OAAO3M,UAAU5D,QAAUm3B,GAAiB5mB,EAAGumB,GAAQK,CACzD,EAEAL,EAAKM,cAAgB,SAAS7mB,GAC5B,OAAO3M,UAAU5D,QAAUo3B,GAAiB7mB,EAAGumB,GAAQM,CACzD,EAEAN,EAAKO,YAAc,SAAS9mB,GAC1B,OAAO3M,UAAU5D,QAAUq3B,GAAe9mB,EAAGumB,GAAQO,CACvD,EAEOP,CACT,CAEO,SAASoB,GAAQjiB,GACtB,OAAO6gB,GA7JC,EA6JS7gB,EACnB,CAEO,SAASkiB,GAAUliB,GACxB,OAAO6gB,GAhKG,EAgKS7gB,EACrB,CAEO,SAASmiB,GAAWniB,GACzB,OAAO6gB,GAnKI,EAmKS7gB,EACtB,CAEO,SAASoiB,GAASpiB,GACvB,OAAO6gB,GAtKE,EAsKS7gB,EACpB,CC3KA,IAAMqiB,GAAc,SAASC,G,IACvBC,EAAS,G,EACH,I,EACM,WAAY,E,EACjB,WAAY,EAEnBC,EAAK,SAALA,EAAct3B,GACZA,GAAMs3B,EAAGt3B,KAAKA,G,UAGfu3B,Q,WAGFA,OAAS,W,IACNC,GAAQ,E,EACEF,EAAGG,WAAa,W,GACpB,C,WAGDC,I,IACFF,EAAO,OAAO,EACPH,EAAO3wB,OAAO,EAAGixB,GACvB3sB,IAAIosB,G,sBACYM,E,OAMvB13B,KAAO,SAASA,G,aAERA,EAAKuB,MAAM,GACb+1B,C,IAGN/yB,IAAM,SAASvE,G,EACPq3B,EAAO1nB,OAAO3P,E,IAGtB43B,KAAO,SAAS11B,G,OACZO,UAAU5D,Q,EACPqD,EACDo1B,GAFuBK,C,IAK7BE,UAAY,W,OACNR,EAAOx4B,M,IAIbivB,MAAQ,SAASsJ,G,OACb30B,UAAU5D,Q,EAINu4B,EACFE,I,IAHEA,E,IAMRG,WAAaK,EAETR,C,EC/DHvO,GAAI,SAAAgP,G,OAAUA,EAAOjO,MAAQiO,EAAOC,OAAO9C,MAAQ6C,EAAOC,OAAO9pB,I,ECejE+pB,GAAgB,SAACtuB,EAAWmL,G,OAClB,OAAVA,EAAuB,GACI,qBAAjBA,EAAMojB,OAjBI,SAACvuB,EAAWmL,G,GACX,IAArBnL,EAAU9K,O,MACL,G,IAEHu3B,EAASthB,EAAMshB,SACfE,EAAQxhB,EAAMwhB,QACd6B,EAAQ,G,SACRC,SAAQ,SAAC/vB,EAAGnI,GACZmI,GAAKsB,EAAU,IAAMtB,GAAKsB,EAAU,I,EAChCnD,KAAK4vB,EAAOl2B,G,IAGfi4B,C,CAMHE,CAAkB1uB,EAAWmL,GAC7BnL,EAAUqB,KAAI,SAAA3C,G,OAAKyM,EAAMojB,OAAO7vB,E,KCdhCiwB,GAAe,SAAC1lB,EAAOmlB,EAAQQ,G,OAAO,SAAAC,G,IAClCC,EAAwB7lB,EAAxB6lB,QAASC,EAAe9lB,EAAf8lB,W,GAEM,qBAAZF,E,OACFriB,OAAOwU,KAAKoN,EAAOY,YAAYC,QAAO,SAACC,EAAKC,G,IAC3CvO,EAAQkO,EAAQK,G,QAERxnB,IAAViZ,GAA2D,OAApCH,GAAesO,EAAWI,IAAgB,C,IAC7DC,EAAM3O,GAAesO,EAAWI,IAChCE,EAASjB,EAAOY,WAAWG,GAAKG,OAChCC,EAASjB,GAAcc,EAAKC,G,EAE9BF,GAAO,C,OACDvO,EAAME,S,UACH,C,wBAORoO,C,GACN,CAAC,G,IAGEM,EAAkB,CAAC,E,SACtB5X,IACAvX,UAAU,UACVM,MAAK,SAASjC,G,EACGA,GAAKuB,GAAOtL,K,WAIzBqsB,KAAKoN,EAAOY,YAAYP,SAAQ,SAAA/vB,G,QAClBiJ,IAAfknB,EAAQnwB,G,KAINkiB,EAAQkO,EAAQpwB,G,QACRiJ,IAAViZ,EAAqB,C,IACjBF,EAAM0N,EAAOY,WAAWtwB,GACxB+wB,EAAUZ,EAAQnwB,GAAG2C,IAAIqf,EAAI4O,Q,EAYnB5wB,GAAG3H,KAAK6pB,GAAO7pB,KAAK6pB,EAAMgD,KAAM6L,EAAQC,U,QAQzDC,gBAEIf,C,GChELgB,GAAW,SAAC3mB,EAAOmlB,EAAQyB,G,OAAe,W,IACtCd,EAAe9lB,EAAf8lB,WAIFe,EAAUtjB,OAAOwU,KAAKoN,EAAOY,YAAYzuB,QAH5B,SAAAilB,G,OACjBuJ,EAAWvJ,IAAwC,OAAlC/E,GAAesO,EAAWvJ,G,IAGvCqJ,EAAUiB,EAAQzuB,KAAI,SAAAmkB,G,IACpBuK,EAActP,GAAesO,EAAWvJ,I,MAEI,oBAAvC4I,EAAOY,WAAWxJ,GAAG8J,OAAOf,OAC9B,CACLH,EAAOY,WAAWxJ,GAAG8J,OAAOf,OAAOwB,EAAY,IAC/C3B,EAAOY,WAAWxJ,GAAG8J,OAAOf,OAAOwB,EAAY,KAG1CA,C,OASY,IAAnBD,EAAQ56B,OAAc,OAAOk5B,EAAO/3B,K,IAGlC25B,EAAS,C,KACP,SAACtxB,EAAG8mB,EAAGyK,G,MAC0C,oBAA1C7B,EAAOY,WAAWxJ,GAAG8J,OAAOzD,UAGnCgD,EAAQoB,GAAW,IAAM7B,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KACvD4I,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KAAOqJ,EAAQoB,GAAW,GAGnDpB,EAAQoB,GAAW,IAAMvxB,EAAE8mB,IAAM9mB,EAAE8mB,IAAMqJ,EAAQoB,GAAW,E,SAG/D,SAACvxB,EAAG8mB,EAAGyK,G,MACwC,oBAA1C7B,EAAOY,WAAWxJ,GAAG8J,OAAOzD,UAGnCgD,EAAQoB,GAAW,IAAM7B,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KACvD4I,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KAAOqJ,EAAQoB,GAAW,GAGnDpB,EAAQoB,GAAW,IAAMvxB,EAAE8mB,IAAM9mB,EAAE8mB,IAAMqJ,EAAQoB,GAAW,E,SAG/D,SAACvxB,EAAG8mB,EAAGyK,G,OAEXpB,EAAQoB,GAAW,IAAM7B,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KACvD4I,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KAAOqJ,EAAQoB,GAAW,E,UAKvD7B,EAAO/3B,KAAKkK,QAAO,SAAA7B,G,OAChBmxB,EAAWK,W,IACZ,M,OACIJ,EAAQ7S,OAAM,SAASuI,EAAGyK,G,OACxBD,EAAO5B,EAAOY,WAAWxJ,GAAGnnB,MAAMK,EAAG8mB,EAAGyK,E,QAE9C,K,OACIH,EAAQK,MAAK,SAAS3K,EAAGyK,G,OACvBD,EAAO5B,EAAOY,WAAWxJ,GAAGnnB,MAAMK,EAAG8mB,EAAGyK,E,kBAG3C,IAAItqB,MAAM,2BAA6ByoB,EAAOgC,gB,MCpEtDC,GAAe,SAACjC,EAAQQ,EAAI0B,EAAQxxB,G,OAAS,SAAAyxB,G,EAC1CC,QAAUD,E,EACVx5B,KAAK,QAAS63B,EAAIR,EAAOoC,QAAS1xB,G,EACtC6wB,e,GCHCc,GAAU,SAACxnB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,OAAe,WACpDjB,EAAGhX,K,EACH8Y,a,IAIC9P,EAAQgO,EACXhX,IACAlW,OAAO,SACPiB,KAAK,QAAS,SACdhC,MAAK,SAASjC,G,GACN/J,MAAMoC,KDLF,SAACkS,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,OAAe,SAC1D7D,EACA2E,G,IAGKvC,EAAOY,WAAWl3B,eAAek0B,G,OAC7B,WAAO,E,IAGV4E,EAC6B,WAAjCxC,EAAOY,WAAWhD,GAAM3tB,KACpB+vB,EAAOY,WAAWhD,GAAMsD,OAAO3C,QAC7ByB,EAAOY,WAAWhD,GAAMsD,OAAO3C,QAAQz3B,OAAS,GAElDk5B,EAAOY,WAAWhD,GAAMsD,OAAO3C,QAAQ,GAEvCkE,EAASlQ,KAAkBG,OAAO,CAAC,EAAE,GAAI,GAAI,CAAC,GAAI8P,KAElDE,EAAwB,SAAAhyB,G,IACtBiyB,EAAa97B,MAAMU,UAAUiC,MAAMb,KAAK+H,GACxCktB,EAAO+E,EAAW,GAElB3B,EAAM3O,GAAesQ,EAAW,GAAG,KAAO,GAG5CzB,EAAS,KACTlB,EAAOY,WAAWl3B,eAAek0B,K,EAC1BoC,EAAOY,WAAWhD,GAAMsD,Q,IAI7BC,EAASjB,GAAcc,EAAKE,G,MAE3B,C,KACCyB,EAAW,G,KACXA,EAAW,GAAG,G,UACT,C,2BAQZ/yB,GAAG,SAAS,WACe,OAAtBd,GAAM6B,c,EACDhI,KACL,aACA63B,EACAR,EAAOoC,QACPM,EAAsBh4B,YAEyB,oBAAtCoE,GAAM6B,YAAYiyB,iB,GACrBjyB,YAAYiyB,kB,IAIvBhzB,GAAG,SAAS,W,GAETowB,EACAQ,EACA0B,EACAQ,EAAsBh4B,W,CACtB82B,GAAS3mB,EAAOmlB,EAAQyB,EAAxBD,G,IAEH5xB,GAAG,OAAO,W,GACIowB,EAAQQ,EAAI0B,E,CAAQV,GAAS3mB,EAAOmlB,EAAQyB,EAAxBD,I,EAC1B74B,KACL,WACA63B,EACAR,EAAOoC,QACPM,EAAsBh4B,W,MAItBg2B,QAAQ9C,GAAQ6E,E,EAChB9B,WAAW/C,GAAQ2E,EAAUr6B,OAE5Bu6B,C,ECxEDI,CAAShoB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,EAApCoB,CAAgDvyB,EAAGuB,GAAOtL,O,aAI7D0L,UAAU,QACVlH,MAAM,aAAc,MACpBwJ,KAAK,KAAM,IACXA,KAAK,QAAS,I,EAEXtC,UAAU,mBAAmBlH,MAAM,OAAQ,e,EAG9CkH,UAAU,eACVlH,MAAM,OAAQ,0BACdA,MAAM,SAAU,mB,EAEbkH,UAAU,gBAAgBlH,MAAM,OAAQ,mB,EAE3Cw1B,aAAeA,GAAa1lB,EAAOmlB,EAAQQ,G,EAC3CsC,WClCc,SAACjoB,EAAOmlB,EAAQQ,G,OAAO,SAAAqB,G,IAChCnB,EAAY7lB,EAAZ6lB,aAEUnnB,IAAdsoB,G,EACKO,SAAU,OACF7oB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,M,EAC1BA,IACAvX,UAAU,UACVM,MAAK,SAASjC,QACMiJ,IAAfmnB,EAAQpwB,I,GACH/J,MAAMoC,KAAK+3B,EAAQpwB,GAAGklB,KAAM,K,MAGtC+L,mB,EAGEa,SAAU,OACF7oB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,M,EAC1BA,IACAvX,UAAU,UACVM,MAAK,SAASjC,GACTA,IAAMuxB,I,GACHt7B,MAAMoC,KAAK+3B,EAAQpwB,GAAGklB,KAAM,MACJ,oBAApBkL,EAAQpwB,GAAGL,M,EACZK,GAAGxB,MAAM+C,GAAOtL,O,MAG3Bg7B,iB,EDOSuB,CAAWjoB,EAAOmlB,EAAQQ,GACnCA,C,GErCHuC,GAAY,SAACloB,EAAO2lB,G,OAAO,gBAChBjnB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,KAC7BgX,EAAGhX,IACAvX,UAAU,UACVvF,S,EAECg0B,QAAU,CAAC,SACVF,EAAGD,oBACHC,EAAGsC,U,GCNNE,GAAc,SAACtC,EAASV,EAAQQ,EAAI5C,EAAMt3B,G,IACxC28B,EAAiB38B,EAAS2L,UAAU,UAAUhK,KAAKy4B,GAAS,SAAApwB,G,OAAKA,EAAErK,E,MAGtE8B,QACAgN,OAAO,IAAK,UACZR,KAAK,QAAS,SACdA,KAAK,YAAaqpB,GAClBrpB,KACC,MACA,SAAAzL,G,MAAK,SAAWsV,OAAOwU,KAAKoN,EAAOY,YAAYr3B,QAAQq0B,GAAQ,IAAM90B,EAAE7C,E,IAExEsM,MAAK,SAAS2wB,G,EACD1Q,MAAM3gB,GAAOtL,M,MAGdgM,MAAK,SAAS2wB,G,GACpB38B,MACJgO,KAAK,QAAS,SACdtC,UAAU,YACVlH,MAAM,kBAAkB,W,IACjBynB,EAAQ0Q,EAAY1Q,M,OACtB0Q,EAAYj9B,KAAOy6B,EAAQ55B,OAAS,QAAeyS,IAAViZ,EACpC,MAEA,M,SAKAxqB,OAAO0E,Q,EC7BlBy2B,GAAW,SAACtoB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,IACnCf,EAAY7lB,EAAZ6lB,QAgBFgB,EAAUtjB,OAAOwU,KAAKoN,EAAOY,YAAYzuB,QAd5B,SAACilB,EAAGgM,G,QACfC,EAAc3C,EAAQtJ,GAEnBjvB,EAAI,EAAGA,EAAIk7B,EAAYv8B,OAAQqB,IAAK,C,IACrCqqB,EAAQ3nB,SAASy4B,eAAe,SAAWF,EAAM,IAAMj7B,G,GAEzDqqB,GAAmC,OAA1BH,GAAeG,G,OACnB,C,QAIJ,C,IAIHiO,EAAUiB,EAAQzuB,KAAI,SAAAmkB,G,OACNsJ,EAAQtJ,GAGzBjlB,QAAO,SAAA7B,G,OAAMkwB,EAAG+C,WAAWC,SAASlzB,E,IACpC2C,KAAI,SAAC3C,EAAGnI,G,OACPkqB,GACExnB,SAASy4B,eACP,SAAWllB,OAAOwU,KAAKoN,EAAOY,YAAYr3B,QAAQ6tB,GAAK,IAAMjvB,G,IAIlE8K,KAAI,SAAC3C,EAAGnI,G,OACG,OAANmI,QAAoBiJ,IAANjJ,EACT,KACgD,oBAAvC0vB,EAAOY,WAAWxJ,GAAG8J,OAAOf,OACrC,CACLH,EAAOY,WAAWxJ,GAAG8J,OAAOf,OAAO7vB,EAAE,IACrC0vB,EAAOY,WAAWxJ,GAAG8J,OAAOf,OAAO7vB,EAAE,KAGhCA,C,UAWQ,IAAnBoxB,EAAQ56B,OAAc,OAAOk5B,EAAO/3B,K,IAGlC25B,EAAS,C,KACP,SAACtxB,EAAG8mB,EAAGjvB,G,IACLs7B,EAAShD,EAAQt4B,G,GAE8B,oBAA1C63B,EAAOY,WAAWxJ,GAAG8J,OAAOzD,UAA0B,C,mCAE/DiG,E,EAAgBD,EAAhB3T,OAAAC,cAAA4T,GAAAD,EAAAE,EAAAh8B,QAAAi8B,MAAAF,GAAA,EAAwB,C,IAAbrqB,EAAaoqB,EAAAv5B,M,GACZ,OAANmP,QAAoBC,IAAND,IAKhBA,EAAE,IAAM0mB,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KACtC4I,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KAAO9d,EAAE,I,OAEhC,C,sFAIJ,C,oCAEPwqB,E,EAAgBL,EAAhB3T,OAAAC,cAAAgU,GAAAD,EAAAE,EAAAp8B,QAAAi8B,MAAAE,GAAA,EAAwB,C,IAAbE,EAAaH,EAAA35B,M,GACZ,OAAN85B,QAAoB1qB,IAAN0qB,IAIdA,EAAE,IAAM3zB,EAAE8mB,IAAM9mB,EAAE8mB,IAAM6M,EAAE,I,OACrB,C,sFAIJ,C,SAGH,SAAC3zB,EAAG8mB,EAAGjvB,G,IACPs7B,EAAShD,EAAQt4B,G,GAE8B,oBAA1C63B,EAAOY,WAAWxJ,GAAG8J,OAAOzD,UAA0B,C,mCAE/DyG,E,EAAgBT,EAAhB3T,OAAAC,cAAAoU,GAAAD,EAAAE,EAAAx8B,QAAAi8B,MAAAM,GAAA,EAAwB,C,IAAb7qB,EAAa4qB,EAAA/5B,M,GACZ,OAANmP,QAAoBC,IAAND,IAKhBA,EAAE,IAAM0mB,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KACtC4I,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KAAO9d,EAAE,I,OAEhC,C,sFAIJ,C,oCAEP+qB,E,EAAgBZ,EAAhB3T,OAAAC,cAAAuU,GAAAD,EAAAE,EAAA38B,QAAAi8B,MAAAS,GAAA,EAAwB,C,IAAbE,EAAaH,EAAAl6B,M,GACZ,OAANq6B,QAAoBjrB,IAANirB,IAIdA,EAAE,IAAMl0B,EAAE8mB,IAAM9mB,EAAE8mB,IAAMoN,EAAE,I,OACrB,C,sFAIJ,C,SAGH,SAACl0B,EAAG8mB,EAAGjvB,G,IACPs7B,EAAShD,EAAQt4B,G,+BAEvBs8B,E,EAAgBhB,EAAhB3T,OAAAC,cAAA2U,GAAAD,EAAAE,EAAA/8B,QAAAi8B,MAAAa,GAAA,EAAwB,C,IAAbprB,EAAamrB,EAAAt6B,M,GACZ,OAANmP,QAAoBC,IAAND,IAKhBA,EAAE,IAAM0mB,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KACtC4I,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,KAAO9d,EAAE,I,OAEhC,C,sFAIJ,C,UAIJ0mB,EAAO/3B,KAAKkK,QAAO,SAAA7B,G,OAChBmxB,EAAWK,W,IACZ,M,OACIJ,EAAQ7S,OAAM,SAACuI,EAAGjvB,G,OACvBy5B,EAAO5B,EAAOY,WAAWxJ,GAAGnnB,MAAMK,EAAG8mB,EAAGjvB,E,QAEvC,K,OACIu5B,EAAQK,MAAK,SAAC3K,EAAGjvB,G,OACtBy5B,EAAO5B,EAAOY,WAAWxJ,GAAGnnB,MAAMK,EAAG8mB,EAAGjvB,E,kBAGpC,IAAIoP,MAAM,2BAA6ByoB,EAAOgC,gB,KCrJtD4C,GAAe,SAAC5E,EAAQQ,EAAI0B,G,OAAW,SAAAC,G,EACpCC,QAAUD,E,EACVx5B,KAAK,QAAS63B,EAAIR,EAAOoC,S,EAC7Bb,e,GAGCsD,GAAW,SAAXA,EAAYhqB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,OAAe,SAC1D7D,EACA2E,G,IAEQ7B,EAAwB7lB,EAAxB6lB,QAASC,EAAe9lB,EAAf8lB,WAEX6B,EAC6B,WAAjCxC,EAAOY,WAAWhD,GAAM3tB,KACpB+vB,EAAOY,WAAWhD,GAAMsD,OAAO3C,QAC7ByB,EAAOY,WAAWhD,GAAMsD,OAAO3C,QAAQz3B,OAAS,GAElDk5B,EAAOY,WAAWhD,GAAMsD,OAAO3C,QAAQ,GAEvC/L,EAAQD,KAASG,OAAO,CAAC,EAAE,GAAI,GAAI,CAAC,GAAI8P,KACxCv8B,EAAKy6B,EAAQ9C,GAAQ8C,EAAQ9C,GAAM92B,OAAS,EAC5CoB,EACJ,SAAWkW,OAAOwU,KAAKoN,EAAOY,YAAYr3B,QAAQq0B,GAAQ,IAAM33B,E,OAE9Dy6B,EAAQ9C,G,EACFA,GAAMnvB,KAAK,C,wBAMXmvB,GAAQ,CAAC,CAAE33B,GAAAA,EAAIusB,MAAAA,EAAOtqB,KAAAA,IAG5By4B,EAAW/C,G,EACFA,GAAMnvB,KAAK,CAAExI,GAAAA,EAAIiC,KAAAA,I,EAEjB01B,GAAQ,CAAC,CAAE33B,GAAAA,EAAIiC,KAAAA,I,EAIzB0H,GAAG,SAAS,WACe,OAAtBd,GAAM6B,c,EACDhI,KAAK,aAAc63B,EAAIR,EAAOoC,SACY,oBAAtCtzB,GAAM6B,YAAYiyB,iB,GACrBjyB,YAAYiyB,kB,IAIvBhzB,GAAG,SAAS,SAAS0J,G,GAGlB0mB,EACAQ,EACA0B,E,CACAiB,GAAStoB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,IAEvC7xB,GAAG,OAAO,W,IAEHk1B,EAAcpE,EAAQ9C,GAAM8C,EAAQ9C,GAAM92B,OAAS,GAAGb,GAOtD2L,EAAYygB,GANAxnB,SAASy4B,eACzB,SACEllB,OAAOwU,KAAKoN,EAAOY,YAAYr3B,QAAQq0B,GACvC,IACAkH,SAKYvrB,IAAd3H,GACc,OAAdA,GACAA,EAAU,KAAOA,EAAU,I,EAElBiJ,EAAOmlB,EAAQQ,EAAI0B,EAAQT,E,CAAY7D,EAAM2E,G,GAE1C7B,EAAQ9C,GAAOoC,EAAQQ,EAAI5C,EAAM2E,G,GAEhCvC,EAAQQ,EAAI0B,E,CACvBiB,GAAStoB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,KAIpC3yB,GAAM6B,aAC2B,wBAAjC7B,GAAM6B,YAAYsZ,YACE,OAApBnb,GAAM8C,W,EAEHkxB,WAAWlF,G,EAIXj1B,KAAK,WAAY63B,EAAIR,EAAOoC,Q,IAGhC5P,C,GCnFHuS,GAAe,SAAClqB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,OAAe,SAAAhB,G,IACtDC,EAAY7lB,EAAZ6lB,QACFsE,EAAaxE,EAAG+C,W,MAEC,qBAAZ9C,EACFriB,OAAOwU,KAAKoN,EAAOY,YACvBzuB,QAAO,SAAA7B,G,OAAM00B,EAAWxB,SAASlzB,E,IACjCuwB,QAAO,SAACC,EAAKC,EAAKqC,G,IACXC,EAAc3C,EAAQK,G,SAGtBA,QADcxnB,IAAhB8pB,GAA6C,OAAhBA,EACpB,GAEAA,EAAYxC,QAAO,SAACvwB,EAAG8mB,EAAGjvB,G,IAC7B64B,EAAM3O,GACVxnB,SAASy4B,eAAe,SAAWF,EAAM,IAAMj7B,I,GAG7C64B,EAAK,C,IACDC,EAASjB,EAAOY,WAAWG,GAAKG,OAChCC,EAASjB,GAAcc,EAAKC,G,EAEhCxyB,KAAK,C,OACG2oB,EAAE5E,MAAME,S,UACL,C,yBAMRpiB,C,GACN,IAGEwwB,C,GACN,CAAC,I,OAIClO,KAAKoN,EAAOY,YAAYP,SAAQ,SAAC/vB,EAAG8yB,G,QACtB7pB,IAAfknB,EAAQnwB,IAAmC,OAAfmwB,EAAQnwB,G,KAIlCgiB,EAAM0N,EAAOY,WAAWtwB,GAIxB20B,EAFWxE,EAAQnwB,GAAG2C,KAAI,SAAAqG,G,OAAKA,EAAErG,IAAIqf,EAAI4O,O,IAE1BjuB,KAAI,SAACqG,EAAGvJ,G,IACrB0yB,EAASoC,GAAShqB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,EAApCoD,CACbv0B,EACAuB,GAAO,gBAAkBuxB,I,SAIpB1Q,OAAO,CAAC,EAAE,GAAIpZ,EAAE,IAAK,CAAC,GAAIA,EAAE,MAE5B,C,GACDvJ,E,MACG0yB,E,IACFnpB,E,MAIDhJ,GAAK20B,E,GAEDA,EAAKjF,EAAQQ,EAAIlwB,EAAGuB,GAAO,gBAAkBuxB,I,EAKrD/C,SAAQ,SAACznB,EAAGwS,G,GACP,UAAYgY,EAAM,IAAMhY,GAC5BziB,KAAKiQ,EAAE4Z,OACP7pB,KAAKiQ,EAAE4Z,MAAMgD,KAAM5c,EAAEssB,IAAI5D,U,UAK7BC,gBAEIf,E,GC3FL2E,GAAU,SAACtqB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,OAAe,WACpDjB,EAAGhX,K,EACH8Y,a,IAGC0C,EAAaxE,EAAG+C,W,SAEnB/Z,IACAlW,OAAO,SACPiB,KAAK,MAAM,SAACjE,EAAGnI,G,MAAM,eAAiBA,C,IACtCoM,KAAK,QAAS,eACdA,KAAK,aAAa,SAAAjE,G,OAAKA,C,IACvBiC,MAAK,SAASjC,GACR00B,EAAWxB,SAASlzB,ICfd,SAACuK,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,OAAe,SAC1D7D,EACA2E,G,IAEQ7B,EAAY7lB,EAAZ6lB,Q,GACC7lB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,E,CAAY7D,EAAM2E,G,GAC1C7B,EAAQ9C,GAAOoC,EAAQQ,EAAI5C,EAAM2E,E,GDU9B1nB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,E,CAAYnxB,EAAGuB,GAAOtL,M,MAI7Dg6B,aAAewE,GAAalqB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,EACvDqB,WErBc,SAACjoB,EAAOmlB,EAAQQ,G,OAAO,SAAAqB,G,IAChCnB,EAAY7lB,EAAZ6lB,Q,QAEUnnB,IAAdsoB,OACatoB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,M,OACtBoJ,KAAKoN,EAAOY,YAAYP,SAAQ,SAAC/vB,EAAG8yB,G,IACnCgC,EAAY1E,EAAQpwB,GAGtB80B,G,EACQ/E,SAAQ,SAAC/mB,EAAGnR,G,IACdqqB,EAAQ3nB,SAASy4B,eAAe,SAAWF,EAAM,IAAMj7B,GACzDqqB,GAAmC,OAA1BH,GAAeG,I,EACvBhJ,IACA3X,OAAO,UAAYuxB,EAAM,IAAMj7B,GAC/BQ,KAAK2Q,EAAEkZ,MAAMgD,KAAM,K,SAM3B+L,sB,QAGUhoB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,IAAc,C,IACrC4b,EAAY1E,EAAQmB,GACpBuB,EAAMhlB,OAAOwU,KAAKoN,EAAOY,YAAYr3B,QAAQs4B,GAE/CuD,G,EACQ/E,SAAQ,SAAC/mB,EAAGnR,GAEU,OAA1BkqB,GADUxnB,SAASy4B,eAAe,SAAWF,EAAM,IAAMj7B,M,EAExDqhB,IACA3X,OAAO,UAAYuxB,EAAM,IAAMj7B,GAC/BQ,KAAK2Q,EAAEkZ,MAAMgD,KAAM,MAEC,oBAAZlc,EAAExK,O,EACTA,MAAM+C,GAAO,UAAYuxB,EAAM,IAAMj7B,I,MAM5Co5B,e,GFtBS8D,CAAWxqB,EAAOmlB,EAAQQ,GACnCA,C,GGzBH8E,GAAY,SAACzqB,EAAO2lB,G,OAAO,gBAChBjnB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,KAC7BgX,EAAGhX,IACAvX,UAAU,gBACVvF,S,EAECg0B,QAAU,CAAC,SACVF,EAAGD,oBACHC,EAAGsC,U,GCRNyC,GAAY,SAAC1qB,EAAO2lB,G,OAAO,W,EAC5B5uB,UACAC,OAAO,OACPA,OAAO,YACPnF,S,EACAkF,UACAC,OAAO,OACPA,OAAO,qBACPnF,S,EACAkD,GAAG,0BAAsB2J,UACrBinB,EAAGsC,W,EAEJ0C,eAAYjsB,C,GCKdksB,GAAe,SAAC5qB,EAAOmlB,G,OAAW,SAAC1vB,EAAGrK,G,IACtCy/B,EAAQ7qB,EAAM8qB,OAAO1/B,GACvBqR,EAlBoB,SAACouB,EAAO3T,G,OAAU,SAAAqF,G,IAClCyE,EAAK,CAAC6J,EAAM7J,GAAG,GAAK6J,EAAME,KAAMF,EAAM7J,GAAG,GAAK6J,EAAME,MACxDC,EAAK,CAACH,EAAMG,GAAG,GAAKH,EAAME,KAAMF,EAAMG,GAAG,GAAKH,EAAME,MACpDhyB,EAAK,EAAIme,EAAQ8J,EAAG,GACpBiK,EAAKjK,EAAG,IAAM,EAAIjoB,GAClB4W,EAAK,EAAIuH,EAAQ8T,EAAG,GACpBE,EAAKF,EAAG,IAAM,EAAIrb,GAEdhY,EAAI4kB,EAAE,GACVxhB,EAAIwhB,EAAE,GACNyB,EAAKjlB,EAAKpB,EAAIszB,EACd9J,EAAKxR,EAAKhY,EAAIuzB,E,OAETnwB,EAAI7P,KAAK8N,IAAIglB,EAAImD,IAAOpmB,EAAI7P,KAAKC,IAAI6yB,EAAImD,E,EAKvCgK,CAAgBN,EAAO7qB,EAAM8qB,OAAO5T,MAAM9rB,IACjDggC,EAAKP,EAAMQ,KAAK/vB,KAChByjB,EAAK8L,EAAMQ,KAAK/I,MAChBtE,EAAKmH,EAAOY,WAAWqF,GAAI/E,OAC3BlF,EAAKgE,EAAOY,WAAWhH,GAAIsH,O,OAEtB5pB,EADG,CAACuhB,EAAGvoB,EAAE21B,IAAOP,EAAME,KAAM5J,EAAG1rB,EAAEspB,IAAO8L,EAAME,M,GAIjDO,GAAW,SAAC1E,EAAY5mB,EAAOmlB,G,IAE7BoG,EAAMhoB,OAAOioB,oBAAoBxrB,EAAM8qB,QAAQxzB,QAAO,SAAA7B,G,OAAMyZ,MAAMzZ,E,IACtE8xB,EAAUpC,EAAO/3B,K,GAEA,IAAfm+B,EAAIt/B,O,OACCs7B,E,IAGHkE,EAAYb,GAAa5qB,EAAOmlB,G,OAE/BoC,EAAQjwB,QAAO,SAAA7B,G,OACZmxB,EAAWK,W,IACZ,M,OACIsE,EAAIvX,OAAM,SAAA5oB,G,OAAMqgC,EAAUh2B,EAAGrK,E,QACjC,K,OACImgC,EAAIrE,MAAK,SAAA97B,G,OAAMqgC,EAAUh2B,EAAGrK,E,kBAE7B,IAAIsR,MAAM,2BAA6ByoB,EAAOgC,gB,KC9CtDuE,GAAc,SAAC1rB,EAAO2lB,G,IACpBkF,EAAQ7qB,EAAM8qB,OAAO9qB,EAAM8qB,OAAOxqB,QACtClS,EAAMu3B,EAAG5uB,UAAUC,OAAO,OAAOA,OAAO,mBAEnCgJ,EAAM8qB,OAAO9qB,EAAM8qB,OAAOxqB,Q,EAC7BlJ,UAAU,cAAgByzB,EAAMQ,KAAK/9B,GAAGuE,S,EACxCuF,UAAU,gBAAkByzB,EAAMQ,KAAK/9B,GAAGuE,Q,ECH1C85B,GAAY,SAAC/E,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,OAAW,W,IACrDwD,EAAQ7qB,EAAM8qB,OAAO9qB,EAAM8qB,OAAOxqB,QAIpCuqB,GAASA,EAAM7J,GAAG,KAAO6J,EAAMG,GAAG,IAAMH,EAAM7J,GAAG,KAAO6J,EAAMG,GAAG,I,GACvDhrB,EAAO2lB,G,IAGf4B,EAAU+D,GAAS1E,EAAY5mB,EAAOmlB,G,EACtC2F,OAAOxqB,YAAS5B,E,EACf6oB,QAAUA,E,EACdb,gB,EACI54B,KAAK,WAAY63B,EAAIR,EAAOoC,Q,GCZ/BqE,GAAY,SAAZA,EACJhF,EACA5mB,EACAmlB,EACAQ,EACA0B,EACAwD,EACAgB,G,IAEIC,EAAOnG,EAAG5uB,UAAUC,OAAO,OAAOA,OAAO,YAC3C5L,EAAKy/B,EAAMQ,KAAK/9B,EAChBy+B,EAAS,CAAClB,EAAM7J,GAAI6J,EAAMG,IAC1BgB,EAAQF,EAAK10B,UAAU,cAAgBhM,GAAIgC,KAAK,CAACy9B,IACjDoB,EAAUH,EAAK10B,UAAU,gBAAkBhM,GAAIgC,KAAK2+B,GACpDG,EAAQlQ,K,EAGP9uB,QACAuL,OAAO,QACPiB,KAAK,KAAM,SAAWtO,GACtBsO,KAAK,QAAS,S,EAGdA,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEurB,GAAG,E,IACrBtnB,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEurB,GAAG,E,IACrBtnB,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEu1B,GAAG,E,IACrBtxB,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEu1B,GAAG,E,IACrBtxB,KAAK,SAAU,SACfA,KAAK,eAAgB,G,EAGrB3E,GAAG,QAAQ,SAASU,EAAGnI,G,IAChB6+B,EAAKl4B,G,EAEL,K,GADE,IACO,GAAK/I,KAAK8N,IAAI9N,KAAKC,IAAI0/B,EAAME,KAAO,EAAGoB,EAAGx0B,GAAIkzB,EAAMuB,M,EAC7D,IAAM9+B,GAAG,GAAKpC,KAAK8N,IAAI9N,KAAKC,IAAI0/B,EAAMwB,KAAMF,EAAGpxB,GAAI8vB,EAAMyB,M,EACrD1F,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQwD,EAAOv9B,EAAI,E,IAE7DyH,GAAG,MAAO42B,GAAU/E,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,I,EAGnDn6B,QACAuL,OAAO,UACPiB,KAAK,KAAM,SAAWtO,GACtBsO,KAAK,QAAS,S,EAGdA,KAAK,MAAM,SAAAjE,G,OAAKA,EAAE,E,IAClBiE,KAAK,MAAM,SAAAjE,G,OAAKA,EAAE,E,IAClBiE,KAAK,IAAK,GACVxJ,MACC,WACA,SAACuF,EAAGnI,G,YAAuBoR,IAAhBmtB,GAA6Bv+B,IAAMu+B,EAAc,GAAM,C,IAEnE92B,GAAG,aAAa,W,GACRrJ,MAAMwE,MAAM,UAAW,G,IAE/B6E,GAAG,YAAY,W,GACPrJ,MAAMwE,MAAM,UAAW,E,IAE/BpC,KAAKo+B,E,EAGJK,GAAS,SAAC3F,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,OAAW,W,IAClD8E,EAAKl4B,GACT42B,EAAQ7qB,EAAM8qB,OAAO9qB,EAAM8qB,OAAOxqB,Q,EAG9B0qB,GAAG,GAAK9/B,KAAK8N,IACjB9N,KAAKC,IAAI0/B,EAAME,KAAO,EAAGoB,EAAGx0B,EAAIwtB,EAAOC,OAAO9pB,MAC9CuvB,EAAMuB,M,EAEFpB,GAAG,GAAK9/B,KAAK8N,IACjB9N,KAAKC,IAAI0/B,EAAMwB,KAAMF,EAAGpxB,EAAIoqB,EAAOC,OAAO5pB,KAC1CqvB,EAAMyB,M,GAGE1F,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQwD,EAAO,E,GCjFpDxb,GAAI,SAAA8V,G,OAAUA,EAAOhO,OAASgO,EAAOC,OAAO5pB,IAAM2pB,EAAOC,OAAOoH,M,ECAhEC,GAAqB,SAACtH,EAAQQ,EAAI+G,EAAQnQ,G,IACxC8O,EAAO,CAAE/9B,GAAI,EAAGgO,UAAMoD,EAAW4jB,WAAO5jB,G,cACvCqZ,KAAKoN,EAAOY,YAAYmB,MAAK,SAACzP,EAAKnqB,G,QACpCo/B,EAAOjV,GAAO8E,EAAE,M,EACbjvB,EAAIA,E,EACJgO,KAAOmc,E,EACP6K,MAAQ/e,OAAOwU,KAAKoN,EAAOY,YAC9BJ,EAAGgH,0BAA0Bj+B,QAAQ+oB,GAAO,IAEvC,E,SAKO/Y,IAAd2sB,EAAK/vB,M,EAEFhO,EAAI,E,EACJgO,KAAOqqB,EAAGgH,0BAA0B,G,EACpCrK,MAAQqD,EAAGgH,0BAA0B,SAClBjuB,IAAf2sB,EAAK/I,Q,EAETh1B,EAAIiW,OAAOwU,KAAKoN,EAAOY,YAAY95B,OAAS,E,EAC5Cq2B,MAAQ+I,EAAK/vB,K,EACbA,KAAOqqB,EAAGgH,0BACbppB,OAAOwU,KAAKoN,EAAOY,YAAY95B,OAAS,IAIrCo/B,C,EC3BHuB,GAAc,SAAA7G,G,OAAc,SAAC8G,EAAOC,G,IAClCC,EAAOxpB,OAAOwU,KAAKgO,G,OAElBgH,EAAK7F,MACV,SAACzxB,EAAGnI,G,OACFmI,IAAMo3B,IAAQv/B,EAAIA,EAAIy/B,EAAK9gC,QAAU85B,EAAWz4B,EAAI,KAAOw/B,E,MCI3DE,GAAU,SAACpG,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQqF,G,OAAW,gBAClDhuB,IAAXinB,EAAGhX,KAAgC,OAAXgX,EAAGhX,K,EAC1B8Y,a,IAGCyE,EAAQlQ,K,EAIR8O,OAAOxqB,YAAS5B,E,EAKhBosB,OAAO5T,MAAQ,SAAA9rB,G,YACEsT,IAArBsB,EAAM8qB,OAAO1/B,QACTsT,EACAsB,EAAM8qB,OAAO1/B,GAAIghC,KAAOpsB,EAAM8qB,OAAO1/B,GAAI2/B,I,IAE5Ch2B,GAAG,sBAAsB,W,IACpBw2B,EAAMhoB,OAAOioB,oBAAoBxrB,EAAM8qB,QAAQxzB,QAAO,SAAA7B,G,OAAMyZ,MAAMzZ,E,IAEpE81B,EAAIt/B,OAAS,I,EAEXu5B,SAAQ,SAAA/vB,G,IACJ41B,EAAOrrB,EAAM8qB,OAAOr1B,GAAG41B,K,EACvBP,OAAOxqB,OAAS7K,EAGjBm3B,GAAYzH,EAAOY,WAAnB6G,CAA+BvB,EAAK/vB,KAAM+vB,EAAK/I,Q,GACtCtiB,EAAO2lB,E,OAGbiB,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,E,SAK1CtwB,UACAC,OAAO,OACPyB,OAAO,KACPiB,KAAK,KAAM,UACXA,KACC,YACA,aAAeyrB,EAAOC,OAAO9pB,KAAO,IAAM6pB,EAAOC,OAAO5pB,IAAM,K,EAI/DysB,WCvDc,SAACrB,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,OAAW,WAChD9jB,OAAOioB,oBAAoBxrB,EAAM8qB,QAAQxzB,QAAO,SAAA7B,G,OAAMyZ,MAAMzZ,E,IAEpE+vB,SAAQ,SAAA/vB,G,EACJq1B,OAAOxqB,OAAS7K,E,GACVuK,EAAO2lB,E,OAEXiB,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,E,IDgDzB4F,CAAWrG,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,EAGvDtyB,GAAG,QErDY,SAACiL,EAAOmlB,EAAQQ,EAAI+G,G,OAAW,W,IAC7CnQ,EAAIlC,GAAMra,EAAM2qB,UAAUt9B,Q,EAE5B,GAAKkvB,EAAE,GAAK4I,EAAOC,OAAO9pB,K,EAC1B,GAAKihB,EAAE,GAAK4I,EAAOC,OAAO5pB,I,IAEtB6vB,EAAOoB,GAAmBtH,EAAQQ,EAAI+G,EAAQnQ,GAC9CsO,EAAQ,C,GACRtO,E,KACE8O,E,KACAqB,EAAOrB,EAAK/vB,M,KACZoxB,EAAOrB,EAAK/I,O,KACZ,E,KACAjT,GAAE8V,I,EAIJnE,GAAG,GAAK91B,KAAK8N,IAAI9N,KAAKC,IAAI0/B,EAAME,KAAMxO,EAAE,IAAKsO,EAAMuB,M,EACnDpB,GAAKH,EAAM7J,GAAGryB,Q,EAEdm8B,OAAOO,EAAK/9B,GAAKu9B,E,EACjBC,OAAOxqB,OAAS+qB,EAAK/9B,C,EFgCZ4/B,CAAYltB,EAAOmlB,EAAQQ,EAAI+G,IAC3C33B,GAAG,OAAQw3B,GAAO3F,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,IACjDtyB,GAAG,MAAO42B,GAAU/E,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,I,EAKhDsD,UAAYhF,EAAG5uB,UAClBC,OAAO,OACPkD,OAAO,OAAQ,YACfR,KAAK,KAAM,gBACXA,KAAK,IAAKyrB,EAAOC,OAAO9pB,MACxB5B,KAAK,IAAKyrB,EAAOC,OAAO5pB,KACxB9B,KAAK,QAASyc,GAAEgP,IAChBzrB,KAAK,SAAU2V,GAAE8V,GAAU,GAC3Bj1B,MAAM,UAAW,GACjBpC,KAAKo+B,E,GG7EJiB,GAAY,SAACntB,EAAO2lB,G,OAAO,W,EAC5B5uB,UACAC,OAAO,OACPA,OAAO,UACPnF,S,EACAkF,UACAC,OAAO,OACPA,OAAO,mBACPnF,S,EACAkD,GAAG,wBAAoB2J,UAEnBinB,EAAGsC,W,EAEJ0C,eAAYjsB,C,GCbd0uB,GAAc,SAACp/B,EAAGC,G,OAAM/C,KAAK+V,KAAKjT,EAAIA,EAAIC,EAAIA,E,ECG9Co/B,GAAc,SAAAC,G,OAClBA,EAAQpiC,KAAKuV,GAAK,IAAMvV,KAAKuV,GAAK6sB,EAAQ,GAAMpiC,KAAKuV,GAAK6sB,C,EAsBtDC,GAAe,SAACvtB,EAAOmlB,G,OAAW,SAAC1vB,EAAGrK,G,IACpCoiC,EAAMxtB,EAAMytB,KAAKriC,GACrBqR,EAhBoB,SAAA+wB,G,OAAO,SAAAx/B,G,IACzBuvB,EAAa8P,GAAYG,EAAIjQ,YAC7BE,EAAW4P,GAAYG,EAAI/P,U,GAE3BF,EAAaE,EAAU,C,IACnBiQ,EAAMnQ,E,EACCE,E,EACFiQ,C,QAIN1/B,GAAKuvB,GAAcvvB,GAAKyvB,C,EAKtBkQ,CAAgBH,GACvBpC,EAAKoC,EAAInC,KAAK/vB,KACdyjB,EAAKyO,EAAInC,KAAK/I,MACdtE,EAAKmH,EAAOY,WAAWqF,GAAI/E,OAC3BlF,EAAKgE,EAAOY,WAAWhH,GAAIsH,OAC3Br4B,EAAIgS,EAAMytB,KAAKvW,MAAM9rB,GACrB6C,EAAI+vB,EAAGvoB,EAAE21B,IAAOjK,EAAG1rB,EAAEspB,IACrBliB,EAAIuwB,GAAYp/B,EAAGC,G,OAEdwO,EADGvR,KAAK+xB,KAAKhvB,EAAI4O,G,GAIpB+wB,GAAW,SAAChH,EAAY5mB,EAAOmlB,G,IAC7BoG,EAAMhoB,OAAOioB,oBAAoBxrB,EAAMytB,MAAMn2B,QAAO,SAAA7B,G,OAAMyZ,MAAMzZ,E,IAChE8xB,EAAUpC,EAAO/3B,K,GAEJ,IAAfm+B,EAAIt/B,O,OACCs7B,E,IAGHkE,EAAY8B,GAAavtB,EAAOmlB,G,OAE/BoC,EAAQjwB,QAAO,SAAA7B,G,OACZmxB,EAAWK,W,IACZ,M,OACIsE,EAAIvX,OAAM,SAAA5oB,G,OAAMqgC,EAAUh2B,EAAGrK,E,QACjC,K,OACImgC,EAAIrE,MAAK,SAAA97B,G,OAAMqgC,EAAUh2B,EAAGrK,E,kBAE7B,IAAIsR,MAAM,2BAA6ByoB,EAAOgC,gB,KCzDtD0G,GAAc,SAAC7tB,EAAO2lB,G,IACpB6H,EAAMxtB,EAAMytB,KAAKztB,EAAMytB,KAAKntB,QAChClS,EAAMu3B,EAAG5uB,UAAUC,OAAO,OAAOA,OAAO,iBAEnCgJ,EAAMytB,KAAKztB,EAAMytB,KAAKntB,Q,EACvBmtB,KAAKntB,YAAS5B,E,EAChBtH,UAAU,YAAco2B,EAAInC,KAAK/9B,GAAGuE,S,EACpCuF,UAAU,cAAgBo2B,EAAInC,KAAK/9B,GAAGuE,S,EACtCuF,UAAU,YAAco2B,EAAInC,KAAK/9B,GAAGuE,Q,ECLpCi8B,GAAY,SAAClH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,OAAW,W,IACrDmG,EAAMxtB,EAAMytB,KAAKztB,EAAMytB,KAAKntB,Q,GAI9BktB,GAAOA,EAAIxM,GAAG,KAAOwM,EAAIxC,GAAG,IAAMwC,EAAIxM,GAAG,KAAOwM,EAAIxC,GAAG,I,GAC7ChrB,EAAO2lB,GAGjB6H,EAAK,C,IACDF,EAAQttB,EAAMytB,KAAKlQ,WAAWvd,EAAMytB,KAAKntB,Q,EAE3Cid,WAAa+P,E,EACb7P,SAAW6P,E,EACXxN,IACDzC,YAAYrd,EAAMytB,KAAKxhC,OAAO+T,EAAMytB,KAAKntB,SACzCid,WAAW+P,GACX7P,SAAS6P,E,GAGRG,KAAKntB,YAAS5B,E,EACb6oB,QAAUqG,GAAShH,EAAY5mB,EAAOmlB,G,EAC1CuB,gB,EACI54B,KAAK,WAAY63B,EAAIR,EAAOoC,Q,GCtB/BwG,GAAY,SAAZnC,EAAahF,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQmG,EAAK3B,G,IACvDz9B,EAAMu3B,EAAG5uB,UAAUC,OAAO,OAAOA,OAAO,UAC5C5L,EAAKoiC,EAAInC,KAAK/9B,EACdy+B,EAAS,CAACyB,EAAIxC,GAAIwC,EAAIQ,IACtBhC,EAAQ59B,EACLgJ,UAAU,YAAchM,GACxBgC,KAAK,CAAC,CAAE4zB,GAAIwM,EAAIxM,GAAIgK,GAAIwC,EAAIxC,IAAM,CAAEhK,GAAIwM,EAAIxM,GAAIgK,GAAIwC,EAAIQ,MAC3D/B,EAAU79B,EAAIgJ,UAAU,cAAgBhM,GAAIgC,KAAK2+B,GACjDG,EAAQlQ,KACRiS,EAAQ7/B,EAAIgJ,UAAU,YAAchM,GAAIgC,KAAK,CAACogC,I,EAG7CtgC,QACAuL,OAAO,QACPiB,KAAK,KAAM,OAAStO,GACpBsO,KAAK,QAAS,OACdxJ,MAAM,OAAQ,UACdA,MAAM,UAAW,I,EAGjBwJ,KAAK,IAAK8zB,EAAI1N,KACdpmB,KAAK,YAAa,aAAe8zB,EAAIxM,GAAG,GAAK,IAAMwM,EAAIxM,GAAG,GAAK,K,EAG/D9zB,QACAuL,OAAO,QACPiB,KAAK,KAAM,OAAStO,GACpBsO,KAAK,QAAS,O,EAGdA,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEurB,GAAG,E,IACrBtnB,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEurB,GAAG,E,IACrBtnB,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEu1B,GAAG,E,IACrBtxB,KAAK,MAAM,SAAAjE,G,OAAKA,EAAEu1B,GAAG,E,IACrBtxB,KAAK,SAAU,SACfA,KAAK,eAAgB,G,EAGrB3E,GAAG,QAAQ,SAACU,EAAGnI,G,IACR6+B,EAAKl4B,G,EAGP,K,GAFI,IAEK,GAAK/I,KAAK8N,IAAI9N,KAAKC,IAAIqiC,EAAIzC,KAAO,EAAGoB,EAAGx0B,GAAI61B,EAAIpB,M,EACzD,IAAM9+B,GAAG,GAAKpC,KAAK8N,IAAI9N,KAAKC,IAAIqiC,EAAInB,KAAMF,EAAGpxB,GAAIyyB,EAAIlB,M,IAEnDgB,EACE,IAANhgC,EAAU0S,EAAMytB,KAAKlQ,WAAWnyB,GAAM4U,EAAMytB,KAAKhQ,SAASryB,IAGzDoiC,EAAIjQ,WAAaryB,KAAKuV,IACrB+sB,EAAI/P,SAAWvyB,KAAKuV,IACpB6sB,EAAQpiC,KAAKuV,IACd+sB,EAAIjQ,YAAcryB,KAAKuV,IACtB+sB,EAAI/P,UAAYvyB,KAAKuV,IACrB6sB,GAASpiC,KAAKuV,MAEN,IAANnT,G,EACEmwB,SAAW6P,E,EACXxN,IAAIrC,SAAS6P,IACF,IAANhgC,I,EACLiwB,WAAa+P,E,EACbxN,IAAIvC,WAAW+P,K,EAIb1G,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQmG,EAAKlgC,EAAI,E,IAE3DyH,GAAG,MAAO+4B,GAAUlH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,I,EAGnDn6B,QACAuL,OAAO,UACPiB,KAAK,KAAM,OAAStO,GACpBsO,KAAK,QAAS,O,EAGdA,KAAK,MAAM,SAAAjE,G,OAAKA,EAAE,E,IAClBiE,KAAK,MAAM,SAAAjE,G,OAAKA,EAAE,E,IAClBiE,KAAK,IAAK,GACVxJ,MACC,WACA,SAACuF,EAAGnI,G,YAAuBoR,IAAhBmtB,GAA6Bv+B,IAAMu+B,EAAc,GAAM,C,IAEnE92B,GAAG,aAAa,W,GACRrJ,MAAMwE,MAAM,UAAW,G,IAE/B6E,GAAG,YAAY,W,GACPrJ,MAAMwE,MAAM,UAAW,E,IAE/BpC,KAAKo+B,E,EAGJgC,GAAS,SAACtH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,OAAW,W,IAClD8E,EAAKl4B,GACTu5B,EAAMxtB,EAAMytB,KAAKztB,EAAMytB,KAAKntB,Q,EAG1B0qB,GAAG,GAAK9/B,KAAK8N,IACf9N,KAAKC,IAAIqiC,EAAIzC,KAAO,EAAGoB,EAAGx0B,EAAIwtB,EAAOC,OAAO9pB,MAC5CkyB,EAAIpB,M,EAEFpB,GAAG,GAAK9/B,KAAK8N,IAAI9N,KAAKC,IAAIqiC,EAAInB,KAAMF,EAAGpxB,EAAIoqB,EAAOC,OAAO5pB,KAAMgyB,EAAIlB,M,EACnE0B,GAAKR,EAAIxC,GAAGr8B,Q,GACNi4B,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQmG,EAAK,E,GC9FlDF,GAAQ,SAACtM,EAAIgK,G,IACXh9B,EAAIgzB,EAAG,GAAKgK,EAAG,GACnB/8B,EAAI+yB,EAAG,GAAKgK,EAAG,GACfnuB,EAAIuwB,GAAYp/B,EAAGC,G,OAEd/C,KAAK+xB,KAAKhvB,EAAI4O,E,EA+CjBsxB,GAAU,SAACvH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQqF,G,OAAW,WAC5D/G,EAAGhX,K,EACH8Y,a,IAGCyE,EAAQlQ,K,EAIRyR,KAAKntB,YAAS5B,E,EAKd+uB,KAAKvW,MAAQ,SAAA9rB,G,IACXoiC,EAAMxtB,EAAMytB,KAAKriC,G,YACRsT,IAAR8uB,OAAoB9uB,EAAY8uB,EAAIpB,KAAOoB,EAAIzC,I,IAIlD0C,KAAKhQ,SAhEI,SAAAzd,G,OAAS,SAAA5U,G,IAClBoiC,EAAMxtB,EAAMytB,KAAKriC,G,QACXsT,IAAR8uB,E,KAIFY,GADWd,GAAME,EAAIxM,GAAIwM,EAAIxC,IACV9/B,KAAKuV,GAAK,E,OAE3B+sB,EAAIxM,GAAG,GAAKwM,EAAIxC,GAAG,K,EACZ,EAAI9/B,KAAKuV,GAAK2tB,GAGlBA,C,GAoDe3Q,CAASzd,G,EACzBytB,KAAKlQ,WAlDM,SAAAvd,G,OAAS,SAAA5U,G,IACpBoiC,EAAMxtB,EAAMytB,KAAKriC,G,QACXsT,IAAR8uB,E,KAKFY,GADWd,GAAME,EAAIxM,GAAIwM,EAAIQ,IACV9iC,KAAKuV,GAAK,E,OAE3B+sB,EAAIxM,GAAG,GAAKwM,EAAIQ,GAAG,K,EACZ,EAAI9iC,KAAKuV,GAAK2tB,GAGlBA,C,GAqCiB7Q,CAAWvd,G,EAC7BytB,KAAKxhC,OAnCE,SAAA+T,G,OAAS,SAAA5U,G,IAChBoiC,EAAMxtB,EAAMytB,KAAKriC,G,QAEXsT,IAAR8uB,E,KAIEx/B,EAAIw/B,EAAIxM,GAAG,GAAKwM,EAAIxC,GAAG,GAC3B/8B,EAAIu/B,EAAIxM,GAAG,GAAKwM,EAAIxC,GAAG,G,OAElBoC,GAAYp/B,EAAGC,E,GAyBFhC,CAAO+T,G,EAExBjL,GAAG,oBAAoB,W,IAClBw2B,EAAMhoB,OAAOioB,oBAAoBiC,MAAMn2B,QAAO,SAAA7B,G,OAAMyZ,MAAMzZ,E,IAE5D81B,EAAIt/B,OAAS,I,EAEXu5B,SAAQ,SAAA/vB,G,IACJ41B,EAAOoC,KAAKh4B,GAAG41B,K,EACfoC,KAAKntB,OAAS7K,EAGfm3B,GAAYvB,EAAZuB,CAAkBvB,EAAK/vB,KAAM+vB,EAAK/I,Q,GACzBtiB,EAAO2lB,E,OAGbiB,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,E,SAK1CtwB,UACAC,OAAO,OACPyB,OAAO,KACPiB,KAAK,KAAM,QACXA,KACC,YACA,aAAeyrB,EAAOC,OAAO9pB,KAAO,IAAM6pB,EAAOC,OAAO5pB,IAAM,K,EAI/DysB,WCnHc,SAACrB,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,OAAW,WAChD9jB,OAAOioB,oBAAoBxrB,EAAMytB,MAAMn2B,QAAO,SAAA7B,G,OAAMyZ,MAAMzZ,E,IAElE+vB,SAAQ,SAAA/vB,G,EACJg4B,KAAKntB,OAAS7K,E,GACRuK,EAAO2lB,E,OAEXiB,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,E,ID4GzBgH,CAAWzH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,G,EAGvDtyB,GAAG,QEhHY,SAACiL,EAAOmlB,EAAQQ,EAAI+G,G,OAAW,W,IAC3CnQ,EAAIlC,GAAMra,EAAM2qB,UAAUt9B,Q,EAE9B,GAAKkvB,EAAE,GAAK4I,EAAOC,OAAO9pB,K,EAC1B,GAAKihB,EAAE,GAAK4I,EAAOC,OAAO5pB,I,IAEtB6vB,EAAOoB,GAAmBtH,EAAQQ,EAAI+G,EAAQnQ,GAC9CiR,EAAM,C,GACNjR,E,KACE8O,E,KACAqB,EAAOrB,EAAK/vB,M,KACZoxB,EAAOrB,EAAK/I,O,KACZ,E,KACAjT,GAAE8V,G,gBACIzmB,E,cACFA,E,IACLohB,KAAQ3C,YAAY,I,EAIvB6D,GAAG,GAAK91B,KAAK8N,IAAI9N,KAAKC,IAAIqiC,EAAIzC,KAAMxO,EAAE,IAAKiR,EAAIpB,M,EAC/CpB,GAAKwC,EAAIxM,GAAGryB,Q,EACZq/B,GAAKR,EAAIxM,GAAGryB,Q,EAEV8+B,KAAKpC,EAAK/9B,GAAKkgC,E,EACfC,KAAKntB,OAAS+qB,EAAK/9B,C,EFuFVghC,CAAYtuB,EAAOmlB,EAAQQ,EAAI+G,IAC3C33B,GAAG,OAAQm5B,GAAOtH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,IACjDtyB,GAAG,MAAO+4B,GAAUlH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,I,EAKhDsD,UAAYhF,EAAG5uB,UAClBC,OAAO,OACPkD,OAAO,OAAQ,UACfR,KAAK,KAAM,cACXA,KAAK,IAAKyrB,EAAOC,OAAO9pB,MACxB5B,KAAK,IAAKyrB,EAAOC,OAAO5pB,KACxB9B,KAAK,QAASyc,GAAEgP,IAChBzrB,KAAK,SAAU2V,GAAE8V,GAAU,GAC3Bj1B,MAAM,UAAW,GACjBpC,KAAKo+B,E,GGvIJqC,GAAe,SAACvgC,EAAGC,EAAG4O,EAAGpH,G,MACtB,C,IAEDzH,EAAE2J,EAAI1J,EAAE8M,EAAI/M,EAAE+M,EAAI9M,EAAE0J,IAAMkF,EAAElF,EAAIlC,EAAEkC,IACjC3J,EAAE2J,EAAI1J,EAAE0J,IAAMkF,EAAElF,EAAIlC,EAAEsF,EAAI8B,EAAE9B,EAAItF,EAAEkC,MACnC3J,EAAE2J,EAAI1J,EAAE0J,IAAMkF,EAAE9B,EAAItF,EAAEsF,IAAM/M,EAAE+M,EAAI9M,EAAE8M,IAAM8B,EAAElF,EAAIlC,EAAEkC,I,IAElD3J,EAAE2J,EAAI1J,EAAE8M,EAAI/M,EAAE+M,EAAI9M,EAAE0J,IAAMkF,EAAE9B,EAAItF,EAAEsF,IACjC/M,EAAE+M,EAAI9M,EAAE8M,IAAM8B,EAAElF,EAAIlC,EAAEsF,EAAI8B,EAAE9B,EAAItF,EAAEkC,MACnC3J,EAAE2J,EAAI1J,EAAE0J,IAAMkF,EAAE9B,EAAItF,EAAEsF,IAAM/M,EAAE+M,EAAI9M,EAAE8M,IAAM8B,EAAElF,EAAIlC,EAAEkC,I,ECKpD62B,GAAY,SAAC5H,EAAYzB,EAAQQ,G,OAAO,W,IAACpM,EAAgB1pB,UAAA5D,OAAA,QAAAyS,IAAA7O,UAAA,GAAAA,UAAA,GAAT,K,GACvC,OAAT0pB,E,OACKqN,EAAWrN,K,IAGmB,IAAnCoM,EAAG8I,aAAa//B,QAAQ6qB,G,MACpB,IAAI7c,MAAM,yCAA2C6c,G,OAKzDA,IAASqN,EAAWrN,OAGE,SAApBqN,EAAWrN,M,EACV0O,a,EAIMyG,MAAM9H,EAAWrN,MAAM2O,UAAUvC,G,EAEjCpM,KAAOA,E,EACPmV,MAAM9H,EAAWrN,MAAMiO,UACrB,SAATjO,SACKoM,EAAGwB,e,EAEPA,eA1Cc,SAACP,EAAYzB,EAAQQ,G,OAAO,W,IAACsB,EAAqBp3B,UAAA5D,OAAA,QAAAyS,IAAA7O,UAAA,GAAAA,UAAA,GAAT,K,GAC5C,OAAdo3B,E,OACKL,EAAWK,U,GAIF,S,EADN0H,OAAO1H,GAAW2H,gBACW,OAAd3H,E,MACnB,IAAIvqB,MAAM,qBAAuBuqB,G,SAG9BA,UAAYA,E,EAChBM,QAAUX,EAAWiI,cAAclI,W,EACvCD,gBACIf,C,EA6BiBwB,CAAeP,EAAYzB,EAAQQ,IAIpDA,C,GCvCHmJ,GAAkB,SAAA3J,G,OAAU,SAAA1vB,G,OAChC0vB,EAAOY,WAAWtwB,GAAGs5B,MAAQ5J,EAAOY,WAAWtwB,GAAGs5B,MAAQt5B,C,GCNtDu5B,GAAuB,SAAC7J,EAAQQ,EAAI5C,G,OACxC,SAASiE,G,EACJiI,KAAKjI,G,EACLiB,WAAWjB,G,EAGXjwB,UACAC,OAAO,OACPI,UAAU,UACVE,QAAO,SAAA7B,G,OAAKA,IAAMuxB,C,IAClB9jB,aACA/C,SAASglB,EAAO+J,eAChBphC,KAAKi1B,EAAK7gB,MAAMijB,EAAOY,WAAWiB,GAAWX,S,EAC7C1B,Q,GCbDwK,GAAe,SAAChK,EAAQQ,G,GACvBR,EAAOgK,a,KAERC,EAAQn7B,GAAMo7B,O,KACVD,EAAQ,GAAK,EAAIA,GACT,EAAI,EAAIA,E,EAEjBE,wBAA0BF,E,EAC9BhhC,IACAgJ,UAAU,cACVsC,KACC,YACA,0BAA4ByrB,EAAOmK,uBAAyB,K,GAE1DzzB,gBAb0B,C,ECF5B0zB,GAAW,SAAApK,G,IACT9V,EAAI8V,EAAOhO,OAASgO,EAAOC,OAAO5pB,IAAM2pB,EAAOC,OAAOoH,O,MAE3B,UAA7BrH,EAAOqK,mBACF,CACLngB,EACE,EACA8V,EAAOsK,0BAA0BjD,OACjCrH,EAAOsK,0BAA0Bj0B,IACnC,GAEoC,OAA7B2pB,EAAOqK,mBACT,CACLngB,EAAI,EACJ,EACE8V,EAAOsK,0BAA0BjD,OACjCrH,EAAOsK,0BAA0Bj0B,KAGhC,CAAC6T,EAAI,EAAG,E,6fCpBXqgB,GAAU,SAAAj6B,G,OAAW,OAANA,QAAoBiJ,IAANjJ,C,ECE7Bk6B,GAAkB,SAAC5M,EAAMiE,G,IACzB4I,OAAA,E,OAEI5I,EAAUhE,Q,IACX,O,UAaOsB,GAAS0C,EAAUX,Q,UAV1B,Q,EACOjC,GAAU4C,EAAUX,Q,UAE3B,M,EACOlC,GAAQ6C,EAAUX,Q,UAEzB,S,EACOhC,GAAW2C,EAAUX,Q,SAQhC9C,MAAMyD,EAAUzD,OAChBL,WAAW8D,EAAU9D,YACrBE,cAAc4D,EAAU6I,eACxBxM,cAAc2D,EAAU8I,eACxBxM,YAAY0D,EAAU1D,aACtBH,WAAW6D,EAAU7D,YAEjByM,C,EC/BHG,GAAY,SAAC5K,EAAQyB,G,GACrBzB,EAAOoC,SAAWpC,EAAOoC,QAAQt7B,SAAWk5B,EAAO/3B,KAAKnB,OAC1D,OAAO,E,IAEH+jC,EAASpJ,EAAWiI,cAAcoB,a,IAEnC,IAAIviC,KAAOsiC,E,GACVA,EAAOnhC,eAAenB,G,OACjB,E,OAGJ,C,ECXIwiC,GAAY,KCKZC,GAAb,W,WAEiBC,G,gBAEJC,YAAYD,E,sCAGlB9iC,EAAE4H,G,OAEG5H,EAAI,GAAKA,EAAI5B,KAAK0kC,SAASnkC,QAAUiJ,EAAI,GAAKA,EAAIxJ,KAAK0kC,SAAS,GAAGnkC,OAE5D,KAEJP,KAAK0kC,SAAS9iC,EAAE,GAAG4H,EAAE,E,6BAG3B5H,G,OAEGA,EAAI5B,KAAK0kC,SAASnkC,OAEX,KAEJ,IAAIqkC,GAAO5kC,KAAK0kC,SAAS9iC,EAAE,G,6BAGjC4H,G,GAE4B,IAAzBxJ,KAAK0kC,SAASnkC,O,OAEP,K,GAEPiJ,EAAIxJ,KAAK0kC,SAAS,GAAGnkC,O,OAEd,K,QAEPskC,EAAM,GAAI7+B,EAAIhG,KAAK0kC,SAASnkC,OACvBqB,EAAI,EAAGA,EAAIoE,EAAGpE,I,EAEfsG,KAAKlI,KAAK0kC,SAAS9iC,GAAG4H,EAAE,I,OAEzB,IAAIo7B,GAAOC,E,0CAKdC,EAAiC,IAAzB9kC,KAAK0kC,SAASnkC,OAAgB,EAAIP,KAAK0kC,SAAS,GAAGnkC,O,MACxD,CAACwkC,KAAM/kC,KAAK0kC,SAASnkC,OAAQukC,KAAMA,E,uCAKnC9kC,KAAK0kC,SAASnkC,M,uCAKQ,IAAzBP,KAAK0kC,SAASnkC,OAEP,EAEJP,KAAK0kC,SAAS,GAAGnkC,M,6BAGvB2W,G,IAEG8tB,EAAI9tB,EAAOwtB,UAAYxtB,E,GACtB8tB,EAAE,IAA0B,qBAAbA,EAAE,GAAG,K,EAEjB,IAAIP,EAAOO,GAAGN,UAEO,IAAzB1kC,KAAK0kC,SAASnkC,QAA6B,IAAbykC,EAAEzkC,O,OAEzBP,KAAK0kC,SAASnkC,SAAWykC,EAAEzkC,O,GAElCP,KAAK0kC,SAASnkC,SAAWykC,EAAEzkC,O,OAEpB,E,GAEPP,KAAK0kC,SAAS,GAAGnkC,SAAWykC,EAAE,GAAGzkC,O,OAE1B,E,QAEiDiJ,EAAxD5H,EAAI5B,KAAK0kC,SAASnkC,OAAQ0kC,EAAKjlC,KAAK0kC,SAAS,GAAGnkC,OAC7CqB,K,MAECqjC,EACGz7B,K,GAEChK,KAAK4vB,IAAIpvB,KAAK0kC,SAAS9iC,GAAG4H,GAAKw7B,EAAEpjC,GAAG4H,IAAMg7B,G,OAEnC,E,OAIZ,C,sCAKA,IAAIC,EAAOzkC,KAAK0kC,S,6BAGtBQ,EAAI/Q,G,GAEwB,IAAzBn0B,KAAK0kC,SAASnkC,O,OAEP,IAAIkkC,EAAO,I,QAEgDj7B,EAAlE27B,EAAM,GAAIvjC,EAAI5B,KAAK0kC,SAASnkC,OAAQ0kC,EAAKjlC,KAAK0kC,SAAS,GAAGnkC,OACvDqB,K,MAECqjC,E,EACArjC,GAAK,GACF4H,K,EAEC5H,GAAG4H,GAAK07B,EAAG9iC,KAAK+xB,EAASn0B,KAAK0kC,SAAS9iC,GAAG4H,GAAI5H,EAAI,EAAG4H,EAAI,G,OAG9D,IAAIi7B,EAAOU,E,sCAGRjuB,G,IAEN8tB,EAAI9tB,EAAOwtB,UAAYxtB,E,MACH,qBAAb8tB,EAAE,GAAG,K,EAER,IAAIP,EAAOO,GAAGN,UAEO,IAAzB1kC,KAAK0kC,SAASnkC,OAEM,IAAbykC,EAAEzkC,OAELP,KAAK0kC,SAASnkC,SAAWykC,EAAEzkC,QAAUP,KAAK0kC,SAAS,GAAGnkC,SAAWykC,EAAE,GAAGzkC,M,6BAG7E2W,G,GAE4B,IAAzBlX,KAAK0kC,SAASnkC,O,OAEPP,KAAK0M,KAAI,SAAST,G,OAEVA,C,QAGf+4B,EAAI9tB,EAAOwtB,UAAYxtB,E,MACH,qBAAb8tB,EAAE,GAAG,K,EAER,IAAIP,EAAOO,GAAGN,UAEjB1kC,KAAKolC,aAAaJ,GAIhBhlC,KAAK0M,KAAI,SAAST,EAAGrK,EAAG4H,G,OAEhByC,EAAI+4B,EAAEpjC,EAAE,GAAG4H,EAAE,E,IAJjB,I,kCAQL0N,G,GAEuB,IAAzBlX,KAAK0kC,SAASnkC,O,OAEPP,KAAK0M,KAAI,SAAST,G,OAEdA,C,QAGX+4B,EAAI9tB,EAAOwtB,UAAYxtB,E,MACH,qBAAb8tB,EAAE,GAAG,K,EAER,IAAIP,EAAOO,GAAGN,UAEjB1kC,KAAKolC,aAAaJ,GAIhBhlC,KAAK0M,KAAI,SAAST,EAAGrK,EAAG4H,G,OAEhByC,EAAI+4B,EAAEpjC,EAAE,GAAG4H,EAAE,E,IAJjB,I,6CAQM0N,G,GAEY,IAAzBlX,KAAK0kC,SAASnkC,O,OAEP,E,IAEPykC,EAAI9tB,EAAOwtB,UAAYxtB,E,MACH,qBAAb8tB,EAAE,GAAG,K,EAER,IAAIP,EAAOO,GAAGN,UAGd1kC,KAAK0kC,SAAS,GAAGnkC,SAAWykC,EAAEzkC,M,kCAGhC2W,G,GAEuB,IAAzBlX,KAAK0kC,SAASnkC,O,OAEP,K,IAEN2W,EAAOwtB,S,OAED1kC,KAAK0M,KAAI,SAAST,G,OAEdA,EAAIiL,C,QAGfmuB,IAAenuB,EAAOouB,Q,GAEF,qBADpBN,EAAI9tB,EAAOwtB,UAAYxtB,GACd,GAAG,K,EAER,IAAIutB,EAAOO,GAAGN,WAEjB1kC,KAAKulC,oBAAoBP,G,OAEnB,K,QAEqCx7B,EACZ2H,EAAkBq0B,EADlD5jC,EAAI5B,KAAK0kC,SAASnkC,OAAQ0kC,EAAKD,EAAE,GAAGzkC,OACpCukC,EAAO9kC,KAAK0kC,SAAS,GAAGnkC,OAAWmkC,EAAW,GAC3C9iC,K,MAECqjC,E,EACKrjC,GAAK,GACP4H,KACP,C,MACQs7B,E,EACE,EACC3zB,K,GAEInR,KAAK0kC,SAAS9iC,GAAGuP,GAAK6zB,EAAE7zB,GAAG3H,G,EAE7B5H,GAAG4H,GAAKg8B,C,KAGrBR,EAAI,IAAIP,EAAOC,G,OACZW,EAAeL,EAAEH,IAAI,GAAKG,C,+BAG9B1iC,EAAGC,EAAG4O,EAAGpH,G,GAEiB,IAAzB/J,KAAK0kC,SAASnkC,O,OAEP,K,QAEgBqB,EAAGqjC,EAAIz7B,EAA9Bk7B,EAAW,GAAIe,EAAKt0B,EACpB4zB,EAAO/kC,KAAK0kC,SAASnkC,OAAQukC,EAAO9kC,KAAK0kC,SAAS,GAAGnkC,OAClDklC,K,QAECt0B,EAAIs0B,EAAK,GACC,G,EACT17B,EACEk7B,K,EAECl7B,EAAIk7B,EAAK,E,EACJrjC,GAAG4H,GAAKxJ,KAAK0kC,UAAUpiC,EAAEV,EAAE,GAAGmjC,IAAOxiC,EAAEiH,EAAE,GAAGs7B,G,OAGtD,IAAIL,EAAOC,E,wCAKW,IAAzB1kC,KAAK0kC,SAASnkC,O,OAEP,IAAIkkC,EAAO,I,QAE8Cj7B,EAAhEu7B,EAAO/kC,KAAK0kC,SAASnkC,OACrBmkC,EAAW,GAAI9iC,EADwB5B,KAAK0kC,SAAS,GAAGnkC,OAErDqB,K,MAECmjC,E,EACKnjC,GAAK,GACP4H,K,EAEM5H,GAAG4H,GAAKxJ,KAAK0kC,SAASl7B,GAAG5H,G,OAGnC,IAAI6iC,EAAOC,E,wCAKdI,EAAiC,IAAzB9kC,KAAK0kC,SAASnkC,OAAgB,EAAIP,KAAK0kC,SAAS,GAAGnkC,O,OACvDP,KAAK0kC,SAASnkC,SAAWukC,C,kCAKJ,IAAzB9kC,KAAK0kC,SAASnkC,O,OAEP,K,QAEwDiJ,EAA/DC,EAAI,EAAG7H,EAAI5B,KAAK0kC,SAASnkC,OAAQ0kC,EAAKjlC,KAAK0kC,SAAS,GAAGnkC,OACpDqB,K,MAECqjC,EACGz7B,KAEChK,KAAK4vB,IAAIpvB,KAAK0kC,SAAS9iC,GAAG4H,IAAMhK,KAAK4vB,IAAI3lB,K,EAErCzJ,KAAK0kC,SAAS9iC,GAAG4H,I,OAI1BC,C,iCAGFwC,G,GAEwB,IAAzBjM,KAAK0kC,SAASnkC,O,OAEP,K,IAEkCqB,EAAiC4H,EAA5Di8B,EAAKzlC,KAAK0kC,SAASnkC,OAAW0kC,EAAKjlC,KAAK0kC,SAAS,GAAGnkC,O,IACjEqB,EAAI,EAAGA,EAAI6jC,EAAI7jC,I,IAEX4H,EAAI,EAAGA,EAAIy7B,EAAIz7B,I,GAEZxJ,KAAK0kC,SAAS9iC,GAAG4H,KAAOyC,E,MAEjB,C,EACArK,EAAE,E,EACF4H,EAAE,G,OAKd,I,wCAKFxJ,KAAK0lC,S,OAEC,K,QAEPP,EAAM,GAAIn/B,EAAIhG,KAAK0kC,SAASnkC,OACvBqB,EAAI,EAAGA,EAAIoE,EAAGpE,I,EAEfsG,KAAKlI,KAAK0kC,SAAS9iC,GAAGA,I,OAEvB,IAAIgjC,GAAOO,E,gDAKW,IAAzBnlC,KAAK0kC,SAASnkC,O,OAEP,IAAIkkC,EAAO,I,IAEFU,EACUvjC,EAAG4H,EAAiCqnB,EAD9DmU,EAAIhlC,KAAK2lC,MACT3/B,EAAIhG,KAAK0kC,SAASnkC,OAAcqlC,EAAK5lC,KAAK0kC,SAAS,GAAGnkC,O,IACrDqB,EAAI,EAAGA,EAAIoE,EAAGpE,IACnB,C,GAC6B,IAArBojC,EAAEN,SAAS9iC,GAAGA,G,IAET4H,EAAI5H,EAAI,EAAG4H,EAAIxD,EAAGwD,I,GAEM,IAArBw7B,EAAEN,SAASl7B,GAAG5H,GAClB,C,MACU,GACDivB,EAAI,EAAGA,EAAI+U,EAAI/U,I,EAEZ3oB,KAAK88B,EAAEN,SAAS9iC,GAAGivB,GAAKmU,EAAEN,SAASl7B,GAAGqnB,I,EAE5C6T,SAAS9iC,GAAKujC,E,SAKH,IAArBH,EAAEN,SAAS9iC,GAAGA,G,IAET4H,EAAI5H,EAAI,EAAG4H,EAAIxD,EAAGwD,IACvB,C,IACQq8B,EAAab,EAAEN,SAASl7B,GAAG5H,GAAKojC,EAAEN,SAAS9iC,GAAGA,G,MAC5C,GACDivB,EAAI,EAAGA,EAAI+U,EAAI/U,I,EAMZ3oB,KAAK2oB,GAAKjvB,EAAI,EAAIojC,EAAEN,SAASl7B,GAAGqnB,GAAKmU,EAAEN,SAAS9iC,GAAGivB,GAAKgV,G,EAE9DnB,SAASl7B,GAAK27B,C,SAIrBH,C,0CAKsB,IAAzBhlC,KAAK0kC,SAASnkC,O,OAEP,E,IAENP,KAAK0lC,W,OAEC,K,QAEPV,EAAIhlC,KAAK8lC,oBACTC,EAAMf,EAAEN,SAAS,GAAG,GAAI1+B,EAAIg/B,EAAEN,SAASnkC,OAClCqB,EAAI,EAAGA,EAAIoE,EAAGpE,I,GAEPojC,EAAEN,SAAS9iC,GAAGA,G,OAEvBmkC,C,6CAKC/lC,KAAK0lC,YAAqC,IAAvB1lC,KAAKgmC,a,oCAKH,IAAzBhmC,KAAK0kC,SAASnkC,O,OAEP,E,IAENP,KAAK0lC,W,OAEC,K,QAEPO,EAAKjmC,KAAK0kC,SAAS,GAAG,GAAI1+B,EAAIhG,KAAK0kC,SAASnkC,OACvCqB,EAAI,EAAGA,EAAIoE,EAAGpE,I,GAEb5B,KAAK0kC,SAAS9iC,GAAGA,G,OAEpBqkC,C,mCAKsB,IAAzBjmC,KAAK0kC,SAASnkC,O,OAEP,E,QAGiDiJ,EADxDw7B,EAAIhlC,KAAK8lC,oBAAqBI,EAAO,EACrCtkC,EAAI5B,KAAK0kC,SAASnkC,OAAQ0kC,EAAKjlC,KAAK0kC,SAAS,GAAGnkC,OAC7CqB,K,MAECqjC,EACGz7B,K,GAEChK,KAAK4vB,IAAI4V,EAAEN,SAAS9iC,GAAG4H,IAAMg7B,GACjC,C,iBAMD0B,C,iCAGFhvB,G,GAEwB,IAAzBlX,KAAK0kC,SAASnkC,O,OAEPP,KAAK2lC,M,IAEZX,EAAI9tB,EAAOwtB,UAAYxtB,EACH,qBAAb8tB,EAAE,GAAG,K,EAER,IAAIP,EAAOO,GAAGN,U,IAGuBl7B,EADzC+H,EAAIvR,KAAK2lC,MAAOb,EAAOvzB,EAAEmzB,SAAS,GAAGnkC,OACrCqB,EAAI2P,EAAEmzB,SAASnkC,OAAQ0kC,EAAKD,EAAE,GAAGzkC,O,GACjCqB,IAAMojC,EAAEzkC,O,OAED,K,KAEJqB,K,MAECqjC,EACGz7B,K,EAEDk7B,SAAS9iC,GAAGkjC,EAAOt7B,GAAKw7B,EAAEpjC,GAAG4H,G,OAGhC+H,C,sCAKsB,IAAzBvR,KAAK0kC,SAASnkC,O,OAEP,K,IAENP,KAAK0lC,YAAc1lC,KAAKmmC,a,OAElB,K,QAEyB38B,EAELqnB,EAAGsU,EAAKiB,EACZC,EAHvBrgC,EAAIhG,KAAK0kC,SAASnkC,OAAQqB,EAAGoE,EAC7Bg/B,EAAIhlC,KAAKsmC,QAAQ7B,EAAO8B,EAAEvgC,IAAI8/B,oBAC9BF,EAAKZ,EAAEN,SAAS,GAAGnkC,OACnBimC,EAAmB,GAGhB5kC,KACP,C,MAEU,G,EACWA,GAAK,G,EACZojC,EAAEN,SAAS9iC,GAAGA,GACnBivB,EAAI,EAAGA,EAAI+U,EAAI/U,I,EAEFmU,EAAEN,SAAS9iC,GAAGivB,GAAKuV,E,EAC7Bl+B,KAAKm+B,GAGLxV,GAAK7qB,G,EAEYpE,GAAGsG,KAAKm+B,G,MAG/B3B,SAAS9iC,GAAKujC,E,EAGZvjC,EACG4H,KACP,C,MACU,GACDqnB,EAAI,EAAGA,EAAI+U,EAAI/U,I,EAEZ3oB,KAAK88B,EAAEN,SAASl7B,GAAGqnB,GAAKmU,EAAEN,SAAS9iC,GAAGivB,GAAKmU,EAAEN,SAASl7B,GAAG5H,I,EAE/D8iC,SAASl7B,GAAK27B,C,SAGjB,IAAIV,EAAO+B,E,wCAKXxmC,KAAK0M,KAAI,SAAST,G,OAEVzM,KAAKikB,MAAMxX,E,mCAItBA,G,OAEGjM,KAAK0M,KAAI,SAASmkB,G,OAETrxB,KAAK4vB,IAAIyB,EAAI5kB,IAAMu4B,GAAav4B,EAAI4kB,C,0CAMhD4V,EAAc,GACdzgC,EAAIhG,KAAK0kC,SAASnkC,O,GACZ,IAANyF,EAAS,MAAO,K,IACf,IAAIpE,EAAI,EAAGA,EAAIoE,EAAGpE,I,EAEPsG,KAAK,IAAI08B,GAAO5kC,KAAK0kC,SAAS9iC,IAAI8kC,W,OAE3CD,EAAYt+B,KAAK,K,qCAGfg9B,G,IAELvjC,EAAG4H,EAAGk7B,EAAWS,EAAIT,UAAYS,E,GACjCT,EAAS,IAAiC,qBAApBA,EAAS,GAAG,GACtC,C,MACQA,EAASnkC,O,KACRmkC,SAAW,GACT9iC,K,MAEC8iC,EAAS9iC,GAAGrB,O,KACXmkC,SAAS9iC,GAAK,GACZ4H,K,KAEEk7B,SAAS9iC,GAAG4H,GAAKk7B,EAAS9iC,GAAG4H,G,OAGnCxJ,I,KAEPgG,EAAI0+B,EAASnkC,O,SACZmkC,SAAW,GACX9iC,EAAI,EAAGA,EAAIoE,EAAGpE,I,KAEV8iC,SAASx8B,KAAK,CAACw8B,EAAS9iC,K,OAE1B5B,I,uCAMH2mC,EAAS,G,GACe,GAAxB3mC,KAAK0kC,SAASnkC,O,MAEP,G,IAIN,IAAIiJ,EAAI,EAAGA,EAAIxJ,KAAK0kC,SAAS,GAAGnkC,OAAQiJ,I,IAEpC,IAAI5H,EAAI,EAAGA,EAAI5B,KAAK0kC,SAASnkC,OAAQqB,I,EAE/BsG,KAAKlI,KAAK0kC,SAAS9iC,GAAG4H,I,OAG9Bm9B,C,wCAMqB,GAAxB3mC,KAAK0kC,SAASnkC,QAA0C,GAA3BP,KAAK0kC,SAAS,GAAGnkC,O,OAEvCP,K,GAGPA,KAAK0kC,SAASnkC,OAAS,GAAKP,KAAK0kC,SAAS,GAAGnkC,OAAS,E,OAE/C,K,IAGN,IAAIqB,EAAI,EAAGA,EAAI5B,KAAK0kC,SAASnkC,OAAQqB,I,IAEjC,IAAI4H,EAAIxJ,KAAK0kC,SAAS9iC,GAAGrB,OAAQiJ,EAAI,EAAGA,IAErC5H,GAAK4H,E,KAEAk7B,SAAS9iC,GAAGsG,KAAK,G,KAIjBw8B,SAAS9iC,GAAGsG,KAAK,G,IAKzBtG,EAAI5B,KAAK0kC,SAASnkC,OAAQqB,EAAI,EAAGA,IAE7B,GAALA,E,KAEK8iC,SAASx8B,KAAK,CAAC,EAAG,EAAG,EAAG,IAEnB,GAALtG,E,KAEA8iC,SAASx8B,KAAK,CAAC,EAAG,EAAG,EAAG,IAEnB,GAALtG,E,KAEA8iC,SAASx8B,KAAK,CAAC,EAAG,EAAG,EAAG,IAEnB,GAALtG,G,KAEA8iC,SAASx8B,KAAK,CAAC,EAAG,EAAG,EAAG,I,OAI9BlI,I,0CAMqB,GAAxBA,KAAK0kC,SAASnkC,QAA0C,GAA3BP,KAAK0kC,SAAS,GAAGnkC,OAEvC,KAGJ,IAAIkkC,EAAO,CAAC,CAACzkC,KAAK0kC,SAAS,GAAG,GAAI1kC,KAAK0kC,SAAS,GAAG,GAAI1kC,KAAK0kC,SAAS,GAAG,IACzD,CAAC1kC,KAAK0kC,SAAS,GAAG,GAAI1kC,KAAK0kC,SAAS,GAAG,GAAI1kC,KAAK0kC,SAAS,GAAG,IAC5D,CAAC1kC,KAAK0kC,SAAS,GAAG,GAAI1kC,KAAK0kC,SAAS,GAAG,GAAI1kC,KAAK0kC,SAAS,GAAG,K,OAlqB1F,GAsqBAD,GAAO8B,EAAI,SAASvgC,G,QAEKwD,EAAjB27B,EAAM,GAAIvjC,EAAIoE,EACXpE,K,MAECoE,E,EACApE,GAAK,GACF4H,K,EAEC5H,GAAG4H,GAAM5H,IAAM4H,EAAK,EAAI,E,OAG7B,IAAIi7B,GAAOU,E,EAGtBV,GAAOmC,SAAW,SAASlC,G,QAEnB9iC,EAAI8iC,EAASnkC,OACbykC,EAAIP,GAAO8B,EAAE3kC,GACVA,K,EAED8iC,SAAS9iC,GAAGA,GAAK8iC,EAAS9iC,G,OAEzBojC,C,EAGXP,GAAOoC,SAAW,SAASC,EAAOxkC,G,IAEzBA,E,OAEM,IAAImiC,GAAO,CACd,CAACjlC,KAAKyxB,IAAI6V,IAAUtnC,KAAK0xB,IAAI4V,IAC7B,CAACtnC,KAAK0xB,IAAI4V,GAAUtnC,KAAKyxB,IAAI6V,M,IAGjCzP,EAAO/0B,EAAEqjC,M,GACgB,IAAzBtO,EAAKqN,SAASnkC,O,OAEP,K,IAEPwmC,EAAM1P,EAAKiO,UACXr5B,EAAIorB,EAAKqN,SAAS,GAAGqC,EAAK13B,EAAIgoB,EAAKqN,SAAS,GAAGqC,EAAKC,EAAI3P,EAAKqN,SAAS,GAAGqC,EACzE/wB,EAAIxW,KAAK0xB,IAAI4V,GAAQ31B,EAAI3R,KAAKyxB,IAAI6V,GAAQn4B,EAAI,EAAIwC,E,OAI/C,IAAIszB,GAAO,CACd,CAAE91B,EAAE1C,EAAEA,EAAIkF,EAAGxC,EAAE1C,EAAEoD,EAAI2G,EAAEgxB,EAAGr4B,EAAE1C,EAAE+6B,EAAIhxB,EAAE3G,GACpC,CAAEV,EAAE1C,EAAEoD,EAAI2G,EAAEgxB,EAAGr4B,EAAEU,EAAEA,EAAI8B,EAAGxC,EAAEU,EAAE23B,EAAIhxB,EAAE/J,GACpC,CAAE0C,EAAE1C,EAAE+6B,EAAIhxB,EAAE3G,EAAGV,EAAEU,EAAE23B,EAAIhxB,EAAE/J,EAAG0C,EAAEq4B,EAAEA,EAAI71B,I,EAI5CszB,GAAOwC,UAAY,SAASt4B,G,IAEpBwC,EAAI3R,KAAKyxB,IAAItiB,GAAIqH,EAAIxW,KAAK0xB,IAAIviB,G,OAC3B,IAAI81B,GAAO,CACd,CAAG,EAAI,EAAI,GACX,CAAG,EAAItzB,GAAI6E,GACX,CAAG,EAAIA,EAAI7E,I,EAGnBszB,GAAOyC,UAAY,SAASv4B,G,IAEpBwC,EAAI3R,KAAKyxB,IAAItiB,GAAIqH,EAAIxW,KAAK0xB,IAAIviB,G,OAC3B,IAAI81B,GAAO,CACd,CAAGtzB,EAAI,EAAI6E,GACX,CAAG,EAAI,EAAI,GACX,EAAGA,EAAI,EAAI7E,I,EAGnBszB,GAAO0C,UAAY,SAASx4B,G,IAEpBwC,EAAI3R,KAAKyxB,IAAItiB,GAAIqH,EAAIxW,KAAK0xB,IAAIviB,G,OAC3B,IAAI81B,GAAO,CACd,CAAGtzB,GAAI6E,EAAI,GACX,CAAGA,EAAI7E,EAAI,GACX,CAAG,EAAI,EAAI,I,EAInBszB,GAAO2C,OAAS,SAASphC,EAAGyD,G,OAEjBg7B,GAAO4C,KAAKrhC,EAAGyD,GAAGiD,KAAI,W,OAEdlN,KAAK8nC,Q,KAKxB7C,GAAO8C,YAAc,SAAUtjC,G,IAYnB+e,E,GAViB,GAArB/e,EAAEygC,SAASnkC,O,OAEPyiB,EAAIyhB,GAAO8B,EAAE,IACf7B,SAAS,GAAG,GAAKzgC,EAAEygC,SAAS,G,EAC5BA,SAAS,GAAG,GAAKzgC,EAAEygC,SAAS,GACvB1hB,E,GAGc,GAArB/e,EAAEygC,SAASnkC,O,OAEPyiB,EAAIyhB,GAAO8B,EAAE,IACf7B,SAAS,GAAG,GAAKzgC,EAAEygC,SAAS,G,EAC5BA,SAAS,GAAG,GAAKzgC,EAAEygC,SAAS,G,EAC5BA,SAAS,GAAG,GAAKzgC,EAAEygC,SAAS,GACvB1hB,E,KAGL,gC,EAGVyhB,GAAO4C,KAAO,SAASrhC,EAAGyD,G,QAEDD,EAAjB27B,EAAM,GAAIvjC,EAAIoE,EACXpE,K,MAEC6H,E,EACA7H,GAAK,GACF4H,K,EAEC5H,GAAG4H,GAAK,E,OAGb,IAAIi7B,GAAOU,E,EAGtBV,GAAOzjC,UAAUwmC,kBAAoB/C,GAAOzjC,UAAU8kC,kBACtDrB,GAAOzjC,UAAU+kC,IAAMtB,GAAOzjC,UAAUglC,YACxCvB,GAAOzjC,UAAUilC,GAAKxB,GAAOzjC,UAAUymC,MACvChD,GAAOzjC,UAAU0mC,GAAKjD,GAAOzjC,UAAUklC,KACvCzB,GAAOzjC,UAAU2mC,IAAMlD,GAAOzjC,UAAUyO,QACxCg1B,GAAOzjC,UAAUiL,EAAIw4B,GAAOzjC,UAAU4mC,S,IC1yBzBhD,GAAb,W,WAEiBF,G,gBAEJC,YAAYD,E,sCAGlB9iC,G,OAESA,EAAI,GAAKA,EAAI5B,KAAK0kC,SAASnkC,OAAU,KAAOP,KAAK0kC,SAAS9iC,EAAE,E,6CAK7D5B,KAAK0kC,SAASnkC,M,0CAKdf,KAAK+V,KAAKvV,KAAK6nC,IAAI7nC,M,6BAGzB8nC,G,IAEG9hC,EAAIhG,KAAK0kC,SAASnkC,OAClBwnC,EAAID,EAAOpD,UAAYoD,E,GACvB9hC,IAAM+hC,EAAExnC,O,OAED,E,KAEJyF,K,GAECxG,KAAK4vB,IAAIpvB,KAAK0kC,SAAS1+B,GAAK+hC,EAAE/hC,IAAMw+B,G,OAE7B,E,OAGR,C,sCAIA,IAAII,EAAO5kC,KAAK0kC,S,6BAGtBQ,EAAI/Q,G,IAEDuQ,EAAW,G,YACV14B,MAAK,SAASC,EAAGrK,G,EAELsG,KAAKg9B,EAAG9iC,KAAK+xB,EAASloB,EAAGrK,G,IAEnC,IAAIgjC,EAAOF,E,iCAGbQ,EAAI/Q,G,QAELnuB,EAAIhG,KAAK0kC,SAASnkC,OACbqB,EAAI,EAAGA,EAAIoE,EAAGpE,I,EAEhBQ,KAAK+xB,EAASn0B,KAAK0kC,SAAS9iC,GAAIA,EAAE,E,4CAMrCohB,EAAIhjB,KAAKslC,U,OACH,IAANtiB,EAEOhjB,KAAK2lC,MAET3lC,KAAK0M,KAAI,SAAST,G,OAEVA,EAAE+W,C,sCAIV8kB,G,IAEHC,EAAID,EAAOpD,UAAYoD,E,GACnB9nC,KAAK0kC,SAASnkC,SACZwnC,EAAExnC,O,OAED,K,IAEPsnC,EAAM,EAAGG,EAAO,EAAGC,EAAO,E,QAEzBj8B,MAAK,SAASC,EAAGrK,G,GAEPqK,EAAI87B,EAAEnmC,EAAE,G,GACPqK,EAAIA,E,GACJ87B,EAAEnmC,EAAE,GAAKmmC,EAAEnmC,EAAE,E,MAEtBpC,KAAK+V,KAAKyyB,GAAOC,EAAOzoC,KAAK+V,KAAK0yB,GACrCD,EAAKC,IAAS,E,OAEP,K,IAEPnB,EAAQe,GAAOG,EAAKC,G,OACpBnB,GAAS,I,GAEA,GAETA,EAAQ,I,EAEA,GAELtnC,KAAK42B,KAAK0Q,E,sCAGPgB,G,IAENlG,EAAQ5hC,KAAKkoC,UAAUJ,G,OACT,OAAVlG,EAAkB,KAAQA,GAAS4C,E,0CAG7BsD,G,IAEVlG,EAAQ5hC,KAAKkoC,UAAUJ,G,OACT,OAAVlG,EAAkB,KAAQpiC,KAAK4vB,IAAIwS,EAAQpiC,KAAKuV,KAAOyvB,E,2CAGhDsD,G,IAEXD,EAAM7nC,KAAK6nC,IAAIC,G,OACH,OAARD,EAAgB,KAAQroC,KAAK4vB,IAAIyY,IAAQrD,E,6BAGhDsD,G,IAEGC,EAAID,EAAOpD,UAAYoD,E,OACvB9nC,KAAK0kC,SAASnkC,SAAWwnC,EAAExnC,OAEpB,KAEJP,KAAK0M,KAAI,SAAST,EAAGrK,G,OAAYqK,EAAI87B,EAAEnmC,EAAE,E,qCAG1CkmC,G,IAEFC,EAAID,EAAOpD,UAAYoD,E,OACvB9nC,KAAK0kC,SAASnkC,SAAWwnC,EAAExnC,OAEpB,KAEJP,KAAK0M,KAAI,SAAST,EAAGrK,G,OAEbqK,EAAI87B,EAAEnmC,EAAE,E,qCAIjBijB,G,OAEC7kB,KAAK0M,KAAI,SAAST,G,OAEVA,EAAE4Y,C,gCAIhBijB,G,IAEGC,EAAID,EAAOpD,UAAYoD,EACpBK,EAAU,EAAGniC,EAAIhG,KAAK0kC,SAASnkC,O,GAClCyF,IAAM+hC,EAAExnC,O,OAED,K,KAEJyF,K,GAEQhG,KAAK0kC,SAAS1+B,GAAK+hC,EAAE/hC,G,OAE7BmiC,C,+BAGJL,G,IAECM,EAAIN,EAAOpD,UAAYoD,E,GACE,IAAzB9nC,KAAK0kC,SAASnkC,QAA6B,IAAb6nC,EAAE7nC,O,OAEzB,K,IAEP8nC,EAAIroC,KAAK0kC,S,OACN,IAAIE,EAAO,CACbyD,EAAE,GAAKD,EAAE,GAAOC,EAAE,GAAKD,EAAE,GACzBC,EAAE,GAAKD,EAAE,GAAOC,EAAE,GAAKD,EAAE,GACzBC,EAAE,GAAKD,EAAE,GAAOC,EAAE,GAAKD,EAAE,I,uCAM1B3+B,EAAI,EAAG7H,EAAI5B,KAAK0kC,SAASnkC,OACtBqB,KAECpC,KAAK4vB,IAAIpvB,KAAK0kC,SAAS9iC,IAAMpC,KAAK4vB,IAAI3lB,K,EAElCzJ,KAAK0kC,SAAS9iC,I,OAGnB6H,C,iCAGFwC,G,QAEDrD,EAAQ,KAAM5C,EAAIhG,KAAK0kC,SAASnkC,OAC3BqB,EAAI,EAAGA,EAAIoE,EAAGpE,IAEL,OAAVgH,GAAkB5I,KAAK0kC,SAAS9iC,KAAOqK,I,EAE/BrK,EAAI,G,OAGbgH,C,mDAKA67B,GAAOmC,SAAS5mC,KAAK0kC,S,wCAKrB1kC,KAAK0M,KAAI,SAAST,G,OAEVzM,KAAKikB,MAAMxX,E,mCAItBA,G,OAEGjM,KAAK0M,KAAI,SAAS2C,G,OAET7P,KAAK4vB,IAAI/f,EAAIpD,IAAMu4B,GAAav4B,EAAIoD,C,yCAI1Ci5B,G,GAENA,EAAIC,QAAWD,EAAI/zB,OAAS+zB,EAAIhjB,I,OAEzBgjB,EAAIE,aAAaxoC,M,IAExB+nC,EAAIO,EAAI5D,UAAY4D,E,GACpBP,EAAExnC,SAAWP,KAAK0kC,SAASnkC,O,OAEpB,K,IAEEkoC,EAATjD,EAAM,E,YACLx5B,MAAK,SAASC,EAAGrK,G,EAEPqK,EAAI87B,EAAEnmC,EAAE,G,GACR6mC,EAAOA,C,IAEfjpC,KAAK+V,KAAKiwB,E,gCAGblN,G,OAEGA,EAAKjwB,SAASrI,K,gCAGjB0oC,G,OAEGA,EAAMrgC,SAASrI,K,gCAGlB2O,EAAG25B,G,IAEHP,EAAa97B,EAAGoD,EAAG23B,EAAhB2B,EAAI,K,OACPh6B,EAAEq3B,c,EAEEr3B,EAAE+1B,UAEF1kC,KAAK0kC,SAASnkC,Q,KAEb,E,OAGgB,K,EADb+nC,EAAI5D,UAAY4D,GACd/nC,OAEK,MAENooC,I,EAEGlE,GAAOoC,SAASl4B,GAAG+1B,U,EAEvB1kC,KAAK0kC,SAAS,GAAKqD,EAAE,G,EACrB/nC,KAAK0kC,SAAS,GAAKqD,EAAE,GAClB,IAAInD,EAAO,CACdmD,EAAE,GAAKY,EAAE,GAAG,GAAK18B,EAAI08B,EAAE,GAAG,GAAKt5B,EAC/B04B,EAAE,GAAKY,EAAE,GAAG,GAAK18B,EAAI08B,EAAE,GAAG,GAAKt5B,K,KAIlC,E,IAEIi5B,EAAIM,U,OAEE,K,IAEPC,EAAIP,EAAIQ,eAAe9oC,MAAM0kC,S,OAC5BiE,I,EAEGlE,GAAOoC,SAASl4B,EAAG25B,EAAIM,WAAWlE,U,EAEtC1kC,KAAK0kC,SAAS,GAAKmE,EAAE,G,EACrB7oC,KAAK0kC,SAAS,GAAKmE,EAAE,G,EACrB7oC,KAAK0kC,SAAS,GAAKmE,EAAE,GAClB,IAAIjE,EAAO,CACdiE,EAAE,GAAKF,EAAE,GAAG,GAAK18B,EAAI08B,EAAE,GAAG,GAAKt5B,EAAIs5B,EAAE,GAAG,GAAK3B,EAC7C6B,EAAE,GAAKF,EAAE,GAAG,GAAK18B,EAAI08B,EAAE,GAAG,GAAKt5B,EAAIs5B,EAAE,GAAG,GAAK3B,EAC7C6B,EAAE,GAAKF,EAAE,GAAG,GAAK18B,EAAI08B,EAAE,GAAG,GAAKt5B,EAAIs5B,EAAE,GAAG,GAAK3B,I,eAM1C,K,sCAKLsB,G,GAENA,EAAIC,OACR,C,IAEQQ,EAAI/oC,KAAK0kC,SAASzhC,QAClB4lC,EAAIP,EAAIQ,eAAeC,GAAGrE,S,OACvB,IAAIE,EAAO,CAACiE,EAAE,IAAMA,EAAE,GAAKE,EAAE,IAAKF,EAAE,IAAMA,EAAE,GAAKE,EAAE,IAAKF,EAAE,IAAMA,EAAE,IAAME,EAAE,IAAM,K,KAKnFC,EAAIV,EAAI5D,UAAY4D,E,OACpBtoC,KAAK0kC,SAASnkC,SAAWyoC,EAAEzoC,OAEpB,KAEJP,KAAK0M,KAAI,SAAST,EAAGrK,G,OAAYonC,EAAEpnC,EAAE,IAAMonC,EAAEpnC,EAAE,GAAKqK,E,uCAM3D87B,EAAI/nC,KAAK2lC,M,OACLoC,EAAErD,SAASnkC,Q,KAEV,E,WAIA,E,EAECmkC,SAASx8B,KAAK,G,qBAKT,K,OAGR6/B,C,yCAKA,IAAM/nC,KAAK0kC,SAASv8B,KAAK,MAAQ,G,qCAG/Bg9B,G,YAEJT,UAAYS,EAAIT,UAAYS,GAAKliC,QAC/BjD,I,0CAMAA,KAAK0kC,Q,OA1XpB,GA8XAE,GAAOwC,OAAS,SAASphC,G,QAEjB0+B,EAAW,GACR1+B,K,EAEMkC,KAAK1I,KAAK8nC,U,OAEhB,IAAI1C,GAAOF,E,EAGtBE,GAAOyC,KAAO,SAASrhC,G,QAEf0+B,EAAW,GACR1+B,K,EAEMkC,KAAK,G,OAEX,IAAI08B,GAAOF,E,EAGtBE,GAAO5jC,UAAUiL,EAAI24B,GAAO5jC,UAAU4mC,SACtChD,GAAO5jC,UAAUgL,KAAO44B,GAAO5jC,UAAU84B,QAEzC8K,GAAOhjC,EAAI,IAAIgjC,GAAO,CAAC,EAAE,EAAE,IAC3BA,GAAOp7B,EAAI,IAAIo7B,GAAO,CAAC,EAAE,EAAE,IAC3BA,GAAO/f,EAAI,IAAI+f,GAAO,CAAC,EAAE,EAAE,IC1Z3B,ICGMqE,GAAc,SAACxP,EAAQtB,EAAUpuB,EAAGm/B,G,IAClCC,EDJiB,SAAC1P,EAAQtB,EAAUiR,G,QACpCD,EAAY,GAEZtY,EAAIhZ,OAAOwU,KAAKoN,EAAOY,YACvByK,EAAOjU,EAAEtwB,OAENqB,EAAI,EAAGA,EAAIkjC,IAAQljC,EAAG,C,IAEvBqK,EAAIksB,EAAStH,EAAEjvB,IACfyN,EAAIoqB,EAAOY,WAAWxJ,EAAEjvB,IAAI+4B,OAAOyO,EAAIvY,EAAEjvB,K,KACrCsG,KAAK,IAAI08B,GAAO,CAAC34B,EAAGoD,KAG1BzN,EAAIkjC,EAAO,EAAG,C,IACV/Q,EAAK9nB,EATL,IAScksB,EAAStH,EAAEjvB,EAAI,IAAMqK,GACrC+nB,EAAK3kB,EAVH,IAUYoqB,EAAOY,WAAWxJ,EAAEjvB,EAAI,IAAI+4B,OAAOyO,EAAIvY,EAAEjvB,EAAI,KAAOyN,G,GACvC,OAA3BoqB,EAAO4P,gBAA0B,C,IAe/B7S,EAAW,IAdMiD,EAAO6P,iBACzBp4B,IACCuoB,EAAOY,WAAWZ,EAAO4P,iBAAiB1O,OACxCyO,EAAI3P,EAAO4P,mBAGdn4B,IAAI2f,EAAEjvB,IACa63B,EAAO6P,iBAC1Bp4B,IACCuoB,EAAOY,WAAWZ,EAAO4P,iBAAiB1O,OACxCyO,EAAI3P,EAAO4P,mBAGdn4B,IAAI2f,EAAEjvB,EAAI,K,EAER40B,GAAY,EAAIiD,EAAO8P,mBAAqBvV,EAAKwC,E,GAE9CtuB,KAAK,IAAI08B,GAAO,CAAC7Q,EAAIC,I,SAI5BmV,C,CClCWK,CAAiB/P,EAAQtB,EAAUpuB,GAC/C0/B,ECLqB,SAACC,EAAYP,G,IAClCrE,EAAOqE,EAAU5oC,OACjB+B,EAAIonC,EACJD,EAAM,G,EAERvhC,KAAKihC,EAAU,I,EACfjhC,KACF,IAAI08B,GAAO,CACTuE,EAAU,GAAGp2B,EAAE,GAAS,EAAJzQ,GAAS6mC,EAAU,GAAGp2B,EAAE,GAAKo2B,EAAU,GAAGp2B,EAAE,IAChEo2B,EAAU,GAAGp2B,EAAE,M,IAGd,IAAI8xB,EAAM,EAAGA,EAAMC,EAAO,IAAKD,EAAK,C,IACnClO,EAAMwS,EAAUtE,GAChBj1B,EAAOu5B,EAAUtE,EAAM,GACvBjO,EAAQuS,EAAUtE,EAAM,GAExB8E,EAAO/5B,EAAKg6B,SAAShT,G,EACrB1uB,KAAKyuB,EAAI1wB,IAAI0jC,EAAK19B,EAAE3J,K,EACpB4F,KAAKyuB,G,EACLzuB,KAAKyuB,EAAIiT,SAASD,EAAK19B,EAAE3J,I,UAG3B4F,KACF,IAAI08B,GAAO,CACTuE,EAAUrE,EAAO,GAAG/xB,EAAE,GAChB,EAAJzQ,GAAS6mC,EAAUrE,EAAO,GAAG/xB,EAAE,GAAKo2B,EAAUrE,EAAO,GAAG/xB,EAAE,IAC5Do2B,EAAUrE,EAAO,GAAG/xB,EAAE,M,EAGtB7K,KAAKihC,EAAUrE,EAAO,IAEnB2E,C,CD3BKI,CAAqBpQ,EAAOiQ,WAAYP,G,EAEhDxU,OAAO8U,EAAI,GAAG12B,EAAE,GAAI02B,EAAI,GAAG12B,EAAE,I,IAE5B,IAAInR,EAAI,EAAGA,EAAI6nC,EAAIlpC,OAAQqB,GAAK,EAAG,C,GAClC63B,EAAOqQ,kB,IACJ,IAAItgC,EAAI,EAAGA,EAAI,EAAGA,I,EACjBugC,SAASN,EAAI7nC,EAAI4H,GAAGuJ,EAAE,GAAI02B,EAAI7nC,EAAI4H,GAAGuJ,EAAE,GAAI,EAAG,G,EAGlDi3B,cACFP,EAAI7nC,GAAGmR,EAAE,GACT02B,EAAI7nC,GAAGmR,EAAE,GACT02B,EAAI7nC,EAAI,GAAGmR,EAAE,GACb02B,EAAI7nC,EAAI,GAAGmR,EAAE,GACb02B,EAAI7nC,EAAI,GAAGmR,EAAE,GACb02B,EAAI7nC,EAAI,GAAGmR,EAAE,G,GAMbk3B,GAAkB,SAAAxQ,G,MACY,WAA9BA,EAAOqK,mBACFngB,GAAE8V,GAAU,EACoB,QAA9BA,EAAOqK,mBACT,G,QAECoG,IACN,oFAGGvmB,GAAE8V,GAAU,E,EAkBf0Q,GAAY,SAAC1Q,EAAQtB,EAAUpuB,EAAGm/B,G,EAClCkB,YAE0B,OAA3B3Q,EAAO4P,iBAA4B5P,EAAO8P,iBAAmB,GAC9D9P,EAAOiQ,WAAa,E,GAERjQ,EAAQtB,EAAUpuB,EAAGm/B,GArBlB,SAACzP,EAAQtB,EAAUpuB,EAAGm/B,G,OAChC7c,KAAKoN,EAAOY,YAChB3tB,KAAI,SAAAmkB,G,MAAK,CACRsH,EAAStH,QACA7d,IAATjJ,EAAE8mB,GACEoZ,GAAgBxQ,GAChBA,EAAOY,WAAWxJ,GAAG8J,OAAO5wB,EAAE8mB,I,IAEnCnjB,MAAK,SAACpL,EAAGC,G,OAAMD,EAAE,GAAKC,EAAE,E,IACxBu3B,SAAQ,SAACjJ,EAAGjvB,GACL,I,EAAIsnC,EAAIvU,OAAO9D,EAAE,GAAIA,EAAE,IAAMqY,EAAI5S,OAAOzF,EAAE,GAAIA,EAAE,G,KAa7C4I,EAAQtB,EAAUpuB,EAAGm/B,G,EAE9BmB,Q,EEnEAC,GAAW,SAAArmC,G,MAAmB,oBAANA,EAAmBA,EAAI,W,OAAMA,C,GCGrDsmC,GAAW,SAAC9Q,EAAQyP,EAAK/Q,G,OAAa,SAACpuB,EAAGnI,G,SAC1C4oC,OAAOC,YAAcH,GAAQ7Q,EAAOnX,MAAfgoB,CAAsBvgC,EAAGnI,GAC3CuoC,GAAU1Q,EAAQtB,EAAUpuB,EAAGm/B,EAAIsB,O,GCDtCE,GAAc,SAACjR,EAAQyP,EAAK/Q,G,OAAa,SAACpuB,EAAGnI,G,OACrB,OAAxB63B,EAAOkR,a,EACL9O,QAAQ4O,YAAcH,GAAQ7Q,EAAOkR,aAAfL,CAA6BvgC,EAAGnI,G,EAEtDi6B,QAAQ4O,YAAcH,GAAQ7Q,EAAOnX,MAAfgoB,CAAsBvgC,EAAGnI,GAE9CuoC,GAAU1Q,EAAQtB,EAAUpuB,EAAGm/B,EAAIrN,Q,GCTtC+O,GAAS,SAAA3mC,G,MACN,CAAC,EAAEyf,SACPthB,KAAK6B,GACL4H,MAAM,iBAAiB,GACvB4W,a,ECJCooB,GAAgB,SAAAC,G,QAChBC,EAAM,GACDnpC,EAAI,EAAGA,EAAIkpC,EAAIvqC,OAAS,EAAGqB,I,EAC9BsG,KAAK,CAAC4iC,EAAIlpC,GAAIkpC,EAAIlpC,EAAI,K,OAErBmpC,C,ECKHC,GAAY,SAACvR,EAAQQ,EAAIgR,EAAQtP,EAAQuN,EAAK/Q,G,OAClD,W,IAASz2B,EAAayC,UAAA5D,OAAA,QAAAyS,IAAA7O,UAAA,GAAAA,UAAA,GAAN,K,OACD,OAATzC,EACK+3B,EAAOyR,a,EAGTA,YAAcxpC,E,EAClB8tB,MAAM,a,GACC,CAACyb,EAAOE,WAAYF,EAAOpP,UAAU1tB,QAAQ,SAAS,G,EAC3D2rB,QAfa,SAACL,EAAQyP,EAAK/Q,G,OAAa,SAACpuB,EAAGnI,G,SAC/CopC,UAAUP,YAAcH,GAAQ7Q,EAAOnX,MAAfgoB,CAAsBvgC,EAAGnI,GAC9CuoC,GAAU1Q,EAAQtB,EAAUpuB,EAAGm/B,EAAI8B,U,EAa3BI,CAAc3R,EAAQyP,EAAK/Q,I,EACjC/1B,KAAK,YAAapC,KAAM0B,GACxB1B,K,GCnBLqrC,GAAiB,SAAC5R,EAAQyP,EAAK/Q,G,OAAa,SAACpuB,EAAGnI,G,SAChDupC,WAAWV,YAAcH,GAAQ7Q,EAAOnX,MAAfgoB,CAAsBvgC,EAAGnI,GAC/CuoC,GAAU1Q,EAAQtB,EAAUpuB,EAAGm/B,EAAIiC,W,GCFtCG,GAAsB,SAAArnC,G,OAC1BsnC,WAAWtnC,IAAMA,GAAW,OAANA,EAAa,SAAW2mC,GAAO3mC,E,ECDjDunC,GAAuB,SAAA9pC,G,OAC3BmW,OAAOwU,KAAK3qB,EAAK,IAAI44B,QAAO,SAACC,EAAKC,G,SACpBhX,MAAMioB,OAAOjR,IAAQA,EAAM7X,SAAS6X,IACrC8Q,GAAoB5pC,EAAK,GAAG84B,IAEhCD,C,GACN,CAAC,E,ECTAmR,GAAgB,C,KACd,G,YACO,G,OACL,G,WACI,CAAC,E,uBACW,E,QACf,G,SACA,E,aACK,K,eACE,E,UACL,I,qBACW,E,KAChB,U,gBACW,E,kBACE,U,iBACD,G,KACZ,G,MACC,I,OACC,I,OACA,CAAE57B,IAAK,GAAI8mB,MAAO,GAAIkK,OAAQ,GAAIlxB,KAAM,I,mBAC5B,Y,0BACO,CAAEE,IAAK,EAAG8mB,MAAO,EAAGkK,OAAQ,EAAGlxB,KAAM,G,MACzD,O,UACI,c,MACJ,G,iBACW,G,gBACD,K,WACL,E,mBACO,E,SACT,G,SACA,G,cACK,K,cACD,GCpBV+7B,GAAc,SAClBlS,EACAyP,EACAjP,EACA+G,EACA3J,EACAuU,EACAC,EACAC,EACAC,G,OAEAj9B,GAAAA,EACG5K,M,UAAY2T,OAAOwU,KAAKoN,IACxBpwB,GAAG,aAAa,SAAAU,G,EACXohC,WAAWa,yBAA2BjiC,EAAEnG,M,EACxCi4B,QAAQmQ,yBAA2BjiC,EAAEnG,K,IAE1CyF,GAAG,SAAS,SAAAU,G,EACPohC,WAAWc,YAAcliC,EAAEnG,M,EAC3Bi4B,QAAQoQ,YAAcliC,EAAEnG,K,IAE7ByF,GAAG,gBAAgB,SAAAU,G,EACd8xB,QAAQ4O,YAAc1gC,EAAEnG,K,IAE7ByF,GAAG,SAAS,SAAAU,G,OAAKkwB,EAAGiS,Q,IACpB7iC,GAAG,UAAU,SAAAU,G,OAAKkwB,EAAGiS,Q,IACrB7iC,GAAG,UAAU,SAAAU,G,OAAKkwB,EAAGiS,Q,IACrB7iC,GAAG,QAAQ,SAAAU,G,EACGuvB,KAAKvvB,EAAEnG,O,EACR01B,KAAKvvB,EAAEnG,O,EACH01B,KAAKvvB,EAAEnG,M,IAExByF,GAAG,cAAc,SAAAU,G,EACTswB,WAAaJ,EAAGkS,uBAAuBt0B,OAAOwU,KAAKtiB,EAAEnG,Q,EACrDk0B,OAAOmC,EAAGgH,2B,EACdmL,iBACCR,EAAMS,a,EACLpT,SAASqT,Y,IAGfjjC,GAAG,mBAAmB,SAAAU,GAChB8N,OAAOwU,KAAKoN,EAAOY,YAAY95B,QAAQ05B,EAAGsS,mB,EAC5CC,YACoB,kBAAZziC,EAAEnG,MACPmG,EAAEnG,MAAQiU,OAAOwU,KAAKoN,EAAOY,YAAY95B,O,EACpC8oC,gBAAkB5P,EAAOY,WAAWtwB,EAAEnG,OACpCmG,EAAEnG,MAAQ61B,EAAOuD,SAASz8B,S,EAC5B8oC,gBAAkB5P,EAAOuD,SAASjzB,EAAEnG,Q,EAGtCylC,gBAAkBt/B,EAAEnG,M,EAGtB0lC,iBCjEmB,SAAC7P,EAAQ1vB,G,IACjCu/B,EAAmB,IAAImD,IACvBC,EAAgB,IAAID,I,SAEnB/qC,KAAKo4B,SAAQ,SAASsP,G,IACvBxO,EAASnB,EAAOY,WAAWtwB,GAAG4wB,OAAOyO,EAAIr/B,IACxC2iC,EAAcC,IAAI/R,I,EACPxpB,IAAIwpB,EAAQ,G,IAExBgS,EAAQF,EAAcx7B,IAAI0pB,G,EAChBxpB,IAAIwpB,EAAQgS,EAAQ,E,MAG7BlrC,KAAKo4B,SAAQ,SAASsP,G,OACpB/c,KAAKoN,EAAOY,YAAY3tB,KAAI,SAAAmkB,G,IAC7B+J,EAASnB,EAAOY,WAAWtwB,GAAG4wB,OAAOyO,EAAIr/B,I,IACxCu/B,EAAiBqD,IAAI/R,GAAS,C,IAC3BiS,EAAO,IAAIJ,I,EACAr7B,IAAIwpB,EAAQiS,E,CAE1BvD,EAAiBp4B,IAAI0pB,GAAQ+R,IAAI9b,I,EACnB3f,IAAI0pB,GAAQxpB,IAAIyf,EAAG,G,IAElCjtB,EAAQ0lC,EAAiBp4B,IAAI0pB,GAAQ1pB,IAAI2f,G,GACpC4I,EAAOY,WAAWxJ,GAAG8J,OAAOyO,EAAIvY,IAAM6b,EAAcx7B,IAAI0pB,G,EAChD1pB,IAAI0pB,GAAQxpB,IAAIyf,EAAGjtB,E,OAIjC0lC,C,CDoCuBwD,CACxBrT,EACAA,EAAO4P,iBAELuC,EAAMS,a,EACLpT,Q,IAGN5vB,GAAG,YAAY,SAAAU,GApEJ,IAAC+gC,E,EAqERvO,a,EACAlC,WAAWJ,EAAGkS,0B,EACd9R,YAvEQyQ,EAuEWrR,EAAOY,WAAYtwB,EAAEnG,MAtEzCk2B,SAAQ,SAAAiT,UACLjC,EAAIiC,E,IAENjC,I,EAoEA7R,Q,IAEJ5vB,GAAG,YAAY,SAAAU,GACVA,EAAEnG,OAASmG,EAAEnG,MAAMrD,S,EACnBqD,MAAMk2B,SAAQ,SAASwB,G,GACF7B,EAAQQ,EAAI5C,E,CAAMiE,E,MAEtCgR,WAAW,G,KE1EhBU,GAAU,SAACtjB,EAAQ3a,EAAQk+B,G,SACxBA,GAPS,SAACvjB,EAAQ3a,EAAQk+B,G,OACjC,W,IACQrpC,EAAQqpC,EAAO/oC,MAAM6K,EAAQ5K,W,OAC5BP,IAAUmL,EAAS2a,EAAS9lB,C,EAIpBspC,CAAUxjB,EAAQ3a,EAAQA,EAAOk+B,IAC3CvjB,C,EAGHyjB,GAAa,SACjBC,EACAlE,EACAjP,EACA+G,EACA4K,EACAC,EACAC,EACAC,EACApQ,EACAtE,ICzBa,SAACiR,EAAKh0B,EAAOqnB,EAAQ0R,G,OAC3BhhB,KAAK/X,GAAOwlB,SAAQ,SAAS93B,G,EAC9BA,GAAO,SAASiK,G,IACb9H,UAAU5D,O,OACN+T,EAAMtS,GAGL,eAARA,GACsC,mBAAtC6V,OAAO7W,UAAU0iB,SAASthB,KAAK6J,K,QAEvBqhC,KAAK,0D,EACThF,EAAI6D,uBAAuBlgC,I,IAE7BshC,EAAMj5B,EAAMtS,G,SACVA,GAAOiK,E,EACA7J,KAAKJ,EAAKsmC,EAAK,CAAE1kC,MAAOqI,EAAGI,SAAUkhC,I,EAC3CnrC,KAAKJ,EAAKsmC,EAAK,CAAE1kC,MAAOqI,EAAGI,SAAUkhC,IACrCjF,C,MDuBJrO,EAAImT,EAAIzR,EAbMgQ,GACnByB,EACAlE,EACAjP,EACA+G,EACA3J,EACAuU,EACAC,EACAC,EACAC,I,GAQM9R,EAAI0B,EAAQ,M,GAGlB1B,EACA5C,EACA,Q,KEwBc,SAAAmW,G,IACVl5B,ECnEU,SAAAk5B,G,IACV/T,EAAS5hB,OAAO4M,OAAO,CAAC,EAAGinB,GAAe8B,GAE5CA,GAAcA,EAAWC,kB,QACnBH,KACN,uF,SAEME,EAAWC,iBAAiB3T,SAAQ,SAAA/vB,GACtC0vB,EAAOY,WAAWtwB,EAAE/H,K,EACfq4B,WAAWtwB,EAAE/H,KAAKqhC,MAAQ5J,EAAOY,WAAWtwB,EAAE/H,KAAKqhC,MACtD5J,EAAOY,WAAWtwB,EAAE/H,KAAKqhC,MACzBt5B,EAAEnG,M,EAECy2B,WAAWtwB,EAAE/H,KAAO,C,MAClB+H,EAAEnG,M,SAMX8pC,EAAa,CACjB,SACA,SACA,YACA,OACA,QACA,WACA,aACA,eACAr8B,QAAOgb,EAAAA,GAAAA,IAAKoN,IAERkC,EAAS7sB,GAAAA,EAAS5K,W,EAAYwpC,GAQlC1M,GAAS2M,EAAAA,GAAAA,MAETtW,EAAOuB,KAAWf,MAAM,G,MAwBnB,C,qCAjCG,C,WACK,E,aACE,E,MACP,E,aACO,E,OACN,G,kBAGE,CAAC,E,WAEN,CAAC,E,OACE,CAAC,E,MAEE,C,MACL,C,KACC,C,QACK,SAASoC,GAAK,E,UACZ,SAASA,GAAK,E,SACf,W,MACD,E,aAEG,W,MACH,CAAC,C,SAIR,O,UACK,M,YACE,W,OACJj6B,KAAKgjC,MAAMhjC,KAAK6tB,K,IDMb+f,CAAUJ,GAEtB/T,EASEnlB,EATFmlB,OACAkC,EAQErnB,EARFqnB,OACAiQ,EAOEt3B,EAPFs3B,MACA5K,EAME1sB,EANF0sB,OACA6M,EAKEv5B,EALFu5B,SACAxW,EAIE/iB,EAJF+iB,KACA6R,EAGE50B,EAHF40B,IACA+B,EAEE32B,EAFF22B,OACAhf,EACE3X,EADF2X,MAGIgO,EE7EK,SAACR,EAAQwR,EAAQ/B,G,OAOjB,SAALjP,EAAc5uB,G,SACN4uB,EAAG5uB,UAAYC,GAAOD,G,EAE3BmgB,MAAQngB,EAAU1J,OAAOmsC,Y,EACzBriB,OAASpgB,EAAU1J,OAAOosC,a,CAEhC,OAAQ,aAAc,UAAW,SAAU,aAAajU,SAAQ,SAAAkU,G,EACxDA,GAAS3iC,EACb0B,OAAO,UACPiB,KAAK,QAASggC,GACdrsC,O,EACCqsC,GAAS/C,EAAO+C,GAAOC,WAAW,K,MAIrCvrC,IAAM2I,EACN0B,OAAO,OACPiB,KAAK,QAASyrB,EAAOjO,OACrBxd,KAAK,SAAUyrB,EAAOhO,QACtBjnB,MAAM,OAAQ,mBACdA,MAAM,WAAY,YAElBuI,OAAO,SACPiB,KACC,YACA,aAAeyrB,EAAOC,OAAO9pB,KAAO,IAAM6pB,EAAOC,OAAO5pB,IAAM,KAG3DmqB,C,EF0CEtlB,CAAK8kB,EAAQwR,EAAQ/B,GAE1B/Q,EAAW,SAAApuB,G,OACe,IAA1Bi3B,EAAOhJ,QAAQz3B,Q,EACVy3B,MAAM,CAAC,EAAGvN,GAAEgP,IAAU,GAET,MAAfoU,EAAS9jC,GAAai3B,EAAOj3B,GAAK8jC,EAAS9jC,E,EAG9C8hC,EAAehT,GAAY6R,GAAYjR,EAAQyP,EAAK/Q,IACvDmB,KAAK,IACL9J,OAAM,W,OAAMyK,EAAGzK,MAAM,U,IAElBsc,EAAcjT,GAAY0R,GAAS9Q,EAAQyP,EAAK/Q,IACnDmB,KAAK,IACL9J,OAAM,W,OAAMyK,EAAGzK,MAAM,S,IAElBuc,EAAkBlT,GAAYwS,GAAe5R,EAAQyP,EAAK/Q,IAC7DmB,KAAK,IACL9J,OAAM,W,EACFA,MAAM,c,EACNA,MAAM,Y,cAIXiK,EACAyP,EACAjP,EACA+G,EACA4K,EACAC,EACAC,EACAC,EACApQ,EACAtE,G,EAIC/iB,MAAQmlB,E,EACRmS,MAAQA,E,EAERY,UGzHa,SAAC/S,EAAQQ,EAAI+G,EAAQkI,G,OACrC,W,IAEQgF,EAAgB,C,KACd,SAASrpB,G,IACTspB,EAAUhiB,GAAOsN,EAAO/3B,MAAM,SAAAqI,G,OAAMA,EAAE8a,GAAK9a,EAAE8a,GAAGvlB,UAAY,I,WAE5D6uC,EAAQ,KAAOA,EAAQ,IAClBR,EAAAA,GAAAA,MACJ7V,OAAOqW,GACPnW,MAAM6L,GAASpK,KAEhBA,EAAO2U,SAASnR,SAASpY,K,EACjBspB,EAAQzhC,KAAI,SAAA2hC,G,OAAOC,SAASC,QAAQF,E,MAEzCG,EAAAA,GAAAA,MACJ1W,OAAOqW,GACPnW,MAAM6L,GAASpK,I,SAEZ,SAAS5U,G,IACXspB,EAAUhiB,GAAOsN,EAAO/3B,MAAM,SAAAqI,G,OAAMA,EAAE8a,E,WAEtCspB,EAAQ,KAAOA,EAAQ,IAClBR,EAAAA,GAAAA,MACJ7V,OAAOqW,GACPnW,MAAM6L,GAASpK,KAEhBA,EAAO2U,SAASnR,SAASpY,K,EACjBspB,EAAQzhC,KAAI,SAAA2hC,G,OAAOC,SAASC,QAAQF,E,MAEzCI,EAAAA,GAAAA,MACJ3W,OAAOqW,GACPnW,MAAM6L,GAASpK,I,SAEZ,SAAS5U,G,IACX6pB,EAAS,CAAC,EACZ5W,EAAS,G,KAGJp2B,KAAKgL,KAAI,SAAAmkB,G,QACD7d,IAAT6d,EAAEhM,IAAkD,cAA9B4U,EAAOqK,mB,OACxB,UAEY9wB,IAAjB07B,EAAO7d,EAAEhM,I,EACJgM,EAAEhM,IAAM,E,EAERgM,EAAEhM,IAAM6pB,EAAO7d,EAAEhM,IAAM,C,IAG9B4U,EAAO2U,SAASnR,SAASpY,G,EAClBhN,OAAOioB,oBAAoB4O,GAAQhhC,Y,QAExCihC,EAAU92B,OAAOioB,oBAAoB4O,GAAQhhC,OACxC9L,EAAI,EAAGA,EAAIiW,OAAOioB,oBAAoB4O,GAAQnuC,OAAQqB,I,EACtDsG,KAAKymC,EAAQ54B,O,IAKpB64B,EAAmB,GACD,IAAlB9W,EAAOv3B,S,EAEA,CAAC,IAAKu3B,EAAO,GAAI,M,QAExB+W,EAAQhL,GAASpK,GAAQ,IAAM3B,EAAOv3B,OAAS,GAC1CiJ,EAAI,EAAGA,EAAIsuB,EAAOv3B,OAAQiJ,IACD,IAA5BolC,EAAiBruC,O,EAIJ2H,KAAK0mC,EAAiBplC,EAAI,GAAKqlC,G,EAH7B3mC,KAAK,G,OAKnB4mC,EAAAA,GAAAA,MACJhX,OAAOA,GACPE,MAAM4W,E,UAGNviB,KAAKoN,EAAOY,YAAYP,SAAQ,SAASjV,QAEZ7R,IAAhCymB,EAAOY,WAAWxV,GAAG8V,QACW,OAAhClB,EAAOY,WAAWxV,GAAG8V,S,EAEdN,WAAWxV,GAAG8V,OAASuT,EAAczU,EAAOY,WAAWxV,GAAGnb,MAC/Dmb,G,MAOCmT,MAAM,CAAC,EAAGvN,GAAEgP,KAAUsV,QAAQ,I,IAG/BC,EAAmBlwC,OAAOkwC,kBAAoB,E,SAGjD3jC,UACAK,UAAU,UACVlH,MAAM,aAAci1B,EAAOC,OAAO5pB,IAAM,MACxCtL,MAAM,cAAei1B,EAAOC,OAAO9pB,KAAO,MAC1CpL,MAAM,QAASimB,GAAEgP,GAAU,EAAI,MAC/Bj1B,MAAM,SAAUmf,GAAE8V,GAAU,EAAI,MAChCzrB,KAAK,SAAUyc,GAAEgP,GAAU,GAAKuV,GAChChhC,KAAK,UAAW2V,GAAE8V,GAAU,GAAKuV,G,EAEhC7D,WAAWV,YAAchR,EAAOnX,M,EAChC6oB,WAAW8D,UAAYxV,EAAOwV,U,EAC9B9D,WAAWa,yBAA2BvS,EAAOyV,U,EAC7C/D,WAAWc,YAAcxS,EAAO0V,M,EAChChE,WAAW30B,MAAMw4B,EAAkBA,G,EACnCnT,QAAQ4O,YAAchR,EAAOkR,a,EAC7B9O,QAAQoT,UAAYxV,EAAOwV,U,EAC3BpT,QAAQmQ,yBAA2BvS,EAAOyV,U,EAC1CrT,QAAQoQ,YAAcxS,EAAO0V,M,EAC7BtT,QAAQrlB,MAAMw4B,EAAkBA,G,EAChChE,UAAUiE,UAAYxV,EAAO2V,qB,EAC7BpE,UAAUx0B,MAAMw4B,EAAkBA,G,EAClCxE,OAAOyE,UAAYxV,EAAO4V,gB,EAC1B7E,OAAO8E,YAAc7V,EAAO8V,kB,EAC5B/E,OAAOgF,WAAa/V,EAAOgW,iB,EAC3BjF,OAAOh0B,MAAMw4B,EAAkBA,GAE5BhvC,I,EHDMwsC,CAAU/S,EAAQQ,EAAI+G,EAAQkI,G,EAC1C1yB,MIjIS,SAACijB,EAAQQ,G,OACrB,SAASlwB,EAAG+tB,G,SACHuC,WAAWtwB,GAAG4wB,OAAO7C,OAAOA,G,EAChCmB,OAAOyW,U,EACPpD,aAEItsC,I,EJ2HEwW,CAAMijB,EAAQQ,G,EACtBsJ,KKlIQ,SAAA9J,G,OACX,SAAS1vB,G,SAEAswB,WAAWtwB,GAAG4wB,OAAO7C,OAC1B2B,EAAOY,WAAWtwB,GAAG4wB,OAAO7C,SAASiD,WAGhC/6B,I,EL2HCujC,CAAK9J,G,EACZkW,YMjIe,SAAClW,EAAQQ,G,OAC3B,SAASp7B,EAAQ6K,G,IACTiF,EAAIjF,GAAQ,SACI,qBAAX7K,I,GACA,GAINgZ,OAAOwU,KAAKoN,EAAOY,YAAY95B,Q,EAC/BgsC,mB,EAEFC,Y,IAGGoD,EAAS/3B,OAAOwU,KAAKoN,EAAOY,YAAYzuB,QAC5C,SAAAilB,G,OAAK4I,EAAOY,WAAWxJ,GAAGnnB,MAAQiF,C,OAGhC9P,EAAQ,C,IACNsvC,EAAUhiB,GACZyjB,EACGljC,KAAI,SAAA3C,G,OAAK0vB,EAAOY,WAAWtwB,GAAG4wB,OAAO7C,Q,IACrCwC,QAAO,SAACE,EAAKD,G,OAAQC,EAAInpB,OAAOkpB,E,OAG9BT,SAAQ,SAAA/vB,G,EACNswB,WAAWtwB,GAAG4wB,OAAO7C,OAAOqW,E,WAG9BrU,SAAQ,SAAA/vB,G,EACNswB,WAAWtwB,GAAG4wB,OAAO7C,OAAO3L,GAAOsN,EAAO/3B,MAAM,SAAAqI,G,OAAMA,EAAE8a,E,eAKpC,OAA3B4U,EAAO4P,iB,EACNA,gBAAgB5P,EAAO4P,iBAGrBrpC,I,EN0FQ2vC,CAAYlW,EAAQQ,G,EAClCsS,iBOpIoB,SAAAtS,G,OACvB,W,SACKI,WAAWJ,EAAGkS,0BACVnsC,I,EPiIausC,CAAiBtS,G,EAEpCuR,qBAAuBA,G,EACvBW,uBvBrI0B,SAAC1S,EAAQQ,G,OACtC,SAAS0F,G,IACDruB,EAAQ2oB,EAAGuR,qBAAqB/R,EAAO/3B,M,SACtCi+B,GAAc9nB,OAAOwU,KAAK/a,IAErBgpB,QAAO,SAACC,EAAKC,EAAK54B,G,IACtBijB,EAAI4U,EAAOY,WAAWG,GAAOf,EAAOY,WAAWG,GAAO,CAAC,E,SACzDA,GAAJqV,GAAA,GACKhrB,EADL,C,OAEUmf,GAAQnf,EAAEyS,QAAUzS,EAAEyS,OAAS,O,MAChC0M,GAAQnf,EAAEgT,OAAShT,EAAEgT,MAAQ,E,cACrBmM,GAAQnf,EAAEsf,eAAiBtf,EAAEsf,cAAgB,E,cAC7CH,GAAQnf,EAAEuf,eAAiBvf,EAAEuf,cAAgB,E,YAC/CJ,GAAQnf,EAAE+S,aAAe/S,EAAE+S,YAAc,E,KAChDoM,GAAQnf,EAAEnb,MAAQmb,EAAEnb,KAAO4H,EAAMkpB,G,MAChCwJ,GAAQnf,EAAEjc,OAASic,EAAEjc,MAAQhH,IAG/B24B,C,GACN,CAAC,E,EuBkHsB4R,CAAuB1S,EAAQQ,G,EACxDgH,wBQtI2B,SAAAxH,G,OAAU,W,OACxC5hB,OAAOwU,KAAKoN,EAAOY,YAAY3sB,MAAK,SAACzB,EAAGoD,G,OACtChN,GAAUo3B,EAAOY,WAAWpuB,GAAGrD,MAAO6wB,EAAOY,WAAWhrB,GAAGzG,M,KRoIhCq4B,CAAwBxH,G,EAGlDR,OShIU,SAACQ,EAAQQ,EAAI0B,G,OAC1B,W,OAEO9jB,OAAOwU,KAAKoN,EAAOY,YAAY95B,Q,EAC/BgsC,mB,EAEFC,Y,EAEAvT,OAAOQ,EAAO5L,Q,EAEVzrB,KAAK,SAAUpC,MACfA,I,ETqHGi5B,CAAOQ,EAAQQ,EAAI0B,G,EAC5BX,cZ/GiB,SAACvB,EAAQQ,EAAI0B,G,OACjC,W,OACO9jB,OAAOwU,KAAKoN,EAAOY,YAAY95B,QAAQ05B,EAAGsS,mB,EAE5CvR,cAAcvB,EAAO5L,Q,EACjBzrB,KAAK,SAAUpC,MACfA,I,EYyGUg7B,CAAcvB,EAAQQ,EAAI0B,G,EAC1CmU,abrHgB,SAACrW,EAAQQ,EAAI0B,G,OAChC,W,OACO9jB,OAAOwU,KAAKoN,EAAOY,YAAY95B,QAAQ05B,EAAGsS,mB,EAE5CuD,aAAarW,EAAO5L,Q,EAChBzrB,KAAK,SAAUpC,MACfA,I,Ea+GS8vC,CAAarW,EAAQQ,EAAI0B,G,EACxC1C,OAAOyW,QRtIU,SAACjW,EAAQQ,EAAIiP,EAAK/Q,G,OAAa,W,EAChD3I,MAAM,c,EACNA,MAAM,a,EAENwL,cAAc0U,U,EACdI,aAAaJ,U,EAEThuC,KAAKo4B,QAAQuR,GAAe5R,EAAQyP,EAAK/Q,G,EQ+H5B4X,CAActW,EAAQQ,EAAIiP,EAAK/Q,G,EAChDc,OAAO+W,MR7He,SAACvW,EAAQQ,EAAI8R,G,OAAoB,W,EACvD/Q,cAAcgV,Q,EACdF,aAAaE,Q,EACAvW,EAAO/3B,K,EQ0HLuuC,CAAmBxW,EAAQQ,EAAI8R,G,EAC9C/Q,cAAc0U,QZnIU,SAACjW,EAAQyP,EAAK/Q,EAAU8B,EAAIiB,G,OAAe,W,EACnE1L,MAAM,WAEL6U,GAAU5K,EAAQyB,KAAkC,IAAnBzB,EAAOoC,S,EACnCA,QAAQ/B,QAAQ4Q,GAAYjR,EAAQyP,EAAK/Q,G,EY+HvB+X,CACzBzW,EACAyP,EACA/Q,EACA8B,EACAhO,G,EAEC+O,cAAcgV,MZlIQ,SAACvW,EAAQyB,EAAY2Q,G,OAAiB,WAC3DxH,GAAU5K,EAAQyB,G,EACPzB,EAAOoC,S,EAEP,G,EY8HUsU,CAAmB1W,EAAQxN,EAAO4f,G,EACxDiE,aAAaJ,QbhJU,SAACjW,EAAQQ,EAAIiP,EAAK/Q,G,OAAa,W,EACtD3I,MAAM,UAELiK,EAAO+Q,OAAOjqC,Q,EACTiqC,OAAO1Q,QAAQyQ,GAAS9Q,EAAQyP,EAAK/Q,G,Ea4IpBiY,CAAoB3W,EAAQQ,EAAIiP,EAAK/Q,G,EAC5D2X,aAAaE,MbzIQ,SAACvW,EAAQqS,G,OAAgB,WAC7CrS,EAAO+Q,O,EACG/Q,EAAO+Q,Q,EAEP,G,EaqIU6F,CAAkB5W,EAAQqS,G,EAE/CwE,uBU3JwB,SAAC7W,EAAQtB,G,OAAa,SAAAiR,G,OACjDvxB,OAAOwU,KAAKoN,EAAOY,YAAY3tB,KAAI,SAAA3C,G,MAG1B,CAFGouB,EAASpuB,GACT0vB,EAAOY,WAAWtwB,GAAG4wB,OAAOyO,EAAIr/B,I,KVwJhBwmC,CAAqB9W,EAAQtB,G,EACtDqY,QW5JW,SAAC5E,EAAO3R,G,OACtB,W,SACQuW,SAAU,E,EACbC,eAAe,I,EACfxX,SACIj5B,I,EXuJIwwC,CAAQ5E,EAAO3R,G,EACzByW,SYzJY,SAACjX,EAAQQ,EAAI9B,G,OAAa,SAAAwY,G,IACnC3tB,EAAI2tB,GAAM,GACVzH,EAAMjP,EAAGiP,IAAI0H,KAEb7e,EAAW,EAAIvyB,KAAKuV,G,EACtBk3B,YCTS,SAAStmB,EAAQmR,GAC9B,IAEIlzB,EACA0J,EAHAtH,EAAI2f,EAAOplB,OACXqB,GAAK,EAIT,GAAe,MAAXk1B,GACF,OAASl1B,EAAIoE,GACX,GAA2B,OAAtBpC,EAAQ+hB,EAAO/jB,KAAegC,GAASA,EAE1C,IADA0J,EAAM1J,IACGhC,EAAIoE,GACgB,OAAtBpC,EAAQ+hB,EAAO/jB,KAAe0L,EAAM1J,IACvC0J,EAAM1J,QAQd,OAAShC,EAAIoE,GACX,GAA+C,OAA1CpC,EAAQkzB,EAAQnR,EAAO/jB,GAAIA,EAAG+jB,KAAoB/hB,GAASA,EAE9D,IADA0J,EAAM1J,IACGhC,EAAIoE,GACoC,OAA1CpC,EAAQkzB,EAAQnR,EAAO/jB,GAAIA,EAAG+jB,KAAoBrY,EAAM1J,IAC3D0J,EAAM1J,GAOhB,OAAO0J,CACT,CDxBoBA,CAAI,CAAC,EAAI9N,KAAKslB,IAAI2U,EAAO/3B,KAAKnB,OAAQ,IAAQ,I,EACzDmB,KAAKo4B,SAAQ,SAAA/vB,I,SACV0vB,EAAOY,YAAYP,SAAQ,SAACjJ,EAAGjvB,G,EACjCwoC,Y,EACAhW,IACF+D,EAAStH,GACT4I,EAAOY,WAAWxJ,EAAE7uB,KAAK24B,OAAO5wB,EAAE8mB,IAClC7N,EATa,EAWb+O,G,EAEEsY,S,EACAwG,M,QZwIMH,CAASjX,EAAQQ,EAAI9B,G,EAChC3I,Mc1JS,SAACiK,EAAQQ,EAAIiP,EAAKhO,G,OAC9B,SAAS8S,G,SACHA,GAAO8C,UAAU,EAAG,EAAGrmB,GAAEgP,GAAU,EAAG9V,GAAE8V,GAAU,GAKxC,YAAVuU,GAAuB3J,GAAU5K,EAAQyB,K,EACvCW,QAAQkV,UAAY9W,EAAG5uB,UAAU7G,MAAM,oB,EACvCq3B,QAAQoQ,YAAc,EAAIxS,EAAOgX,e,EACjC5U,QAAQkO,SAAS,EAAG,EAAGtf,GAAEgP,GAAU,EAAG9V,GAAE8V,GAAU,G,EAClDoC,QAAQoQ,YAAcxS,EAAO0V,OAE5BnvC,I,Ed6IEwvB,CAAMiK,EAAQQ,EAAIiP,EAAKjd,G,EAC/B8P,We5Ic,SAACtC,EAAQQ,EAAI+G,EAAQ4K,EAAOvU,G,OAC7C,W,YACiBrkB,IAAXinB,EAAGhX,K,EACF+tB,a,EAGFC,GAAKhX,EAAGv3B,IACRgJ,UAAU,cACVhK,KAAKu4B,EAAGgH,2BAA2B,SAASl3B,G,OACpCA,C,IAERvI,QACAuL,OAAO,SACPiB,KAAK,QAAS,aACdA,KAAK,aAAa,SAASjE,G,MACnB,aAAei3B,EAAOj3B,GAAK,G,MAGnCknC,GACAlkC,OAAO,SACPiB,KAAK,QAAS,QACdA,KAAK,YAAa,kBAClBhC,MAAK,SAASjC,G,IACTmnC,EAAc5lC,GAAOtL,MAAMoC,KAC7B63B,EAAGgK,gBAAgB5M,EAAMoC,EAAOY,WAAWtwB,K,EAI1C2B,UAAU,QACVlH,MAAM,OAAQ,QACdA,MAAM,SAAU,QAChBA,MAAM,kBAAmB,c,EAGzBkH,UAAU,QACVlH,MAAM,OAAQ,QACdA,MAAM,SAAU,QAChBA,MAAM,kBAAmB,a,IAG7BuI,OAAO,YACPiB,KAAK,cAAe,UACpBA,KAAK,IAAK,GACVA,KACC,YACA,0BAA4ByrB,EAAOmK,uBAAyB,KAE7D51B,KAAK,IAAK,GACVA,KAAK,QAAS,SACdI,KAAKg1B,GAAgB3J,IACrBpwB,GAAG,WAAYi6B,GAAqB7J,EAAQQ,EAAI5C,IAChDhuB,GAAG,QAASo6B,GAAahK,EAAQQ,IAEF,QAA9BR,EAAOqK,mB,EACNphC,IACAqK,OAAO,QACPiB,KAAK,KAAM,GACXA,KAAK,KAAM,EAAIyrB,EAAOsK,0BAA0Bj0B,KAChD9B,KAAK,KAAMyc,GAAEgP,IACbzrB,KAAK,KAAM,EAAIyrB,EAAOsK,0BAA0Bj0B,KAChD9B,KAAK,eAAgB,GACrBA,KAAK,SAAU,QACfA,KAAK,OAAQ,QACbA,KAAK,kBAAmB,cACY,WAA9ByrB,EAAOqK,oB,EACbphC,IACAqK,OAAO,QACPiB,KAAK,KAAM,GACXA,KAAK,KAAM2V,GAAE8V,GAAU,EAAIA,EAAOsK,0BAA0BjD,QAC5D9yB,KAAK,KAAMyc,GAAEgP,IACbzrB,KAAK,KAAM2V,GAAE8V,GAAU,EAAIA,EAAOsK,0BAA0BjD,QAC5D9yB,KAAK,eAAgB,GACrBA,KAAK,SAAU,QACfA,KAAK,OAAQ,QACbA,KAAK,kBAAmB,c,EAGvBmjC,MAAO,EACNnxC,I,Ef8DO+7B,CAAWtC,EAAQQ,EAAI+G,EAAQ4K,EAAOvU,G,EACnD2Z,WgBhKc,SAAA/W,G,OACjB,W,SACKgX,GAAG9qC,gBAEC8zB,EAAGgX,GACHjxC,I,EhB2JOgxC,CAAW/W,G,EACxBqS,WiB3Jc,SAAC7S,EAAQQ,EAAI9B,EAAUd,EAAMuU,G,OAAU,W,IACxDpI,EACGr/B,UAAA5D,OAAA,QAAAyS,IAAA7O,UAAA,GAAAA,UAAA,GADa,KAEM,OAAlBq/B,I,EACc/J,EAAO+J,e,IAGnB4N,EAASnX,EAAGv3B,IACfgJ,UAAU,cACVhK,KAAKu4B,EAAGgH,2B,KAGRz/B,QACAuL,OAAO,SACPiB,KAAK,QAAS,aACdA,KAAK,aAAa,SAAA6iB,G,MAAK,aAAesH,EAAStH,GAAK,G,IACpDrsB,MAAM,UAAW,GACjBuI,OAAO,SACPiB,KAAK,QAAS,QACdA,KAAK,YAAa,kBAClBhC,MAAK,SAASjC,G,IACPmnC,EAAc5lC,GAAOtL,MAAMoC,KAC/B63B,EAAGgK,gBAAgB5M,EAAMoC,EAAOY,WAAWtwB,K,EAI1C2B,UAAU,QACVlH,MAAM,OAAQ,QACdA,MAAM,SAAU,QAChBA,MAAM,kBAAmB,c,EAGzBkH,UAAU,QACVlH,MAAM,OAAQ,QACdA,MAAM,SAAU,QAChBA,MAAM,kBAAmB,a,IAE7BuI,OAAO,YACPiB,KAAK,cAAe,UACpBA,KAAK,QAAS,SACdA,KAAK,IAAK,GACVA,KAAK,IAAK,GACVA,KACC,YACA,0BAA4ByrB,EAAOmK,uBAAyB,KAE7Dx1B,KAAKg1B,GAAgB3J,IACrBpwB,GAAG,WAAYi6B,GAAqB7J,EAAQQ,EAAI5C,IAChDhuB,GAAG,QAASo6B,GAAahK,EAAQQ,I,EAG7BjsB,KAAK,UAAW,G,EAEpB1C,OAAO,SACPkM,aACA/C,SAAS+uB,GACTx3B,MAAK,SAASjC,G,GACN/J,MAAMoC,KAAK63B,EAAGgK,gBAAgB5M,EAAMoC,EAAOY,WAAWtwB,I,MAG9DuB,OAAO,UACPkM,aACA/C,SAAS+uB,GACTp1B,KAAKg1B,GAAgB3J,IACrBzrB,KACC,YACA,0BAA4ByrB,EAAOmK,uBAAyB,K,EAIzDniC,OAAO0E,SAEJ8zB,EAAGv3B,IAAIgJ,UAAU,cACzB8L,aACC/C,SAAS+uB,GACTx1B,KAAK,aAAa,SAAA6iB,G,MAAK,aAAesH,EAAStH,GAAK,G,IACpDrsB,MAAM,UAAW,G,EAEjB9B,IACAgJ,UAAU,SACV8L,aACA/C,SAAS+uB,GACTx3B,MAAK,SAASjC,G,GACN/J,MAAMoC,KAAK63B,EAAGgK,gBAAgB5M,EAAMoC,EAAOY,WAAWtwB,I,IAG7D6hC,EAAMyF,WAAWpX,EAAGoX,YACpBzF,EAAM0F,aAAarX,EAAGqX,cACH,SAAnBrX,EAAG6I,YAAwB,C,IACvBjV,EAAOoM,EAAG6I,Y,EACbA,UAAU,Q,EACVA,UAAUjV,E,GjBgECye,CAAW7S,EAAQQ,EAAI9B,EAAUd,EAAMuU,G,EACpD3H,gBAAkBA,G,EAClBoN,UkBhKa,SAAC5X,EAAQQ,EAAI2R,G,OAC7B,W,OACO3R,EAAGhX,K,EACH8Y,aAGK9B,EAAGhX,IAGXlW,OAAO,SACNiB,KAAK,QAAS,SACdhC,MAAK,SAASjC,QACgBiJ,IAAzBymB,EAAOY,WAAWtwB,K,EACbswB,WAAWtwB,GAAlB,MAAgCiiB,GAAO1gB,GAAOtL,OAAOmsB,OAAO,CAC1D,EAAE,GAAI,GACN,CAAC,GAAIsN,EAAOY,WAAWtwB,GAAG4wB,OAAO3C,QAAQ,M,GAEpCh4B,MAAMoC,KACXq3B,EAAOY,WAAWtwB,GAAlB,MACGV,GAAG,SAAS,WACe,OAAtBd,GAAM6B,aAAyB7B,GAAM6B,YAAY+gB,S,EAChDoR,Y,IAGNlzB,GAAG,SAAS,WACNd,GAAM6B,YAAY+gB,S,EAClBc,O,IAGN5iB,GAAG,OAAO,W,GAKLd,GAAM6B,YAAY+gB,QAAS,C,IACzB9c,EAAO/C,GAAOtL,MACfsL,OAAO,cACPyC,QAAQ,GAAGwjC,U,EACPljC,EAAKmjC,QACV,oBACA,oCAEE/X,EAAOU,QAAQ55B,OACf,K,IAEAkxC,EAAMnmC,GAAOtL,MAAM+N,QAAQ,GAAGhN,SAC9BkrB,EAAQ,C,GACNwN,EAAOU,QAAQ55B,O,OACXurB,GAAe9rB,M,KACjBqO,E,KACAojC,G,EAEDtX,QAAQjyB,KAAK+jB,G,GACb3gB,GAAOtL,MAAM+N,QAAQ,GAAG9G,YAC5BqE,OAAO,SACPyC,QAAQ,GAAGwjC,WAAaljC,E,EACxB4d,Q,EACIoO,WAAWtwB,GAAGkiB,MAAMgD,KAAK3jB,GAAOtL,O,GAChCA,MACJsL,OAAO,cACP0C,KAAK,QAAS,gB,EACdqjC,W,QAEAplB,O,QAIJjsB,MAAMqJ,GAAG,YAAY,W,EACvBkzB,WAAWxyB,E,UAKhBsnC,WAAY,EACXrxC,I,ElBsFMqxC,CAAU5X,EAAQQ,EAAI2R,G,EAClCrP,WmBlKc,SAAC9C,EAAQQ,G,OAC1B,SAASqB,G,QACDoW,EAAgB,GACbloC,EAAI,EAAGA,EAAIiwB,EAAOU,QAAQ55B,OAAQiJ,IACrCiwB,EAAOU,QAAQ3wB,GAAG9H,OAAS45B,G,EACfpzB,KAAKuxB,EAAOU,QAAQ3wB,I,KAI/B2wB,QAAUuX,E,EACV7V,SAAU,OAEF7oB,IAAXinB,EAAGhX,I,QACClV,EAAQksB,EACXhX,IACAvX,UAAU,UACVqC,QACMnM,EAAI,EAAGA,EAAImM,EAAMxN,OAAQqB,IAC5BmM,EAAMnM,GAAGb,WAAau6B,I,GAEjBhwB,GAAOyC,EAAMnM,IAAImM,QAAQ,GAAG9G,YAChCyE,UAAU,UACVvF,S,EACIk0B,WAAWiB,GAAWrP,MAAMgD,KAAK3jB,GAAOyC,EAAMnM,M,OAKpD5B,I,EnBsIO2xC,CAAWlY,EAAQQ,G,EAChCgB,SoBnKY,SAACxB,EAAQQ,G,OAAO,W,IAC3BkB,EAAU,GACVjB,EAAU,GACV0X,EAAS,CAAC,E,GAGgB,IAA1BnY,EAAOU,QAAQ55B,OAAc,C,QAC3BwN,EAAQksB,EACThX,IACAvX,UAAU,UACVqC,QACM8W,EAAI,EAAGA,EAAI9W,EAAMxN,OAAQskB,I,GACC,OAA7BiH,GAAe/d,EAAM8W,IAAc,C,EAC7B3c,KAAK6F,EAAM8W,GAAG9jB,U,IAClB4kB,EAAS,GACTksB,EAAS/lB,GAAe/d,EAAM8W,I,GAGhC,kBADO4U,EAAOY,WAAWtsB,EAAM8W,GAAG9jB,UAAU45B,OAAO7C,SAAS,GAE5D,C,IACK,IAAIl2B,EAAI,EAAGA,EAAIiwC,EAAOtxC,OAAQqB,IAE/Bu5B,EAAQ8B,SAASlvB,EAAM8W,GAAG9jB,WAC1B04B,EAAO2U,SAASnR,SAASlvB,EAAM8W,GAAG9jB,U,EAE3BmH,KACLuxB,EAAOY,WAAWtsB,EAAM8W,GAAG9jB,UAAU45B,OAAOf,OAAOiY,EAAOjwC,KAED,IAAlD63B,EAAOY,WAAWtsB,EAAM8W,GAAG9jB,UAAU45B,U,EACvC4T,QACL9U,EAAOY,WAAWtsB,EAAM8W,GAAG9jB,UAAU45B,OAAOf,OAAOiY,EAAOjwC,K,EAIxDsG,KAAKyd,G,IACR,IAAImsB,EAAK,EAAGA,EAAK5X,EAAQ35B,OAAQuxC,IACT,IAAvB5X,EAAQ4X,GAAIvxC,S,EACNuxC,GAAM,CAAC,EAAG,G,KAGjB,C,EACE/jC,EAAM8W,GAAG9jB,UAAY+qB,GAAe/d,EAAM8W,I,QAC7CktB,EAAWtY,EAAOY,WAAWtsB,EAAM8W,GAAG9jB,UAAU45B,OAAO3C,QAElDxuB,GADOiwB,EAAOY,WAAWtsB,EAAM8W,GAAG9jB,UAAU45B,OAAO7C,SAC/C,GAAGtuB,EAAIuoC,EAASxxC,OAAQiJ,IAEjCuoC,EAASvoC,IAAMqoC,EAAO,IACtBE,EAASvoC,IAAMqoC,EAAO,IACtB1W,EAAQ8B,SAASlvB,EAAM8W,GAAG9jB,WAC1B04B,EAAO2U,SAASnR,SAASlvB,EAAM8W,GAAG9jB,U,EAE3BmH,KAAK6pC,EAASvoC,IACZuoC,EAASvoC,IAAMqoC,EAAO,IAAME,EAASvoC,IAAMqoC,EAAO,I,EACpDtD,QAAQwD,EAASvoC,I,EAGpBtB,KAAKyd,G,IACR,IAAIqsB,EAAK,EAAGA,EAAK9X,EAAQ35B,OAAQyxC,IACT,IAAvB9X,EAAQ8X,GAAIzxC,S,EACNyxC,GAAM,CAAC,EAAG,G,MAOtB3W,EAAS,C,KACP,SAAStxB,EAAG8mB,EAAGyK,G,IACf2W,EAAWloC,EAAE8mB,GACbqhB,EAAgBzY,EAAOY,WAAWxJ,GAAG8J,OACtC7C,SACA90B,QAAQivC,GACPE,EAAqB1Y,EAAOY,WAAWxJ,GAAG8J,OAAO3C,QACnDka,G,OAGAC,GAAsBP,EAAO/gB,GAAG,IAChCshB,GAAsBP,EAAO/gB,GAAG,E,SAG5B,SAAS9mB,EAAG8mB,EAAGyK,G,OACdpB,EAAQoB,GAAW,IAAMvxB,EAAE8mB,IAAM9mB,EAAE8mB,IAAMqJ,EAAQoB,GAAW,E,SAE7D,SAASvxB,EAAG8mB,EAAGyK,G,IACjB2W,EAAWloC,EAAE8mB,GACbqhB,EAAgBzY,EAAOY,WAAWxJ,GAAG8J,OACtC7C,SACA90B,QAAQivC,GACPE,EAAqB1Y,EAAOY,WAAWxJ,GAAG8J,OAAO3C,QACnDka,G,OAGAC,GAAsBP,EAAO/gB,GAAG,IAChCshB,GAAsBP,EAAO/gB,GAAG,E,UAI/B4I,EAAO/3B,KAAKkK,QAAO,SAAA7B,G,OACxBoxB,EAAQ7S,OAAM,SAACuI,EAAGyK,G,OAChBD,EAAO5B,EAAOY,WAAWxJ,GAAGnnB,MAAMK,EAAG8mB,EAAGyK,E,eAOxC8W,EAAiB,G,WACZC,G,IACHpmB,EAAQwN,EAAOU,QAAQkY,GACvB1sB,EAAS,GACTksB,EAAS5lB,EAAME,OACfgP,EAAU,CAAClP,EAAMvqB,M,GAEyC,kBAArD+3B,EAAOY,WAAWpO,EAAMvqB,MAAMi5B,OAAO7C,SAAS,GACrD,C,IACK,IAAIwa,EAAI,EAAGA,EAAIT,EAAOtxC,OAAQ+xC,IAE/BnX,EAAQ8B,SAAShR,EAAMvqB,OACvB+3B,EAAO2U,SAASnR,SAAShR,EAAMvqB,M,EAExBwG,KAAKuxB,EAAOY,WAAWpO,EAAMvqB,MAAMi5B,OAAOf,OAAOiY,EAAOS,KACX,IAA3C7Y,EAAOY,WAAWpO,EAAMvqB,MAAMi5B,U,EAChC4T,QACL9U,EAAOY,WAAWpO,EAAMvqB,MAAMi5B,OAAOf,OAAOiY,EAAOS,K,EAIjDpqC,KAAKyd,G,IACR,IAAI4sB,EAAK,EAAGA,EAAKrY,EAAQ35B,OAAQgyC,IACT,IAAvBrY,EAAQqY,GAAIhyC,S,EACNgyC,GAAM,CAAC,EAAG,G,KAGjB,C,EACEtmB,EAAMvqB,MAAQuqB,EAAME,O,QACvBqmB,EAAW/Y,EAAOY,WAAWpO,EAAMvqB,MAAMi5B,OAAO3C,QAE3Cya,GADOhZ,EAAOY,WAAWpO,EAAMvqB,MAAMi5B,OAAO7C,SACxC,GAAG2a,EAAID,EAASjyC,OAAQkyC,IAEjCD,EAASC,IAAMZ,EAAO,IACtBW,EAASC,IAAMZ,EAAO,IACtB1W,EAAQ8B,SAAShR,EAAMvqB,OACvB+3B,EAAO2U,SAASnR,SAAShR,EAAMvqB,M,EAExBwG,KAAKsqC,EAASC,IACZD,EAASC,IAAMZ,EAAO,IAAMW,EAASC,IAAMZ,EAAO,I,EACpDtD,QAAQiE,EAASC,I,EAGpBvqC,KAAKyd,G,IACR,IAAI+sB,EAAK,EAAGA,EAAKxY,EAAQ35B,OAAQmyC,IACT,IAAvBxY,EAAQwY,GAAInyC,S,EACNmyC,GAAM,CAAC,EAAG,G,SAIpBrX,EAAS,C,KACL,SAAStxB,EAAG8mB,EAAGyK,G,IACf2W,EAAWloC,EAAE8mB,GACbqhB,EAAgBzY,EAAOY,WAAWxJ,GAAG8J,OACtC7C,SACA90B,QAAQivC,GACPE,EAAqB1Y,EAAOY,WAAWxJ,GAAG8J,OAAO3C,QACnDka,G,OAGAC,GAAsBP,EAAO/gB,GAAG,IAChCshB,GAAsBP,EAAO/gB,GAAG,E,SAG5B,SAAS9mB,EAAG8mB,EAAGyK,G,OACdpB,EAAQmY,GAAK,IAAMtoC,EAAE8mB,IAAM9mB,EAAE8mB,IAAMqJ,EAAQmY,GAAK,E,SAEjD,SAAStoC,EAAG8mB,EAAGyK,G,IACjB2W,EAAWloC,EAAE8mB,GACbqhB,EAAgBzY,EAAOY,WAAWxJ,GAAG8J,OACtC7C,SACA90B,QAAQivC,GACPE,EAAqB1Y,EAAOY,WAAWxJ,GAAG8J,OAAO3C,QACnDka,G,OAGAC,GAAsBP,EAAO/gB,GAAG,IAChCshB,GAAsBP,EAAO/gB,GAAG,E,GAQlC8hB,EAAWlZ,EAAO/3B,KAAKkK,QAAO,SAAA7B,G,OAChCoxB,EAAQ7S,OAAM,SAACuI,EAAGyK,G,OAChBD,EAAO5B,EAAOY,WAAWxJ,GAAGnnB,MAAMK,EAAG8mB,EAAGyK,E,OAGnC0L,EAAI,EAAGA,EAAI2L,EAASpyC,OAAQymC,I,EACpB9+B,KAAKyqC,EAAS3L,I,EAErB,G,EACD,CAAC,C,EA7FHqL,EAAM,EAAGA,EAAM5Y,EAAOU,QAAQ55B,OAAQ8xC,I,EAAtCA,G,OA+FFD,C,EpBvCKQ,CAASnZ,EAAQQ,G,EAC5BqX,YqBhKe,SAAC7X,EAAQQ,EAAI+G,EAAQ7I,EAAU0V,EAAUjC,G,OAC3D,gBACiB54B,IAAXinB,EAAGhX,KAAmBgX,EAAG8B,a,IACvB9Y,EAAIgX,EAAGhX,I,SAEXze,MAAM,SAAU,QAAQpC,KACxBkuB,KACGjnB,GAAG,SAAS,SAASU,G,EACXA,GAAK/J,KAAK6yC,WAAa7R,EAAOj3B,E,IAExCV,GAAG,QAAQ,SAASU,G,EACVA,GAAKvK,KAAK8N,IACjBmd,GAAEgP,GACFj6B,KAAKC,IAAI,EAAIO,KAAK6yC,YAActqC,GAAM+lB,K,EAErC8d,iB,EACItU,OAAOmC,EAAGgH,2B,EACdhI,S,EACDjrB,KAAK,aAAa,SAAAjE,G,MAAK,aAAeouB,EAASpuB,GAAK,G,OAEvDV,GAAG,OAAO,SAASU,UACX/J,KAAK6yC,kBACLhF,EAAS9jC,G,GACT/J,MACJwX,aACAxJ,KAAK,YAAa,aAAegzB,EAAOj3B,GAAK,K,EAC7CkvB,S,EACA6W,c,OAGHwB,aAAc,EACbtxC,I,ErBiIQsxC,CAAY7X,EAAQQ,EAAI+G,EAAQ7I,EAAU0V,EAAUjC,G,EAKlEkH,QsBxKW,SAACrZ,EAAQQ,EAAI+G,G,OAAW,SAAA+R,G,IAChCC,EAAW/Y,EAAGgH,0BAA0B,G,KAE3CgS,wBAAwBF,GAITC,IAAa/Y,EAAGgH,0BAA0B,GAE7C,C,EACNnJ,OAAOmC,EAAGgH,2B,IACXiK,EAAczR,EAAOyR,YAAYjoC,MAAM,G,EAC1CiwC,c,IAEG1I,EAAS/Q,EAAO+Q,OAAOvnC,MAAM,G,EAChCkwC,SAEOlZ,EAAGhX,IACXzL,aACC/C,SAAS,MACTzG,KAAK,aAAa,SAAAjE,G,MAAK,aAAei3B,EAAOj3B,GAAK,G,MAClDkvB,SAGwB,IAAvBiS,EAAY3qC,Q,EACXyqC,UAAUE,GAEO,IAAlBV,EAAOjqC,Q,EACN6yC,KAAK5I,E,GtB4ICsI,CAAQrZ,EAAQQ,EAAI+G,G,EAC9BiS,wBuB5K2B,SAAAxZ,G,OAAU,SAAAsZ,G,IAClCvhC,EAAOqG,OAAO4M,OAAO,CAAC,EAAGgV,EAAOY,YAChCgZ,EAAqBx7B,OAAOwU,KAAKoN,EAAOY,YAAY3sB,MAAK,SAACpL,EAAGC,G,IAC3D+wC,EACJ7Z,EAAOY,WAAW/3B,GAAGq4B,OAAOoY,EAAQzwC,IACpCm3B,EAAOY,WAAW93B,GAAGo4B,OAAOoY,EAAQxwC,I,OAKX,IAApB+wC,EAAwBhxC,EAAEixC,cAAchxC,GAAK+wC,C,MAE/CjZ,WAAa,CAAC,E,EACFP,SAAQ,SAACjJ,EAAGjvB,G,EACtBy4B,WAAWxJ,GAAKrf,EAAKqf,G,EACrBwJ,WAAWxJ,GAAGjoB,MAAQhH,C,KvB6JFqxC,CAAwBxZ,G,EAClD2S,ewB7KkB,SAAC3S,EAAQtB,G,OAAa,W,IACrC3mB,EAAOqG,OAAO4M,OAAO,CAAC,EAAGgV,EAAOY,YAChCgZ,EAAqBx7B,OAAOwU,KAAKoN,EAAOY,YAAY3sB,MACxD,SAACpL,EAAGC,G,OAAO41B,EAAS71B,GAAK61B,EAAS51B,KAAO,EAAI,EAAI41B,EAAS71B,GAAK61B,EAAS51B,E,MAEnE83B,WAAa,CAAC,E,EACFP,SAAQ,SAACjJ,EAAGjvB,G,EACtBy4B,WAAWxJ,GAAKrf,EAAKqf,G,EACrBwJ,WAAWxJ,GAAGjoB,MAAQhH,C,KxBqKXwqC,CAAe3S,EAAQtB,G,EAGxCqb,eAAiB3I,G,EACjBwB,YyBjLe,SAAAT,G,OAClB,W,SACQS,aAAc,EACbrsC,I,EzB8KQqsC,CAAYT,G,EAG1B5K,OAASA,E,EACTkI,IAAMA,E,EACN+B,OAASA,E,EACThoB,EAAI,W,OAAMgX,EAAGgX,E,IAIb/E,O0BzLU,SAACzS,EAAQQ,EAAI2R,EAAOjQ,G,OAC1B,W,SAEFtwB,UACAC,OAAO,OACP0C,KAAK,QAASyrB,EAAOjO,OACrBxd,KAAK,SAAUyrB,EAAOhO,Q,EACtB/oB,IAAIsL,KACL,YACA,aAAeyrB,EAAOC,OAAO9pB,KAAO,IAAM6pB,EAAOC,OAAO5pB,IAAM,KAI5D87B,EAAMyF,WAAWpX,EAAGsC,a,EAGrBiQ,YAGCvS,EAAGhX,KAAKgX,EAAG8B,aACX6P,EAAMyF,WAAWpX,EAAGoX,YACpBzF,EAAM0F,aAAarX,EAAGqX,c,EAEnBlvC,KAAK,SAAUpC,KAAM,C,MACnBy5B,EAAOjO,M,OACNiO,EAAOhO,O,OACPgO,EAAOC,SAGV15B,I,E1B4JGksC,CAAOzS,EAAQQ,EAAI2R,EAAOjQ,G,EAGnCqP,UAAYA,GAAUvR,EAAQQ,EAAIgR,EAAQtP,EAAQuN,EAAK/Q,G,EAEvD+a,Y2B7Le,SAACzZ,EAAQQ,EAAIgR,G,OAC/B,W,SACSC,YAAc,G,EAClB1b,MAAM,a,GACC,CAACyb,EAAOE,WAAYF,EAAOpP,UAAU1tB,QAAQ,SAAS,GACzDnO,I,E3BwLQkzC,CAAYzZ,EAAQQ,EAAIgR,G,EAGtCmI,K4B9LQ,SAAC3Z,EAAQQ,EAAIgR,EAAQtP,EAAQuN,EAAK/Q,G,OAC7C,W,IAASz2B,EAAayC,UAAA5D,OAAA,QAAAyS,IAAA7O,UAAA,GAAAA,UAAA,GAAN,K,OACD,OAATzC,EACK+3B,EAAO+Q,Q,EAITA,OAAS/Q,EAAO+Q,OAAOn5B,OAAO3P,G,GAC3B,CAACupC,EAAOE,WAAYF,EAAOpP,UAAU1tB,QAAQ,UAAU,G,EAC5D2rB,QAAQyQ,GAAS9Q,EAAQyP,EAAK/Q,I,EAC5B/1B,KAAK,OAAQpC,KAAM0B,GACnB1B,K,E5BmLCozC,CAAK3Z,EAAQQ,EAAIgR,EAAQtP,EAAQuN,EAAK/Q,G,EAE7Cgb,O6BlMU,SAAC1Z,EAAQQ,EAAIgR,G,OAC1B,W,SACST,OAAS,G,EACbhb,MAAM,U,GACC,CAACyb,EAAOE,WAAYF,EAAOpP,UAAU1tB,QAAQ,UAAU,GAC1DnO,I,E7B6LGmzC,CAAO1Z,EAAQQ,EAAIgR,G,EAI5BpI,aAAeA,G,EAIf4Q,e8BzMkB,SAAAxZ,G,OAAM,SAAA96B,G,IAErB6vC,EAAmBlwC,OAAOkwC,kBAAoB,EAG9C0E,EAAepvC,SAASkD,cAAc,UAEtCmsC,EAAmB1Z,EAAGgR,OAAOE,WAE7ByI,EAAmBnI,OACvBkI,EAAiBnvC,MAAMqvC,WAAWrC,QAAQ,KAAM,KAI5CsC,EACJrI,OAAOkI,EAAiBnvC,MAAMuvC,UAAUvC,QAAQ,KAAM,KAFlC,GAGhBhmB,GACHmoB,EAAiB7F,YAAc8F,GAAoB5E,EAChDvjB,GACHkoB,EAAiB5F,aAAe+F,GAAmB9E,E,EACzCxjB,MAAQA,EAAQ,G,EAChBC,OAASA,EAAS,G,EAClBjnB,MAAMgnB,MAAQkoB,EAAaloB,MAAQwjB,EAAmB,K,EACtDxqC,MAAMinB,OAASioB,EAAajoB,OAASujB,EAAmB,K,IAG/D7a,EAAUuf,EAAazF,WAAW,M,IAKnC,IAAMjsC,K,EAJH+uC,UAAY,U,EACZhH,SAAS,EAAG,EAAG2J,EAAaloB,MAAOkoB,EAAajoB,QAGtCwO,EAAGgR,O,EACX+I,UACN/Z,EAAGgR,OAAOjpC,GACV4xC,EAAmB5E,EACnB8E,EAAkB9E,EAClBxjB,EAAQooB,EAAmB5E,EAC3BvjB,EAASqoB,EAAkB9E,GAKhBlwC,OAAOm1C,KAAOn1C,OAAOo1C,WAAap1C,O,IAC3Cq1C,EAAa,IAAIC,cAGjBC,EAAcpa,EAAG5uB,UACpBC,OAAO,OACP3J,OACAqG,WAAU,G,EACDnE,aAAa,YAAa,mB,EAC1BA,aACV,SACAwwC,EAAYzuC,aAAa,UAxCL,I,GA6CfyuC,GACJ3oC,UAAU,QACVsC,KAAK,OAAQ,S,IACVsmC,EAASH,EAAWI,kBAAkBF,GAGtCG,EAAM,6BAA+B11C,OAAO21C,KAAKH,GACjDI,EAAM,IAAIC,M,EACZC,OAAS,W,EACHZ,UACNU,EACA,EACA,EACAA,EAAIlpB,MAAQwjB,EACZ0F,EAAIjpB,OAASujB,GAES,oBAAb7vC,G,EACAu0C,E,IAGTc,IAAMA,C,E9B2HUf,CAAexZ,G,EAChC8I,WAAa,W,OAAMlrB,OAAOioB,oBAAoB7T,EAAM+W,M,IACpDF,UAAYA,GAAU7W,EAAOwN,EAAQQ,G+B1MpB,SAACiB,EAAYzB,EAAQQ,EAAI0B,G,IACvCrnB,EAAQ,C,QACH,CAAC,E,WACE,CAAC,G,EAGJ0uB,MAAM,WAAa,C,QACnBlH,GAAQxnB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,UACjCsB,GAAUloB,EAAO2lB,G,SAClBgB,GAAS3mB,EAAOmlB,EAAQyB,G,WACtBlB,GAAa1lB,EAAOmlB,EAAQQ,G,E/BmM5BhO,EAAOwN,EAAQQ,EAAI0B,GgC9MX,SAACT,EAAYzB,EAAQQ,EAAI0B,EAAQqF,G,IACjD1sB,EAAQ,C,OACJ,CAAC,E,UACE,CAAC,G,EAGH0uB,MAAM,aAAe,C,QACrB1B,GAAQpG,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQqF,G,UAC7ChC,GAAU1qB,EAAO2lB,G,SAClB2F,GAAS1E,EAAY5mB,EAAOmlB,G,WAC1B,W,OAAMnlB,EAAM8qB,M,IhCqMVnT,EAAOwN,EAAQQ,EAAI0B,EAAQqF,GiC/MjB,SAAC9F,EAAYzB,EAAQQ,EAAI0B,EAAQqF,G,IACrD1sB,EAAQ,C,KACN,CAAC,E,UACI,CAAC,G,EAGH0uB,MAAX,QAA8B,C,QACnBP,GAAQvH,EAAY5mB,EAAOmlB,EAAQQ,EAAI0B,EAAQqF,G,UAC7CS,GAAUntB,EAAO2lB,G,SAClBiI,GAAShH,EAAY5mB,EAAOmlB,G,WAC1B,W,OAAMnlB,EAAMytB,I,IjCsMN9V,EAAOwN,EAAQQ,EAAI0B,EAAQqF,GkC/MtB,SAAC9F,EAAYzB,EAAQQ,EAAI0B,G,IAC5CrnB,EAAQ,C,QACH,CAAC,E,WACE,CAAC,G,EAGJ0uB,MAAM,iBAAmB,C,QACzBpE,GAAQtqB,EAAOmlB,EAAQQ,EAAI0B,EAAQT,G,UACjC6D,GAAUzqB,EAAO2lB,G,SAClB2C,GAAStoB,EAAOmlB,EAAQyB,G,WACtBsD,GAAalqB,EAAOmlB,EAAQQ,G,ElCsMvBhO,EAAOwN,EAAQQ,EAAI0B,G,EAEnCkZ,Q,WAEAnxB,SmCxNY,SAAA+V,G,OAAU,W,MACzB,yBACA5hB,OAAOwU,KAAKoN,EAAOY,YAAY95B,OAC/B,gBACAsX,OAAOwU,KAAKoN,EAAO/3B,KAAK,IAAInB,OAC5B,aACAk5B,EAAO/3B,KAAKnB,OACZ,O,EnCiNcmjB,CAAS+V,G,EACpBmR,OAASA,G,EAETU,oBAAsBA,GAElBrR,C,sCoC9NM,WAAS33B,EAAGC,GACzB,OAAOD,EAAIC,GAAK,EAAID,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAIC,GAC/C,CCAe,WAASmL,GA0BxB,IAA6B0E,EAxB3B,OADuB,IAAnB1E,EAAQpN,SAyBe8R,EAzB6B1E,EAA9BA,EA0BnB,SAAS5D,EAAGkC,GACjB,OAAO5J,EAAUgQ,EAAEtI,GAAIkC,EACzB,GA3BO,CACL2D,KAAM,SAAStN,EAAG2J,EAAG2mB,EAAI8D,GAGvB,IAFU,MAAN9D,IAAYA,EAAK,GACX,MAAN8D,IAAYA,EAAKp0B,EAAE/B,QAChBqyB,EAAK8D,GAAI,CACd,IAAIC,EAAM/D,EAAK8D,IAAO,EAClB/oB,EAAQrL,EAAEq0B,GAAM1qB,GAAK,EAAG2mB,EAAK+D,EAAM,EAClCD,EAAKC,CACZ,CACA,OAAO/D,CACT,EACAgE,MAAO,SAASt0B,EAAG2J,EAAG2mB,EAAI8D,GAGxB,IAFU,MAAN9D,IAAYA,EAAK,GACX,MAAN8D,IAAYA,EAAKp0B,EAAE/B,QAChBqyB,EAAK8D,GAAI,CACd,IAAIC,EAAM/D,EAAK8D,IAAO,EAClB/oB,EAAQrL,EAAEq0B,GAAM1qB,GAAK,EAAGyqB,EAAKC,EAC5B/D,EAAK+D,EAAM,CAClB,CACA,OAAO/D,CACT,EAEJ,C,mICvBA,IAAI6D,EAAkBI,EAASx0B,GACpByyC,EAAcre,EAAgBG,MAEzC,GADwBH,EAAgB7mB,KACxC,GCNImnB,EAAQz2B,MAAMU,UCAd+zC,GDEehe,EAAM9zB,MACR8zB,EAAMrqB,ICHblN,KAAK+V,KAAK,KAChBy/B,EAAKx1C,KAAK+V,KAAK,IACf0/B,EAAKz1C,KAAK+V,KAAK,GA+BZ,SAAS2/B,EAAc3gC,EAAOX,EAAMg5B,GACzC,IAAIuI,GAAQvhC,EAAOW,GAAS/U,KAAKC,IAAI,EAAGmtC,GACpCwI,EAAQ51C,KAAKomB,MAAMpmB,KAAK0qC,IAAIiL,GAAQ31C,KAAK61C,MACzCC,EAAQH,EAAO31C,KAAKslB,IAAI,GAAIswB,GAChC,OAAOA,GAAS,GACTE,GAASP,EAAM,GAAKO,GAASN,EAAK,EAAIM,GAASL,EAAK,EAAI,GAAKz1C,KAAKslB,IAAI,GAAIswB,IAC1E51C,KAAKslB,IAAI,IAAKswB,IAAUE,GAASP,EAAM,GAAKO,GAASN,EAAK,EAAIM,GAASL,EAAK,EAAI,EACzF,CAEO,SAASM,EAAShhC,EAAOX,EAAMg5B,GACpC,IAAI4I,EAAQh2C,KAAK4vB,IAAIxb,EAAOW,GAAS/U,KAAKC,IAAI,EAAGmtC,GAC7C6I,EAAQj2C,KAAKslB,IAAI,GAAItlB,KAAKomB,MAAMpmB,KAAK0qC,IAAIsL,GAASh2C,KAAK61C,OACvDC,EAAQE,EAAQC,EAIpB,OAHIH,GAASP,EAAKU,GAAS,GAClBH,GAASN,EAAIS,GAAS,EACtBH,GAASL,IAAIQ,GAAS,GACxB7hC,EAAOW,GAASkhC,EAAQA,CACjC,CClDO,SAASC,EAAU5d,EAAQE,GAChC,OAAQ7zB,UAAU5D,QAChB,KAAK,EAAG,MACR,KAAK,EAAGP,KAAKg4B,MAAMF,GAAS,MAC5B,QAAS93B,KAAKg4B,MAAMA,GAAOF,OAAOA,GAEpC,OAAO93B,IACT,CAEO,SAAS21C,EAAiB7d,EAAQ8d,GACvC,OAAQzxC,UAAU5D,QAChB,KAAK,EAAG,MACR,KAAK,EAAGP,KAAK41C,aAAa9d,GAAS,MACnC,QAAS93B,KAAK41C,aAAaA,GAAc9d,OAAOA,GAElD,OAAO93B,IACT,C,eChBI+2B,EAAQz2B,MAAMU,UAEP0L,EAAMqqB,EAAMrqB,IACZzJ,EAAQ8zB,EAAM9zB,MCCd4yC,EAAW,CAAC/yC,KAAM,YAEd,SAASgzC,IACtB,IAAIltC,GAAQ8D,EAAAA,EAAAA,MACRorB,EAAS,GACTE,EAAQ,GACR+d,EAAUF,EAEd,SAASr/B,EAAMzM,GACb,IAAI/H,EAAM+H,EAAI,GAAInI,EAAIgH,EAAMsI,IAAIlP,GAChC,IAAKJ,EAAG,CACN,GAAIm0C,IAAYF,EAAU,OAAOE,EACjCntC,EAAMwI,IAAIpP,EAAKJ,EAAIk2B,EAAO5vB,KAAK6B,GACjC,CACA,OAAOiuB,GAAOp2B,EAAI,GAAKo2B,EAAMz3B,OAC/B,CAwBA,OAtBAiW,EAAMshB,OAAS,SAAShnB,GACtB,IAAK3M,UAAU5D,OAAQ,OAAOu3B,EAAO70B,QACrC60B,EAAS,GAAIlvB,GAAQ8D,EAAAA,EAAAA,MAErB,IADA,IAA0B3C,EAAG/H,EAAzBJ,GAAK,EAAGoE,EAAI8K,EAAEvQ,SACTqB,EAAIoE,GAAQ4C,EAAM+jC,IAAI3qC,GAAO+H,EAAI+G,EAAElP,IAAM,KAAKgH,EAAMwI,IAAIpP,EAAK81B,EAAO5vB,KAAK6B,IAClF,OAAOyM,CACT,EAEAA,EAAMwhB,MAAQ,SAASlnB,GACrB,OAAO3M,UAAU5D,QAAUy3B,EAAQ/0B,EAAMb,KAAK0O,GAAI0F,GAASwhB,EAAM/0B,OACnE,EAEAuT,EAAMu/B,QAAU,SAASjlC,GACvB,OAAO3M,UAAU5D,QAAUw1C,EAAUjlC,EAAG0F,GAASu/B,CACnD,EAEAv/B,EAAMhF,KAAO,WACX,OAAOskC,EAAQhe,EAAQE,GAAO+d,QAAQA,EACxC,EAEAL,EAAUxxC,MAAMsS,EAAOrS,WAEhBqS,CACT,CCxCe,SAASw/B,IACtB,IAIIb,EACAje,EALA1gB,EAAQs/B,IAAUC,aAAQ/iC,GAC1B8kB,EAASthB,EAAMshB,OACfme,EAAez/B,EAAMwhB,MACrBA,EAAQ,CAAC,EAAG,GAGZvU,GAAQ,EACRyyB,EAAe,EACfC,EAAe,EACfC,EAAQ,GAIZ,SAASC,IACP,IAAIrwC,EAAI8xB,IAASv3B,OACbw6B,EAAU/C,EAAM,GAAKA,EAAM,GAC3BzjB,EAAQyjB,EAAM+C,EAAU,GACxBnnB,EAAOokB,EAAM,EAAI+C,GACrBoa,GAAQvhC,EAAOW,GAAS/U,KAAKC,IAAI,EAAGuG,EAAIkwC,EAA8B,EAAfC,GACnD1yB,IAAO0xB,EAAO31C,KAAKomB,MAAMuvB,IAC7B5gC,IAAUX,EAAOW,EAAQ4gC,GAAQnvC,EAAIkwC,IAAiBE,EACtDlf,EAAYie,GAAQ,EAAIe,GACpBzyB,IAAOlP,EAAQ/U,KAAKikB,MAAMlP,GAAQ2iB,EAAY13B,KAAKikB,MAAMyT,IAC7D,IAAIvR,EC5BO,SAASpR,EAAOX,EAAMuhC,GACnC5gC,GAASA,EAAOX,GAAQA,EAAMuhC,GAAQnvC,EAAI7B,UAAU5D,QAAU,GAAKqT,EAAOW,EAAOA,EAAQ,EAAG,GAAKvO,EAAI,EAAI,GAAKmvC,EAM9G,IAJA,IAAIvzC,GAAK,EACLoE,EAAoD,EAAhDxG,KAAKC,IAAI,EAAGD,KAAK82C,MAAM1iC,EAAOW,GAAS4gC,IAC3Cnd,EAAQ,IAAI13B,MAAM0F,KAEbpE,EAAIoE,GACXgyB,EAAMp2B,GAAK2S,EAAQ3S,EAAIuzC,EAGzB,OAAOnd,CACT,CDgBiBue,CAASvwC,GAAG0G,KAAI,SAAS9K,GAAK,OAAO2S,EAAQ4gC,EAAOvzC,CAAG,IACpE,OAAOq0C,EAAalb,EAAUpV,EAAOoV,UAAYpV,EACnD,CAkDA,cAhEOnP,EAAMu/B,QAgBbv/B,EAAMshB,OAAS,SAAShnB,GACtB,OAAO3M,UAAU5D,QAAUu3B,EAAOhnB,GAAIulC,KAAave,GACrD,EAEAthB,EAAMwhB,MAAQ,SAASlnB,GACrB,OAAO3M,UAAU5D,QAAUy3B,EAAQ,EAAElnB,EAAE,IAAKA,EAAE,IAAKulC,KAAare,EAAM/0B,OACxE,EAEAuT,EAAMggC,WAAa,SAAS1lC,GAC1B,OAAOknB,EAAQ,EAAElnB,EAAE,IAAKA,EAAE,IAAK2S,GAAQ,EAAM4yB,GAC/C,EAEA7/B,EAAM0gB,UAAY,WAChB,OAAOA,CACT,EAEA1gB,EAAM2+B,KAAO,WACX,OAAOA,CACT,EAEA3+B,EAAMiN,MAAQ,SAAS3S,GACrB,OAAO3M,UAAU5D,QAAUkjB,IAAU3S,EAAGulC,KAAa5yB,CACvD,EAEAjN,EAAMu4B,QAAU,SAASj+B,GACvB,OAAO3M,UAAU5D,QAAU21C,EAAe12C,KAAK8N,IAAI,EAAG6oC,GAAgBrlC,GAAIulC,KAAaH,CACzF,EAEA1/B,EAAM0/B,aAAe,SAASplC,GAC5B,OAAO3M,UAAU5D,QAAU21C,EAAe12C,KAAK8N,IAAI,EAAGwD,GAAIulC,KAAaH,CACzE,EAEA1/B,EAAM2/B,aAAe,SAASrlC,GAC5B,OAAO3M,UAAU5D,QAAU41C,GAAgBrlC,EAAGulC,KAAaF,CAC7D,EAEA3/B,EAAM4/B,MAAQ,SAAStlC,GACrB,OAAO3M,UAAU5D,QAAU61C,EAAQ52C,KAAKC,IAAI,EAAGD,KAAK8N,IAAI,EAAGwD,IAAKulC,KAAaD,CAC/E,EAEA5/B,EAAMhF,KAAO,WACX,OAAOwkC,EAAKle,IAAUE,GACjBvU,MAAMA,GACNyyB,aAAaA,GACbC,aAAaA,GACbC,MAAMA,EACb,EAEOV,EAAUxxC,MAAMmyC,IAAWlyC,UACpC,CAEA,SAASsyC,EAASjgC,GAChB,IAAIhF,EAAOgF,EAAMhF,KAUjB,OARAgF,EAAMu4B,QAAUv4B,EAAM2/B,oBACf3/B,EAAM0/B,oBACN1/B,EAAM2/B,aAEb3/B,EAAMhF,KAAO,WACX,OAAOilC,EAASjlC,IAClB,EAEOgF,CACT,CAEO,SAASrH,IACd,OAAOsnC,EAAST,EAAK9xC,MAAM,KAAMC,WAAW+xC,aAAa,GAC3D,C,0BEnGe,WAAS5zC,EAAGC,GACzB,OAAOD,GAAKA,EAAGC,GAAKA,EAAG,SAASoM,GAC9B,OAAOnP,KAAKikB,MAAMnhB,GAAK,EAAIqM,GAAKpM,EAAIoM,EACtC,CACF,CCJe,WAAS1C,GACtB,OAAQA,CACV,CCIA,IAAIyqC,EAAO,CAAC,EAAG,GAER,SAAS1hC,EAAS/I,GACvB,OAAOA,CACT,CAEA,SAAS0qC,EAAUr0C,EAAGC,GACpB,OAAQA,GAAMD,GAAKA,GACb,SAAS2J,GAAK,OAAQA,EAAI3J,GAAKC,CAAG,GCdlB0J,EDePuX,MAAMjhB,GAAKC,IAAM,GCdzB,WACL,OAAOyJ,CACT,GAHa,IAASA,CDgBxB,CAEA,SAAS2qC,EAAQ9e,GACf,IAAkDnpB,EAA9CrM,EAAIw1B,EAAO,GAAIv1B,EAAIu1B,EAAOA,EAAOv3B,OAAS,GAE9C,OADI+B,EAAIC,IAAGoM,EAAIrM,EAAGA,EAAIC,EAAGA,EAAIoM,GACtB,SAAS1C,GAAK,OAAOzM,KAAKC,IAAI6C,EAAG9C,KAAK8N,IAAI/K,EAAG0J,GAAK,CAC3D,CAIA,SAAS4qC,EAAM/e,EAAQE,EAAOvR,GAC5B,IAAIqwB,EAAKhf,EAAO,GAAI4H,EAAK5H,EAAO,GAAIxD,EAAK0D,EAAM,GAAIzF,EAAKyF,EAAM,GAG9D,OAFI0H,EAAKoX,GAAIA,EAAKH,EAAUjX,EAAIoX,GAAKxiB,EAAK7N,EAAY8L,EAAI+B,KACrDwiB,EAAKH,EAAUG,EAAIpX,GAAKpL,EAAK7N,EAAY6N,EAAI/B,IAC3C,SAAStmB,GAAK,OAAOqoB,EAAGwiB,EAAG7qC,GAAK,CACzC,CAEA,SAAS8qC,EAAQjf,EAAQE,EAAOvR,GAC9B,IAAIjd,EAAIhK,KAAK8N,IAAIwqB,EAAOv3B,OAAQy3B,EAAMz3B,QAAU,EAC5CwJ,EAAI,IAAIzJ,MAAMkJ,GACdwZ,EAAI,IAAI1iB,MAAMkJ,GACd5H,GAAK,EAQT,IALIk2B,EAAOtuB,GAAKsuB,EAAO,KACrBA,EAASA,EAAO70B,QAAQ83B,UACxB/C,EAAQA,EAAM/0B,QAAQ83B,aAGfn5B,EAAI4H,GACXO,EAAEnI,GAAK+0C,EAAU7e,EAAOl2B,GAAIk2B,EAAOl2B,EAAI,IACvCohB,EAAEphB,GAAK6kB,EAAYuR,EAAMp2B,GAAIo2B,EAAMp2B,EAAI,IAGzC,OAAO,SAASqK,GACd,IAAIrK,EAAIo1C,EAAOlf,EAAQ7rB,EAAG,EAAGzC,GAAK,EAClC,OAAOwZ,EAAEphB,GAAGmI,EAAEnI,GAAGqK,GACnB,CACF,CAEO,SAASuF,EAAKzC,EAAQ2a,GAC3B,OAAOA,EACFoO,OAAO/oB,EAAO+oB,UACdE,MAAMjpB,EAAOipB,SACbvR,YAAY1X,EAAO0X,eACnB1B,MAAMhW,EAAOgW,SACbgxB,QAAQhnC,EAAOgnC,UACtB,CA0De,SAASkB,EAAWlgC,EAAWmgC,GAC5C,OAzDK,WACL,IAGIngC,EACAmgC,EACAnB,EAEAoB,EACA/sB,EACAD,EATA2N,EAAS4e,EACT1e,EAAQ0e,EACRjwB,EAAc2wB,EAAAA,EAIdryB,EAAQ/P,EAKZ,SAASqhC,IAGP,OAFAc,EAAY33C,KAAK8N,IAAIwqB,EAAOv3B,OAAQy3B,EAAMz3B,QAAU,EAAIw2C,EAAUF,EAClEzsB,EAASD,EAAQ,KACV3T,CACT,CAEA,SAASA,EAAMvK,GACb,OAAOuX,MAAMvX,GAAKA,GAAK8pC,GAAW3rB,IAAWA,EAAS+sB,EAAUrf,EAAOprB,IAAIqK,GAAYihB,EAAOvR,KAAe1P,EAAUgO,EAAM9Y,IAC/H,CA8BA,OA5BAuK,EAAMojB,OAAS,SAASvqB,GACtB,OAAO0V,EAAMmyB,GAAa/sB,IAAUA,EAAQgtB,EAAUnf,EAAOF,EAAOprB,IAAIqK,GAAYuP,EAAAA,KAAqBjX,IAC3G,EAEAmH,EAAMshB,OAAS,SAAShnB,GACtB,OAAO3M,UAAU5D,QAAUu3B,EAASprB,EAAItK,KAAK0O,EAAGwF,GAASyO,IAAU/P,IAAa+P,EAAQ6xB,EAAQ9e,IAAUue,KAAave,EAAO70B,OAChI,EAEAuT,EAAMwhB,MAAQ,SAASlnB,GACrB,OAAO3M,UAAU5D,QAAUy3B,EAAQ/0B,EAAMb,KAAK0O,GAAIulC,KAAare,EAAM/0B,OACvE,EAEAuT,EAAMggC,WAAa,SAAS1lC,GAC1B,OAAOknB,EAAQ/0B,EAAMb,KAAK0O,GAAI2V,EAAc4wB,EAAkBhB,GAChE,EAEA7/B,EAAMuO,MAAQ,SAASjU,GACrB,OAAO3M,UAAU5D,QAAUwkB,EAAQjU,EAAI8lC,EAAQ9e,GAAU9iB,EAAUwB,GAASuO,IAAU/P,CACxF,EAEAwB,EAAMiQ,YAAc,SAAS3V,GAC3B,OAAO3M,UAAU5D,QAAUkmB,EAAc3V,EAAGulC,KAAa5vB,CAC3D,EAEAjQ,EAAMu/B,QAAU,SAASjlC,GACvB,OAAO3M,UAAU5D,QAAUw1C,EAAUjlC,EAAG0F,GAASu/B,CACnD,EAEO,SAASpnC,EAAG2oC,GAEjB,OADAvgC,EAAYpI,EAAGuoC,EAAcI,EACtBjB,GACT,CACF,CAGSkB,GAAcxgC,EAAWmgC,EAClC,C,yCExHe,WAAS3iC,EAAOX,EAAMg5B,EAAO4K,GAC1C,IACIC,EADAtC,EAAOI,EAAShhC,EAAOX,EAAMg5B,GAGjC,QADA4K,GAAYE,EAAAA,EAAAA,GAA6B,MAAbF,EAAoB,KAAOA,IACrC9tC,MAChB,IAAK,IACH,IAAI9F,EAAQpE,KAAKC,IAAID,KAAK4vB,IAAI7a,GAAQ/U,KAAK4vB,IAAIxb,IAE/C,OAD2B,MAAvB4jC,EAAUC,WAAsBj0B,MAAMi0B,ECRjC,SAAStC,EAAMvxC,GAC5B,OAAOpE,KAAKC,IAAI,EAAgE,EAA7DD,KAAKC,KAAK,EAAGD,KAAK8N,IAAI,EAAG9N,KAAKomB,OAAM+xB,EAAAA,EAAAA,GAAS/zC,GAAS,MAAW+zC,EAAAA,EAAAA,GAASn4C,KAAK4vB,IAAI+lB,IACxG,CDM4DyC,CAAgBzC,EAAMvxC,MAAS4zC,EAAUC,UAAYA,IACpGI,EAAAA,EAAAA,GAAaL,EAAW5zC,GAEjC,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACwB,MAAvB4zC,EAAUC,WAAsBj0B,MAAMi0B,EEhBjC,SAAStC,EAAM11C,GAE5B,OADA01C,EAAO31C,KAAK4vB,IAAI+lB,GAAO11C,EAAMD,KAAK4vB,IAAI3vB,GAAO01C,EACtC31C,KAAKC,IAAI,GAAGk4C,EAAAA,EAAAA,GAASl4C,IAAOk4C,EAAAA,EAAAA,GAASxC,IAAS,CACvD,CFa4D2C,CAAe3C,EAAM31C,KAAKC,IAAID,KAAK4vB,IAAI7a,GAAQ/U,KAAK4vB,IAAIxb,QAAU4jC,EAAUC,UAAYA,GAAgC,MAAnBD,EAAU9tC,OACrK,MAEF,IAAK,IACL,IAAK,IACwB,MAAvB8tC,EAAUC,WAAsBj0B,MAAMi0B,EGrBjC,SAAStC,GACtB,OAAO31C,KAAKC,IAAI,IAAIk4C,EAAAA,EAAAA,GAASn4C,KAAK4vB,IAAI+lB,IACxC,CHmB4D4C,CAAe5C,MAAQqC,EAAUC,UAAYA,EAAuC,GAAP,MAAnBD,EAAU9tC,OAI9H,OAAO6Y,EAAAA,EAAAA,IAAOi1B,EAChB,CIvBO,SAASQ,EAAUxhC,GACxB,IAAIshB,EAASthB,EAAMshB,OAoDnB,OAlDAthB,EAAMqhB,MAAQ,SAAS+U,GACrB,IAAI7iC,EAAI+tB,IACR,OdNW,SAASvjB,EAAOX,EAAMg5B,GACnC,IAAI7R,EAEA/0B,EACA6xB,EACAsd,EAHAvzC,GAAK,EAMT,GAD8BgrC,GAASA,GAAzBr4B,GAASA,MAAvBX,GAAQA,IACcg5B,EAAQ,EAAG,MAAO,CAACr4B,GAEzC,IADIwmB,EAAUnnB,EAAOW,KAAOvO,EAAIuO,EAAOA,EAAQX,EAAMA,EAAO5N,GACT,KAA9CmvC,EAAOD,EAAc3gC,EAAOX,EAAMg5B,MAAkBrU,SAAS4c,GAAO,MAAO,GAEhF,GAAIA,EAAO,EAIT,IAHA5gC,EAAQ/U,KAAK82C,KAAK/hC,EAAQ4gC,GAC1BvhC,EAAOpU,KAAKomB,MAAMhS,EAAOuhC,GACzBtd,EAAQ,IAAIv3B,MAAM0F,EAAIxG,KAAK82C,KAAK1iC,EAAOW,EAAQ,MACtC3S,EAAIoE,GAAG6xB,EAAMj2B,IAAM2S,EAAQ3S,GAAKuzC,OAKzC,IAHA5gC,EAAQ/U,KAAKomB,MAAMrR,EAAQ4gC,GAC3BvhC,EAAOpU,KAAK82C,KAAK1iC,EAAOuhC,GACxBtd,EAAQ,IAAIv3B,MAAM0F,EAAIxG,KAAK82C,KAAK/hC,EAAQX,EAAO,MACtChS,EAAIoE,GAAG6xB,EAAMj2B,IAAM2S,EAAQ3S,GAAKuzC,EAK3C,OAFIpa,GAASlD,EAAMkD,UAEZlD,CACT,CcrBWA,CAAM9tB,EAAE,GAAIA,EAAEA,EAAExJ,OAAS,GAAa,MAATqsC,EAAgB,GAAKA,EAC3D,EAEAp2B,EAAMihB,WAAa,SAASmV,EAAO4K,GACjC,IAAIztC,EAAI+tB,IACR,OAAOL,EAAW1tB,EAAE,GAAIA,EAAEA,EAAExJ,OAAS,GAAa,MAATqsC,EAAgB,GAAKA,EAAO4K,EACvE,EAEAhhC,EAAMyhC,KAAO,SAASrL,GACP,MAATA,IAAeA,EAAQ,IAE3B,IAKIuI,EALAprC,EAAI+tB,IACJxrB,EAAK,EACLC,EAAKxC,EAAExJ,OAAS,EAChBgU,EAAQxK,EAAEuC,GACVsH,EAAO7J,EAAEwC,GA8Bb,OA3BIqH,EAAOW,IACT4gC,EAAO5gC,EAAOA,EAAQX,EAAMA,EAAOuhC,EACnCA,EAAO7oC,EAAIA,EAAKC,EAAIA,EAAK4oC,IAG3BA,EAAOD,EAAc3gC,EAAOX,EAAMg5B,IAEvB,EAGTuI,EAAOD,EAFP3gC,EAAQ/U,KAAKomB,MAAMrR,EAAQ4gC,GAAQA,EACnCvhC,EAAOpU,KAAK82C,KAAK1iC,EAAOuhC,GAAQA,EACEvI,GACzBuI,EAAO,IAGhBA,EAAOD,EAFP3gC,EAAQ/U,KAAK82C,KAAK/hC,EAAQ4gC,GAAQA,EAClCvhC,EAAOpU,KAAKomB,MAAMhS,EAAOuhC,GAAQA,EACCvI,IAGhCuI,EAAO,GACTprC,EAAEuC,GAAM9M,KAAKomB,MAAMrR,EAAQ4gC,GAAQA,EACnCprC,EAAEwC,GAAM/M,KAAK82C,KAAK1iC,EAAOuhC,GAAQA,EACjCrd,EAAO/tB,IACEorC,EAAO,IAChBprC,EAAEuC,GAAM9M,KAAK82C,KAAK/hC,EAAQ4gC,GAAQA,EAClCprC,EAAEwC,GAAM/M,KAAKomB,MAAMhS,EAAOuhC,GAAQA,EAClCrd,EAAO/tB,IAGFyM,CACT,EAEOA,CACT,CAEe,SAASwO,IACtB,IAAIxO,EAAQygC,EAAWjiC,EAAUA,GAQjC,OANAwB,EAAMhF,KAAO,WACX,OAAOA,EAAKgF,EAAOwO,IACrB,EAEA0wB,EAAUxxC,MAAMsS,EAAOrS,WAEhB6zC,EAAUxhC,EACnB,C,uGC/DA,IAAI0hC,EAAiB,IACjBC,EAAkC,GAAjBD,EACjBE,GAAgC,GAAjBD,EACfE,GAA6B,GAAfD,GACdE,GAA6B,EAAdD,GACfE,GAA8B,GAAdF,GAChBG,GAA6B,IAAdH,GAEnB,SAASI,GAAK9pC,GACZ,OAAO,IAAItP,KAAKsP,EAClB,CAEA,SAAS2H,GAAO3H,GACd,OAAOA,aAAatP,MAAQsP,GAAK,IAAItP,MAAMsP,EAC7C,CAEO,SAAS+pC,GAASC,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQ5X,EAAQ6X,EAAa12B,GAClF,IAAI/L,EAAQygC,EAAWjiC,EAAUA,GAC7B4kB,EAASpjB,EAAMojB,OACf9B,EAASthB,EAAMshB,OAEfohB,EAAoB32B,EAAO,OAC3B42B,EAAe52B,EAAO,OACtB62B,EAAe72B,EAAO,SACtB82B,EAAa92B,EAAO,SACpB+2B,EAAY/2B,EAAO,SACnBg3B,EAAah3B,EAAO,SACpBi3B,EAAcj3B,EAAO,MACrBk3B,EAAal3B,EAAO,MAEpBm3B,EAAgB,CAClB,CAACtY,EAAS,EAAQ8W,GAClB,CAAC9W,EAAS,EAAI,EAAI8W,GAClB,CAAC9W,EAAQ,GAAI,GAAK8W,GAClB,CAAC9W,EAAQ,GAAI,GAAK8W,GAClB,CAACc,EAAS,EAAQb,GAClB,CAACa,EAAS,EAAI,EAAIb,GAClB,CAACa,EAAQ,GAAI,GAAKb,GAClB,CAACa,EAAQ,GAAI,GAAKb,GAClB,CAAGY,EAAO,EAAQX,IAClB,CAAGW,EAAO,EAAI,EAAIX,IAClB,CAAGW,EAAO,EAAI,EAAIX,IAClB,CAAGW,EAAM,GAAI,GAAKX,IAClB,CAAIU,EAAM,EAAQT,IAClB,CAAIS,EAAM,EAAI,EAAIT,IAClB,CAAGQ,EAAO,EAAQP,IAClB,CAAEM,EAAQ,EAAQL,IAClB,CAAEK,EAAQ,EAAI,EAAIL,IAClB,CAAGI,EAAO,EAAQH,KAGpB,SAAS/gB,EAAWghB,GAClB,OAAQrX,EAAOqX,GAAQA,EAAOS,EACxBF,EAAOP,GAAQA,EAAOU,EACtBJ,EAAKN,GAAQA,EAAOW,EACpBN,EAAIL,GAAQA,EAAOY,EACnBT,EAAMH,GAAQA,EAAQI,EAAKJ,GAAQA,EAAOa,EAAYC,EACtDZ,EAAKF,GAAQA,EAAOe,EACpBC,GAAYhB,EACpB,CAEA,SAASkB,EAAa9nC,EAAU0C,EAAOX,EAAMuhC,GAM3C,GALgB,MAAZtjC,IAAkBA,EAAW,IAKT,kBAAbA,EAAuB,CAChC,IAAI6X,EAASlqB,KAAK4vB,IAAIxb,EAAOW,GAAS1C,EAClCjQ,EAAIi1B,GAAS,SAASj1B,GAAK,OAAOA,EAAE,EAAI,IAAGg1B,MAAM8iB,EAAehwB,GAChE9nB,IAAM83C,EAAcn5C,QACtB40C,EAAOI,EAAShhC,EAAQikC,GAAc5kC,EAAO4kC,GAAc3mC,GAC3DA,EAAW8mC,GACF/2C,GAETuzC,GADAvzC,EAAI83C,EAAchwB,EAASgwB,EAAc93C,EAAI,GAAG,GAAK83C,EAAc93C,GAAG,GAAK8nB,EAAS9nB,EAAI,EAAIA,IACnF,GACTiQ,EAAWjQ,EAAE,KAEbuzC,EAAO31C,KAAKC,IAAI81C,EAAShhC,EAAOX,EAAM/B,GAAW,GACjDA,EAAWonC,EAEf,CAEA,OAAe,MAAR9D,EAAetjC,EAAWA,EAASyW,MAAM6sB,EAClD,CAqCA,OAnCA3+B,EAAMojB,OAAS,SAASvqB,GACtB,OAAO,IAAIhQ,KAAKu6B,EAAOvqB,GACzB,EAEAmH,EAAMshB,OAAS,SAAShnB,GACtB,OAAO3M,UAAU5D,OAASu3B,EAAOprB,EAAItK,KAAK0O,EAAGwF,KAAWwhB,IAASprB,IAAI+rC,GACvE,EAEAjiC,EAAMqhB,MAAQ,SAAShmB,EAAUsjC,GAC/B,IAIIxmC,EAJA5E,EAAI+tB,IACJ5kB,EAAKnJ,EAAE,GACPqJ,EAAKrJ,EAAEA,EAAExJ,OAAS,GAClByiB,EAAI5P,EAAKF,EAKb,OAHI8P,IAAGrU,EAAIuE,EAAIA,EAAKE,EAAIA,EAAKzE,GAE7BA,GADAA,EAAIgrC,EAAa9nC,EAAUqB,EAAIE,EAAI+hC,IAC3BxmC,EAAEqpB,MAAM9kB,EAAIE,EAAK,GAAK,GACvB4P,EAAIrU,EAAEosB,UAAYpsB,CAC3B,EAEA6H,EAAMihB,WAAa,SAASmV,EAAO4K,GACjC,OAAoB,MAAbA,EAAoB/f,EAAalV,EAAOi1B,EACjD,EAEAhhC,EAAMyhC,KAAO,SAASpmC,EAAUsjC,GAC9B,IAAIprC,EAAI+tB,IACR,OAAQjmB,EAAW8nC,EAAa9nC,EAAU9H,EAAE,GAAIA,EAAEA,EAAExJ,OAAS,GAAI40C,IAC3Drd,ECzHK,SAASA,EAAQjmB,GAG9B,IAIIlD,EAJArC,EAAK,EACLC,GAHJurB,EAASA,EAAO70B,SAGA1C,OAAS,EACrB4xB,EAAK2F,EAAOxrB,GACZ+lB,EAAKyF,EAAOvrB,GAUhB,OAPI8lB,EAAKF,IACPxjB,EAAIrC,EAAIA,EAAKC,EAAIA,EAAKoC,EACtBA,EAAIwjB,EAAIA,EAAKE,EAAIA,EAAK1jB,GAGxBmpB,EAAOxrB,GAAMuF,EAAS+T,MAAMuM,GAC5B2F,EAAOvrB,GAAMsF,EAASykC,KAAKjkB,GACpByF,CACT,CDwGiBmgB,CAAKluC,EAAG8H,IACf2E,CACR,EAEAA,EAAMhF,KAAO,WACX,OAAOA,EAAKgF,EAAOkiC,GAASC,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQ5X,EAAQ6X,EAAa12B,GACzF,EAEO/L,CACT,CAEe,cACb,OAAOk/B,EAAUxxC,MAAMw0C,GAASkB,EAAAA,EAAUC,EAAAA,EAAWC,EAAAA,GAAUC,EAAAA,EAASC,EAAAA,EAAUC,EAAAA,EAAYC,EAAAA,EAAYC,EAAAA,EAAiBC,EAAAA,IAAYtiB,OAAO,CAAC,IAAIz4B,KAAK,IAAM,EAAG,GAAI,IAAIA,KAAK,IAAM,EAAG,KAAM8E,UAC/L,CE/HA,SAASozC,KACP,IAEIrkC,EACAE,EACAinC,EACAtjC,EAGAg/B,EARA5jB,EAAK,EACLE,EAAK,EAKLujB,EAAe5gC,EACf+P,GAAQ,EAGZ,SAASvO,EAAMvK,GACb,OAAOuX,MAAMvX,GAAKA,GAAK8pC,EAAUH,EAAqB,IAARyE,EAAY,IAAOpuC,GAAK8K,EAAU9K,GAAKiH,GAAMmnC,EAAKt1B,EAAQvlB,KAAKC,IAAI,EAAGD,KAAK8N,IAAI,EAAGrB,IAAMA,GACxI,CAkBA,OAhBAuK,EAAMshB,OAAS,SAAShnB,GACtB,OAAO3M,UAAU5D,QAAU2S,EAAK6D,EAAUob,GAAMrhB,EAAE,IAAKsC,EAAK2D,EAAUsb,GAAMvhB,EAAE,IAAKupC,EAAMnnC,IAAOE,EAAK,EAAI,GAAKA,EAAKF,GAAKsD,GAAS,CAAC2b,EAAIE,EACxI,EAEA7b,EAAMuO,MAAQ,SAASjU,GACrB,OAAO3M,UAAU5D,QAAUwkB,IAAUjU,EAAG0F,GAASuO,CACnD,EAEAvO,EAAMo/B,aAAe,SAAS9kC,GAC5B,OAAO3M,UAAU5D,QAAUq1C,EAAe9kC,EAAG0F,GAASo/B,CACxD,EAEAp/B,EAAMu/B,QAAU,SAASjlC,GACvB,OAAO3M,UAAU5D,QAAUw1C,EAAUjlC,EAAG0F,GAASu/B,CACnD,EAEO,SAASpnC,GAEd,OADAoI,EAAYpI,EAAGuE,EAAKvE,EAAEwjB,GAAK/e,EAAKzE,EAAE0jB,GAAKgoB,EAAMnnC,IAAOE,EAAK,EAAI,GAAKA,EAAKF,GAChEsD,CACT,CACF,CAEO,SAAShF,GAAKzC,EAAQ2a,GAC3B,OAAOA,EACFoO,OAAO/oB,EAAO+oB,UACd8d,aAAa7mC,EAAO6mC,gBACpB7wB,MAAMhW,EAAOgW,SACbgxB,QAAQhnC,EAAOgnC,UACtB,CAEe,SAASuE,KACtB,IAAI9jC,EAAQwhC,EAAUT,KAAcviC,IAMpC,OAJAwB,EAAMhF,KAAO,WACX,OAAOA,GAAKgF,EAAO8jC,KACrB,EAEO3E,EAAiBzxC,MAAMsS,EAAOrS,UACvC,C", "sources": ["../node_modules/requestanimationframe/app/requestAnimationFrame.js", "../node_modules/d3-selection/src/selector.js", "../node_modules/d3-selection/src/selectorAll.js", "../node_modules/d3-selection/src/matcher.js", "../node_modules/d3-selection/src/selection/sparse.js", "../node_modules/d3-selection/src/selection/enter.js", "../node_modules/d3-selection/src/selection/data.js", "../node_modules/d3-selection/src/selection/sort.js", "../node_modules/d3-selection/src/namespaces.js", "../node_modules/d3-selection/src/namespace.js", "../node_modules/d3-selection/src/selection/attr.js", "../node_modules/d3-selection/src/window.js", "../node_modules/d3-selection/src/selection/style.js", "../node_modules/d3-selection/src/selection/property.js", "../node_modules/d3-selection/src/selection/classed.js", "../node_modules/d3-selection/src/selection/text.js", "../node_modules/d3-selection/src/selection/html.js", "../node_modules/d3-selection/src/selection/raise.js", "../node_modules/d3-selection/src/selection/lower.js", "../node_modules/d3-selection/src/creator.js", "../node_modules/d3-selection/src/selection/insert.js", "../node_modules/d3-selection/src/selection/remove.js", "../node_modules/d3-selection/src/selection/clone.js", "../node_modules/d3-selection/src/selection/on.js", "../node_modules/d3-selection/src/selection/dispatch.js", "../node_modules/d3-selection/src/selection/index.js", "../node_modules/d3-selection/src/selection/select.js", "../node_modules/d3-selection/src/selection/selectAll.js", "../node_modules/d3-selection/src/selection/filter.js", "../node_modules/d3-selection/src/constant.js", "../node_modules/d3-selection/src/selection/exit.js", "../node_modules/d3-selection/src/selection/join.js", "../node_modules/d3-selection/src/selection/merge.js", "../node_modules/d3-selection/src/selection/order.js", "../node_modules/d3-selection/src/selection/call.js", "../node_modules/d3-selection/src/selection/nodes.js", "../node_modules/d3-selection/src/selection/node.js", "../node_modules/d3-selection/src/selection/size.js", "../node_modules/d3-selection/src/selection/empty.js", "../node_modules/d3-selection/src/selection/each.js", "../node_modules/d3-selection/src/selection/append.js", "../node_modules/d3-selection/src/selection/datum.js", "../node_modules/d3-selection/src/select.js", "../node_modules/d3-selection/src/sourceEvent.js", "../node_modules/d3-selection/src/point.js", "../node_modules/d3-selection/src/mouse.js", "../node_modules/d3-selection/src/selectAll.js", "../node_modules/d3-drag/src/noevent.js", "../node_modules/d3-drag/src/nodrag.js", "../node_modules/d3-selection/src/touch.js", "../node_modules/d3-transition/node_modules/d3-dispatch/src/dispatch.js", "../node_modules/d3-timer/src/timer.js", "../node_modules/d3-timer/src/timeout.js", "../node_modules/d3-transition/src/transition/schedule.js", "../node_modules/d3-transition/src/interrupt.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/number.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/transform/decompose.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/transform/parse.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/transform/index.js", "../node_modules/d3-transition/src/transition/tween.js", "../node_modules/d3-color/src/define.js", "../node_modules/d3-color/src/color.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/basis.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/constant.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/color.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/rgb.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/basisClosed.js", "../node_modules/d3-transition/node_modules/d3-interpolate/src/string.js", "../node_modules/d3-transition/src/transition/interpolate.js", "../node_modules/d3-transition/src/transition/attr.js", "../node_modules/d3-transition/src/transition/attrTween.js", "../node_modules/d3-transition/src/transition/delay.js", "../node_modules/d3-transition/src/transition/duration.js", "../node_modules/d3-transition/src/transition/selection.js", "../node_modules/d3-transition/src/transition/style.js", "../node_modules/d3-transition/src/transition/index.js", "../node_modules/d3-transition/src/transition/select.js", "../node_modules/d3-transition/src/transition/selectAll.js", "../node_modules/d3-transition/src/transition/filter.js", "../node_modules/d3-transition/src/transition/merge.js", "../node_modules/d3-transition/src/transition/transition.js", "../node_modules/d3-transition/src/transition/on.js", "../node_modules/d3-transition/src/transition/styleTween.js", "../node_modules/d3-transition/src/transition/text.js", "../node_modules/d3-transition/src/transition/textTween.js", "../node_modules/d3-transition/src/transition/remove.js", "../node_modules/d3-transition/src/transition/ease.js", "../node_modules/d3-transition/src/transition/easeVarying.js", "../node_modules/d3-transition/src/transition/end.js", "../node_modules/d3-transition/src/selection/transition.js", "../node_modules/d3-ease/src/cubic.js", "../node_modules/d3-brush/src/constant.js", "../node_modules/d3-brush/src/event.js", "../node_modules/d3-brush/src/noevent.js", "../node_modules/d3-transition/src/selection/index.js", "../node_modules/d3-transition/src/selection/interrupt.js", "../node_modules/d3-brush/src/brush.js", "../node_modules/d3-drag/src/constant.js", "../node_modules/d3-drag/src/event.js", "../node_modules/d3-drag/src/drag.js", "../node_modules/d3-shape/src/math.js", "../node_modules/d3-shape/src/arc.js", "../node_modules/parcoord-es/node_modules/d3-array/src/ascending.js", "../node_modules/parcoord-es/node_modules/d3-array/src/bisect.js", "../node_modules/parcoord-es/node_modules/d3-array/src/bisector.js", "../node_modules/parcoord-es/node_modules/d3-array/src/extent.js", "../node_modules/parcoord-es/node_modules/d3-array/src/array.js", "../node_modules/parcoord-es/node_modules/d3-array/src/ticks.js", "../node_modules/d3-axis/src/array.js", "../node_modules/d3-axis/src/identity.js", "../node_modules/d3-axis/src/axis.js", "../node_modules/parcoord-es/src/util/renderQueue.js", "../node_modules/parcoord-es/src/util/width.js", "../node_modules/parcoord-es/src/brush/invertByScale.js", "../node_modules/parcoord-es/src/brush/1d/brushExtents.js", "../node_modules/parcoord-es/src/brush/1d/selected.js", "../node_modules/parcoord-es/src/brush/1d/brushFor.js", "../node_modules/parcoord-es/src/brush/1d/install.js", "../node_modules/parcoord-es/src/brush/1d/brushReset.js", "../node_modules/parcoord-es/src/brush/1d/uninstall.js", "../node_modules/parcoord-es/src/brush/1d-multi/drawBrushes.js", "../node_modules/parcoord-es/src/brush/1d-multi/selected.js", "../node_modules/parcoord-es/src/brush/1d-multi/newBrush.js", "../node_modules/parcoord-es/src/brush/1d-multi/brushExtents.js", "../node_modules/parcoord-es/src/brush/1d-multi/install.js", "../node_modules/parcoord-es/src/brush/1d-multi/brushFor.js", "../node_modules/parcoord-es/src/brush/1d-multi/brushReset.js", "../node_modules/parcoord-es/src/brush/1d-multi/uninstall.js", "../node_modules/parcoord-es/src/brush/strums/uninstall.js", "../node_modules/parcoord-es/src/brush/strums/selected.js", "../node_modules/parcoord-es/src/brush/strums/removeStrum.js", "../node_modules/parcoord-es/src/brush/strums/onDragEnd.js", "../node_modules/parcoord-es/src/brush/strums/onDrag.js", "../node_modules/parcoord-es/src/util/height.js", "../node_modules/parcoord-es/src/brush/dimensionsForPoint.js", "../node_modules/parcoord-es/src/brush/consecutive.js", "../node_modules/parcoord-es/src/brush/strums/install.js", "../node_modules/parcoord-es/src/brush/strums/brushReset.js", "../node_modules/parcoord-es/src/brush/strums/onDragStart.js", "../node_modules/parcoord-es/src/brush/angular/uninstall.js", "../node_modules/parcoord-es/src/brush/angular/util/hypothenuse.js", "../node_modules/parcoord-es/src/brush/angular/selected.js", "../node_modules/parcoord-es/src/brush/angular/removeStrum.js", "../node_modules/parcoord-es/src/brush/angular/onDragEnd.js", "../node_modules/parcoord-es/src/brush/angular/onDrag.js", "../node_modules/parcoord-es/src/brush/angular/install.js", "../node_modules/parcoord-es/src/brush/angular/brushReset.js", "../node_modules/parcoord-es/src/brush/angular/onDragStart.js", "../node_modules/parcoord-es/src/api/intersection.js", "../node_modules/parcoord-es/src/api/brushMode.js", "../node_modules/parcoord-es/src/util/dimensionLabels.js", "../node_modules/parcoord-es/src/util/flipAxisAndUpdatePCP.js", "../node_modules/parcoord-es/src/util/rotateLabels.js", "../node_modules/parcoord-es/src/util/getRange.js", "../node_modules/parcoord-es/src/api/applyDimensionDefaults.js", "../node_modules/parcoord-es/src/api/applyAxisConfig.js", "../node_modules/parcoord-es/src/util/isBrushed.js", "../node_modules/parcoord-es/node_modules/sylvester-es6/src/PRECISION.js", "../node_modules/parcoord-es/node_modules/sylvester-es6/src/Matrix.js", "../node_modules/parcoord-es/node_modules/sylvester-es6/src/Vector.js", "../node_modules/parcoord-es/src/util/computeCentroids.js", "../node_modules/parcoord-es/src/util/colorPath.js", "../node_modules/parcoord-es/src/util/computeControlPoints.js", "../node_modules/parcoord-es/src/util/functor.js", "../node_modules/parcoord-es/src/api/renderMarked.js", "../node_modules/parcoord-es/src/api/renderBrushed.js", "../node_modules/parcoord-es/src/api/toType.js", "../node_modules/parcoord-es/src/api/adjacentPairs.js", "../node_modules/parcoord-es/src/api/highlight.js", "../node_modules/parcoord-es/src/api/renderDefault.js", "../node_modules/parcoord-es/src/api/toTypeCoerceNumbers.js", "../node_modules/parcoord-es/src/api/detectDimensionTypes.js", "../node_modules/parcoord-es/src/state/defaultConfig.js", "../node_modules/parcoord-es/src/state/sideEffects.js", "../node_modules/parcoord-es/src/util/computeClusterCentroids.js", "../node_modules/parcoord-es/src/bindEvents.js", "../node_modules/parcoord-es/src/util/getset.js", "../node_modules/parcoord-es/src/index.js", "../node_modules/parcoord-es/src/state/index.js", "../node_modules/parcoord-es/src/api/init.js", "../node_modules/parcoord-es/src/api/autoscale.js", "../node_modules/parcoord-es/src/api/scale.js", "../node_modules/parcoord-es/src/api/flip.js", "../node_modules/parcoord-es/src/api/commonScale.js", "../node_modules/parcoord-es/src/api/detectDimensions.js", "../node_modules/parcoord-es/src/api/getOrderedDimensionKeys.js", "../node_modules/parcoord-es/src/api/render.js", "../node_modules/parcoord-es/src/api/computeRealCentroids.js", "../node_modules/parcoord-es/src/api/shadows.js", "../node_modules/parcoord-es/src/api/axisDots.js", "../node_modules/parcoord-es/node_modules/d3-array/src/min.js", "../node_modules/parcoord-es/src/api/clear.js", "../node_modules/parcoord-es/src/api/createAxes.js", "../node_modules/parcoord-es/src/api/removeAxes.js", "../node_modules/parcoord-es/src/api/updateAxes.js", "../node_modules/parcoord-es/src/api/brushable.js", "../node_modules/parcoord-es/src/api/brushReset.js", "../node_modules/parcoord-es/src/api/selected.js", "../node_modules/parcoord-es/src/api/reorderable.js", "../node_modules/parcoord-es/src/api/reorder.js", "../node_modules/parcoord-es/src/api/sortDimensionsByRowData.js", "../node_modules/parcoord-es/src/api/sortDimensions.js", "../node_modules/parcoord-es/src/api/interactive.js", "../node_modules/parcoord-es/src/api/resize.js", "../node_modules/parcoord-es/src/api/unhighlight.js", "../node_modules/parcoord-es/src/api/mark.js", "../node_modules/parcoord-es/src/api/unmark.js", "../node_modules/parcoord-es/src/api/mergeParcoords.js", "../node_modules/parcoord-es/src/brush/1d/index.js", "../node_modules/parcoord-es/src/brush/strums/index.js", "../node_modules/parcoord-es/src/brush/angular/index.js", "../node_modules/parcoord-es/src/brush/1d-multi/index.js", "../node_modules/parcoord-es/src/api/toString.js", "../node_modules/d3-scale/node_modules/d3-array/src/ascending.js", "../node_modules/d3-scale/node_modules/d3-array/src/bisector.js", "../node_modules/d3-scale/node_modules/d3-array/src/bisect.js", "../node_modules/d3-scale/node_modules/d3-array/src/array.js", "../node_modules/d3-scale/node_modules/d3-array/src/ticks.js", "../node_modules/d3-scale/src/init.js", "../node_modules/d3-scale/src/array.js", "../node_modules/d3-scale/src/ordinal.js", "../node_modules/d3-scale/src/band.js", "../node_modules/d3-scale/node_modules/d3-array/src/range.js", "../node_modules/d3-interpolate/src/round.js", "../node_modules/d3-scale/src/number.js", "../node_modules/d3-scale/src/continuous.js", "../node_modules/d3-scale/src/constant.js", "../node_modules/d3-scale/src/tickFormat.js", "../node_modules/d3-format/src/precisionPrefix.js", "../node_modules/d3-format/src/precisionRound.js", "../node_modules/d3-format/src/precisionFixed.js", "../node_modules/d3-scale/src/linear.js", "../node_modules/d3-scale/src/time.js", "../node_modules/d3-scale/src/nice.js", "../node_modules/d3-scale/src/sequential.js"], "sourcesContent": ["/**\n * requestAnimationFrame version: \"0.0.23\" Copyright (c) 2011-2012, <PERSON> ( <EMAIL>) All Rights Reserved.\n * Available via the MIT license.\n * see: http://github.com/cagosta/requestAnimationFrame for details\n *\n * http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n * http://my.opera.com/emoller/blog/2011/12/20/requestanimationframe-for-smart-er-animating\n * requestAnimationFrame polyfill by <PERSON>. fixes from Paul <PERSON> and <PERSON>o <PERSON>\n * MIT license\n *\n */\n\n\n( function( global ) {\n\n\n    ( function() {\n\n\n        if ( global.requestAnimationFrame ) {\n\n            return;\n\n        }\n\n        if ( global.webkitRequestAnimationFrame ) { // Chrome <= 23, Safari <= 6.1, Blackberry 10\n\n            global.requestAnimationFrame = global[ 'webkitRequestAnimationFrame' ];\n            global.cancelAnimationFrame = global[ 'webkitCancelAnimationFrame' ] || global[ 'webkitCancelRequestAnimationFrame' ];\n            return;\n\n        }\n\n        // IE <= 9, Android <= 4.3, very old/rare browsers\n\n        var lastTime = 0;\n\n        global.requestAnimationFrame = function( callback ) {\n\n            var currTime = new Date().getTime();\n\n            var timeToCall = Math.max( 0, 16 - ( currTime - lastTime ) );\n\n            var id = global.setTimeout( function() {\n\n                callback( currTime + timeToCall );\n\n            }, timeToCall );\n\n            lastTime = currTime + timeToCall;\n\n            return id; // return the id for cancellation capabilities\n\n        };\n\n        global.cancelAnimationFrame = function( id ) {\n\n            clearTimeout( id );\n\n        };\n\n    } )();\n\n    if ( typeof define === 'function' ) {\n\n        define( function() {\n\n            return global.requestAnimationFrame;\n\n        } );\n\n    }\n\n} )( window );", "function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n", "function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n", "export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n", "export default function(update) {\n  return new Array(update.length);\n}\n", "import sparse from \"./sparse\";\nimport {Selection} from \"./index\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n", "import {Selection} from \"./index\";\nimport {EnterNode} from \"./enter\";\nimport constant from \"../constant\";\n\nvar keyPrefix = \"$\"; // Protect against keys like “__proto__”.\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = {},\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = keyPrefix + key.call(node, node.__data__, i, group);\n      if (keyValue in nodeByKeyValue) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue[keyValue] = node;\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = keyPrefix + key.call(parent, data[i], i, data);\n    if (node = nodeByKeyValue[keyValue]) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue[keyValue] = null;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue[keyValues[i]] === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nexport default function(value, key) {\n  if (!value) {\n    data = new Array(this.size()), j = -1;\n    this.each(function(d) { data[++j] = d; });\n    return data;\n  }\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = value.call(parent, parent && parent.__data__, j, parents),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n", "import {Selection} from \"./index\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n", "import namespaces from \"./namespaces\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name;\n}\n", "import namespace from \"../namespace\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n", "export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n", "import defaultView from \"../window\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n", "function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n", "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n", "function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n", "function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n", "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n", "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n", "import namespace from \"./namespace\";\nimport {xhtml} from \"./namespaces\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n", "import creator from \"../creator\";\nimport selector from \"../selector\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n", "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n", "function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n", "var filterEvents = {};\n\nexport var event = null;\n\nif (typeof document !== \"undefined\") {\n  var element = document.documentElement;\n  if (!(\"onmouseenter\" in element)) {\n    filterEvents = {mouseenter: \"mouseover\", mouseleave: \"mouseout\"};\n  }\n}\n\nfunction filterContextListener(listener, index, group) {\n  listener = contextListener(listener, index, group);\n  return function(event) {\n    var related = event.relatedTarget;\n    if (!related || (related !== this && !(related.compareDocumentPosition(this) & 8))) {\n      listener.call(this, event);\n    }\n  };\n}\n\nfunction contextListener(listener, index, group) {\n  return function(event1) {\n    var event0 = event; // Events can be reentrant (e.g., focus).\n    event = event1;\n    try {\n      listener.call(this, this.__data__, index, group);\n    } finally {\n      event = event0;\n    }\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.capture);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, capture) {\n  var wrap = filterEvents.hasOwnProperty(typename.type) ? filterContextListener : contextListener;\n  return function(d, i, group) {\n    var on = this.__on, o, listener = wrap(value, i, group);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.capture);\n        this.addEventListener(o.type, o.listener = listener, o.capture = capture);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, capture);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, capture: capture};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, capture) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  if (capture == null) capture = false;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, capture));\n  return this;\n}\n\nexport function customEvent(event1, listener, that, args) {\n  var event0 = event;\n  event1.sourceEvent = event;\n  event = event1;\n  try {\n    return listener.apply(that, args);\n  } finally {\n    event = event0;\n  }\n}\n", "import defaultView from \"../window\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n", "import selection_select from \"./select\";\nimport selection_selectAll from \"./selectAll\";\nimport selection_filter from \"./filter\";\nimport selection_data from \"./data\";\nimport selection_enter from \"./enter\";\nimport selection_exit from \"./exit\";\nimport selection_join from \"./join\";\nimport selection_merge from \"./merge\";\nimport selection_order from \"./order\";\nimport selection_sort from \"./sort\";\nimport selection_call from \"./call\";\nimport selection_nodes from \"./nodes\";\nimport selection_node from \"./node\";\nimport selection_size from \"./size\";\nimport selection_empty from \"./empty\";\nimport selection_each from \"./each\";\nimport selection_attr from \"./attr\";\nimport selection_style from \"./style\";\nimport selection_property from \"./property\";\nimport selection_classed from \"./classed\";\nimport selection_text from \"./text\";\nimport selection_html from \"./html\";\nimport selection_raise from \"./raise\";\nimport selection_lower from \"./lower\";\nimport selection_append from \"./append\";\nimport selection_insert from \"./insert\";\nimport selection_remove from \"./remove\";\nimport selection_clone from \"./clone\";\nimport selection_datum from \"./datum\";\nimport selection_on from \"./on\";\nimport selection_dispatch from \"./dispatch\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch\n};\n\nexport default selection;\n", "import {Selection} from \"./index\";\nimport selector from \"../selector\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "import {Selection} from \"./index\";\nimport selectorAll from \"../selectorAll\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n", "import {Selection} from \"./index\";\nimport matcher from \"../matcher\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import sparse from \"./sparse\";\nimport {Selection} from \"./index\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n", "export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  enter = typeof onenter === \"function\" ? onenter(enter) : enter.append(onenter + \"\");\n  if (onupdate != null) update = onupdate(update);\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n", "import {Selection} from \"./index\";\n\nexport default function(selection) {\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n", "export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n", "export default function() {\n  var nodes = new Array(this.size()), i = -1;\n  this.each(function() { nodes[++i] = this; });\n  return nodes;\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n", "export default function() {\n  var size = 0;\n  this.each(function() { ++size; });\n  return size;\n}\n", "export default function() {\n  return !this.node();\n}\n", "export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n", "import creator from \"../creator\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n", "export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n", "import {Selection, root} from \"./selection/index\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n", "import {event} from \"./selection/on\";\n\nexport default function() {\n  var current = event, source;\n  while (source = current.sourceEvent) current = source;\n  return current;\n}\n", "export default function(node, event) {\n  var svg = node.ownerSVGElement || node;\n\n  if (svg.createSVGPoint) {\n    var point = svg.createSVGPoint();\n    point.x = event.clientX, point.y = event.clientY;\n    point = point.matrixTransform(node.getScreenCTM().inverse());\n    return [point.x, point.y];\n  }\n\n  var rect = node.getBoundingClientRect();\n  return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n}\n", "import sourceEvent from \"./sourceEvent\";\nimport point from \"./point\";\n\nexport default function(node) {\n  var event = sourceEvent();\n  if (event.changedTouches) event = event.changedTouches[0];\n  return point(node, event);\n}\n", "import {Selection, root} from \"./selection/index\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([document.querySelectorAll(selector)], [document.documentElement])\n      : new Selection([selector == null ? [] : selector], root);\n}\n", "import {event} from \"d3-selection\";\n\nexport function nopropagation() {\n  event.stopImmediatePropagation();\n}\n\nexport default function() {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {select} from \"d3-selection\";\nimport noevent from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, true);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, true);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, true);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n", "import sourceEvent from \"./sourceEvent\";\nimport point from \"./point\";\n\nexport default function(node, touches, identifier) {\n  if (arguments.length < 3) identifier = touches, touches = sourceEvent().changedTouches;\n\n  for (var i = 0, n = touches ? touches.length : 0, touch; i < n; ++i) {\n    if ((touch = touches[i]).identifier === identifier) {\n      return point(node, touch);\n    }\n  }\n\n  return null;\n}\n", "var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n", "import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n", "import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n", "import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n", "import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n", "export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n", "import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n", "import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n", "import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n", "import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n", "import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n", "import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n", "import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n", "import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n", "import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n", "import {Transition} from \"./index.js\";\n\nexport default function(transition) {\n  if (transition._id !== this._id) throw new Error;\n\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Transition(merges, this._parents, this._name, this._id);\n}\n", "import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n", "import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n", "function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n", "import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n", "function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n", "function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n", "import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n", "import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n", "import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n", "export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(target, type, selection) {\n  this.target = target;\n  this.type = type;\n  this.selection = selection;\n}\n", "import {event} from \"d3-selection\";\n\nexport function nopropagation() {\n  event.stopImmediatePropagation();\n}\n\nexport default function() {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n", "import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolate} from \"d3-interpolate\";\nimport {customEvent, event, touch, mouse, select} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\nvar MODE_DRAG = {name: \"drag\"},\n    MODE_SPACE = {name: \"space\"},\n    MODE_HANDLE = {name: \"handle\"},\n    MODE_CENTER = {name: \"center\"};\n\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\n\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\n\nfunction toucher(identifier) {\n  return function(target) {\n    return touch(target, event.touches, identifier);\n  };\n}\n\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function(x, e) { return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]]; },\n  output: function(xy) { return xy && [xy[0][0], xy[1][0]]; }\n};\n\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function(y, e) { return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]]; },\n  output: function(xy) { return xy && [xy[0][1], xy[1][1]]; }\n};\n\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function(xy) { return xy == null ? null : number2(xy); },\n  output: function(xy) { return xy; }\n};\n\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\n\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\n\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\n\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\n\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\n\nfunction type(t) {\n  return {type: t};\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter() {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\n\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0]\n      || extent[0][1] === extent[1][1];\n}\n\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\n\nexport function brushX() {\n  return brush(X);\n}\n\nexport function brushY() {\n  return brush(Y);\n}\n\nexport default function() {\n  return brush(XY);\n}\n\nfunction brush(dim) {\n  var extent = defaultExtent,\n      filter = defaultFilter,\n      touchable = defaultTouchable,\n      keys = true,\n      listeners = dispatch(\"start\", \"brush\", \"end\"),\n      handleSize = 6,\n      touchending;\n\n  function brush(group) {\n    var overlay = group\n        .property(\"__brush\", initialize)\n      .selectAll(\".overlay\")\n      .data([type(\"overlay\")]);\n\n    overlay.enter().append(\"rect\")\n        .attr(\"class\", \"overlay\")\n        .attr(\"pointer-events\", \"all\")\n        .attr(\"cursor\", cursors.overlay)\n      .merge(overlay)\n        .each(function() {\n          var extent = local(this).extent;\n          select(this)\n              .attr(\"x\", extent[0][0])\n              .attr(\"y\", extent[0][1])\n              .attr(\"width\", extent[1][0] - extent[0][0])\n              .attr(\"height\", extent[1][1] - extent[0][1]);\n        });\n\n    group.selectAll(\".selection\")\n      .data([type(\"selection\")])\n      .enter().append(\"rect\")\n        .attr(\"class\", \"selection\")\n        .attr(\"cursor\", cursors.selection)\n        .attr(\"fill\", \"#777\")\n        .attr(\"fill-opacity\", 0.3)\n        .attr(\"stroke\", \"#fff\")\n        .attr(\"shape-rendering\", \"crispEdges\");\n\n    var handle = group.selectAll(\".handle\")\n      .data(dim.handles, function(d) { return d.type; });\n\n    handle.exit().remove();\n\n    handle.enter().append(\"rect\")\n        .attr(\"class\", function(d) { return \"handle handle--\" + d.type; })\n        .attr(\"cursor\", function(d) { return cursors[d.type]; });\n\n    group\n        .each(redraw)\n        .attr(\"fill\", \"none\")\n        .attr(\"pointer-events\", \"all\")\n        .on(\"mousedown.brush\", started)\n      .filter(touchable)\n        .on(\"touchstart.brush\", started)\n        .on(\"touchmove.brush\", touchmoved)\n        .on(\"touchend.brush touchcancel.brush\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  brush.move = function(group, selection) {\n    if (group.selection) {\n      group\n          .on(\"start.brush\", function() { emitter(this, arguments).beforestart().start(); })\n          .on(\"interrupt.brush end.brush\", function() { emitter(this, arguments).end(); })\n          .tween(\"brush\", function() {\n            var that = this,\n                state = that.__brush,\n                emit = emitter(that, arguments),\n                selection0 = state.selection,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n                i = interpolate(selection0, selection1);\n\n            function tween(t) {\n              state.selection = t === 1 && selection1 === null ? null : i(t);\n              redraw.call(that);\n              emit.brush();\n            }\n\n            return selection0 !== null && selection1 !== null ? tween : tween(1);\n          });\n    } else {\n      group\n          .each(function() {\n            var that = this,\n                args = arguments,\n                state = that.__brush,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n                emit = emitter(that, args).beforestart();\n\n            interrupt(that);\n            state.selection = selection1 === null ? null : selection1;\n            redraw.call(that);\n            emit.start().brush().end();\n          });\n    }\n  };\n\n  brush.clear = function(group) {\n    brush.move(group, null);\n  };\n\n  function redraw() {\n    var group = select(this),\n        selection = local(this).selection;\n\n    if (selection) {\n      group.selectAll(\".selection\")\n          .style(\"display\", null)\n          .attr(\"x\", selection[0][0])\n          .attr(\"y\", selection[0][1])\n          .attr(\"width\", selection[1][0] - selection[0][0])\n          .attr(\"height\", selection[1][1] - selection[0][1]);\n\n      group.selectAll(\".handle\")\n          .style(\"display\", null)\n          .attr(\"x\", function(d) { return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2; })\n          .attr(\"y\", function(d) { return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2; })\n          .attr(\"width\", function(d) { return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize; })\n          .attr(\"height\", function(d) { return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize; });\n    }\n\n    else {\n      group.selectAll(\".selection,.handle\")\n          .style(\"display\", \"none\")\n          .attr(\"x\", null)\n          .attr(\"y\", null)\n          .attr(\"width\", null)\n          .attr(\"height\", null);\n    }\n  }\n\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n\n  Emitter.prototype = {\n    beforestart: function() {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function() {\n      if (this.starting) this.starting = false, this.emit(\"start\");\n      else this.emit(\"brush\");\n      return this;\n    },\n    brush: function() {\n      this.emit(\"brush\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\");\n      return this;\n    },\n    emit: function(type) {\n      customEvent(new BrushEvent(brush, type, dim.output(this.state.selection)), listeners.apply, listeners, [type, this.that, this.args]);\n    }\n  };\n\n  function started() {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n\n    var that = this,\n        type = event.target.__data__.type,\n        mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : (keys && event.altKey ? MODE_CENTER : MODE_HANDLE),\n        signX = dim === Y ? null : signsX[type],\n        signY = dim === X ? null : signsY[type],\n        state = local(that),\n        extent = state.extent,\n        selection = state.selection,\n        W = extent[0][0], w0, w1,\n        N = extent[0][1], n0, n1,\n        E = extent[1][0], e0, e1,\n        S = extent[1][1], s0, s1,\n        dx = 0,\n        dy = 0,\n        moving,\n        shifting = signX && signY && keys && event.shiftKey,\n        lockX,\n        lockY,\n        pointer = event.touches ? toucher(event.changedTouches[0].identifier) : mouse,\n        point0 = pointer(that),\n        point = point0,\n        emit = emitter(that, arguments, true).beforestart();\n\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      state.selection = selection = [\n        [w0 = dim === Y ? W : point0[0], n0 = dim === X ? N : point0[1]],\n        [e0 = dim === Y ? E : w0, s0 = dim === X ? S : n0]\n      ];\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n\n    var group = select(that)\n        .attr(\"pointer-events\", \"none\");\n\n    var overlay = group.selectAll(\".overlay\")\n        .attr(\"cursor\", cursors[type]);\n\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view)\n          .on(\"mousemove.brush\", moved, true)\n          .on(\"mouseup.brush\", ended, true);\n      if (keys) view\n          .on(\"keydown.brush\", keydowned, true)\n          .on(\"keyup.brush\", keyupped, true)\n\n      dragDisable(event.view);\n    }\n\n    nopropagation();\n    interrupt(that);\n    redraw.call(that);\n    emit.start();\n\n    function moved() {\n      var point1 = pointer(that);\n      if (shifting && !lockX && !lockY) {\n        if (Math.abs(point1[0] - point[0]) > Math.abs(point1[1] - point[1])) lockY = true;\n        else lockX = true;\n      }\n      point = point1;\n      moving = true;\n      noevent();\n      move();\n    }\n\n    function move() {\n      var t;\n\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG: {\n          if (signX) dx = Math.max(W - w0, Math.min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n          if (signY) dy = Math.max(N - n0, Math.min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n          break;\n        }\n        case MODE_HANDLE: {\n          if (signX < 0) dx = Math.max(W - w0, Math.min(E - w0, dx)), w1 = w0 + dx, e1 = e0;\n          else if (signX > 0) dx = Math.max(W - e0, Math.min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n          if (signY < 0) dy = Math.max(N - n0, Math.min(S - n0, dy)), n1 = n0 + dy, s1 = s0;\n          else if (signY > 0) dy = Math.max(N - s0, Math.min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n          break;\n        }\n        case MODE_CENTER: {\n          if (signX) w1 = Math.max(W, Math.min(E, w0 - dx * signX)), e1 = Math.max(W, Math.min(E, e0 + dx * signX));\n          if (signY) n1 = Math.max(N, Math.min(S, n0 - dy * signY)), s1 = Math.max(N, Math.min(S, s0 + dy * signY));\n          break;\n        }\n      }\n\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n\n      if (selection[0][0] !== w1\n          || selection[0][1] !== n1\n          || selection[1][0] !== e1\n          || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush();\n      }\n    }\n\n    function ended() {\n      nopropagation();\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end();\n    }\n\n    function keydowned() {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          shifting = signX && signY;\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_HANDLE) {\n            if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n            if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n            mode = MODE_CENTER;\n            move();\n          }\n          break;\n        }\n        case 32: { // SPACE; takes priority over ALT\n          if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1 - dx; else if (signX > 0) w0 = w1 - dx;\n            if (signY < 0) s0 = s1 - dy; else if (signY > 0) n0 = n1 - dy;\n            mode = MODE_SPACE;\n            overlay.attr(\"cursor\", cursors.selection);\n            move();\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent();\n    }\n\n    function keyupped() {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          if (shifting) {\n            lockX = lockY = shifting = false;\n            move();\n          }\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n            if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n            mode = MODE_HANDLE;\n            move();\n          }\n          break;\n        }\n        case 32: { // SPACE\n          if (mode === MODE_SPACE) {\n            if (event.altKey) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n            } else {\n              if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n            }\n            overlay.attr(\"cursor\", cursors[type]);\n            move();\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent();\n    }\n  }\n\n  function touchmoved() {\n    emitter(this, arguments).moved();\n  }\n\n  function touchended() {\n    emitter(this, arguments).ended();\n  }\n\n  function initialize() {\n    var state = this.__brush || {selection: null};\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n\n  brush.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n\n  brush.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n\n  brush.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n\n  brush.handleSize = function(_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n\n  brush.keyModifiers = function(_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n\n  brush.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n\n  return brush;\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function DragEvent(target, type, subject, id, active, x, y, dx, dy, dispatch) {\n  this.target = target;\n  this.type = type;\n  this.subject = subject;\n  this.identifier = id;\n  this.active = active;\n  this.x = x;\n  this.y = y;\n  this.dx = dx;\n  this.dy = dy;\n  this._ = dispatch;\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n", "import {dispatch} from \"d3-dispatch\";\nimport {event, customEvent, select, mouse, touch} from \"d3-selection\";\nimport nodrag, {yesdrag} from \"./nodrag.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\nimport constant from \"./constant.js\";\nimport DragEvent from \"./event.js\";\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter() {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultContainer() {\n  return this.parentNode;\n}\n\nfunction defaultSubject(d) {\n  return d == null ? {x: event.x, y: event.y} : d;\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      container = defaultContainer,\n      subject = defaultSubject,\n      touchable = defaultTouchable,\n      gestures = {},\n      listeners = dispatch(\"start\", \"drag\", \"end\"),\n      active = 0,\n      mousedownx,\n      mousedowny,\n      mousemoving,\n      touchending,\n      clickDistance2 = 0;\n\n  function drag(selection) {\n    selection\n        .on(\"mousedown.drag\", mousedowned)\n      .filter(touchable)\n        .on(\"touchstart.drag\", touchstarted)\n        .on(\"touchmove.drag\", touchmoved)\n        .on(\"touchend.drag touchcancel.drag\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  function mousedowned() {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var gesture = beforestart(\"mouse\", container.apply(this, arguments), mouse, this, arguments);\n    if (!gesture) return;\n    select(event.view).on(\"mousemove.drag\", mousemoved, true).on(\"mouseup.drag\", mouseupped, true);\n    nodrag(event.view);\n    nopropagation();\n    mousemoving = false;\n    mousedownx = event.clientX;\n    mousedowny = event.clientY;\n    gesture(\"start\");\n  }\n\n  function mousemoved() {\n    noevent();\n    if (!mousemoving) {\n      var dx = event.clientX - mousedownx, dy = event.clientY - mousedowny;\n      mousemoving = dx * dx + dy * dy > clickDistance2;\n    }\n    gestures.mouse(\"drag\");\n  }\n\n  function mouseupped() {\n    select(event.view).on(\"mousemove.drag mouseup.drag\", null);\n    yesdrag(event.view, mousemoving);\n    noevent();\n    gestures.mouse(\"end\");\n  }\n\n  function touchstarted() {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.changedTouches,\n        c = container.apply(this, arguments),\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = beforestart(touches[i].identifier, c, touch, this, arguments)) {\n        nopropagation();\n        gesture(\"start\");\n      }\n    }\n  }\n\n  function touchmoved() {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        noevent();\n        gesture(\"drag\");\n      }\n    }\n  }\n\n  function touchended() {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        nopropagation();\n        gesture(\"end\");\n      }\n    }\n  }\n\n  function beforestart(id, container, point, that, args) {\n    var p = point(container, id), s, dx, dy,\n        sublisteners = listeners.copy();\n\n    if (!customEvent(new DragEvent(drag, \"beforestart\", s, id, active, p[0], p[1], 0, 0, sublisteners), function() {\n      if ((event.subject = s = subject.apply(that, args)) == null) return false;\n      dx = s.x - p[0] || 0;\n      dy = s.y - p[1] || 0;\n      return true;\n    })) return;\n\n    return function gesture(type) {\n      var p0 = p, n;\n      switch (type) {\n        case \"start\": gestures[id] = gesture, n = active++; break;\n        case \"end\": delete gestures[id], --active; // nobreak\n        case \"drag\": p = point(container, id), n = active; break;\n      }\n      customEvent(new DragEvent(drag, type, s, id, n, p[0] + dx, p[1] + dy, p[0] - p0[0], p[1] - p0[1], sublisteners), sublisteners.apply, sublisteners, [type, that, args]);\n    };\n  }\n\n  drag.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), drag) : filter;\n  };\n\n  drag.container = function(_) {\n    return arguments.length ? (container = typeof _ === \"function\" ? _ : constant(_), drag) : container;\n  };\n\n  drag.subject = function(_) {\n    return arguments.length ? (subject = typeof _ === \"function\" ? _ : constant(_), drag) : subject;\n  };\n\n  drag.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), drag) : touchable;\n  };\n\n  drag.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? drag : value;\n  };\n\n  drag.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, drag) : Math.sqrt(clickDistance2);\n  };\n\n  return drag;\n}\n", "export var abs = Math.abs;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var max = Math.max;\nexport var min = Math.min;\nexport var sin = Math.sin;\nexport var sqrt = Math.sqrt;\n\nexport var epsilon = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n", "import {path} from \"d3-path\";\nimport constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null;\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle.\n        if (da < pi && (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10))) {\n          var ax = x01 - oc[0],\n              ay = y01 - oc[1],\n              bx = x11 - oc[0],\n              by = y11 - oc[1],\n              kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n              lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n          rc0 = min(rc, (r0 - lc) / (kc - 1));\n          rc1 = min(rc, (r1 - lc) / (kc + 1));\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n", "export default function(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "import ascending from \"./ascending\";\nimport bisector from \"./bisector\";\n\nvar ascendingBisect = bisector(ascending);\nexport var bisectRight = ascendingBisect.right;\nexport var bisectLeft = ascendingBisect.left;\nexport default bisectRight;\n", "import ascending from \"./ascending\";\n\nexport default function(compare) {\n  if (compare.length === 1) compare = ascendingComparator(compare);\n  return {\n    left: function(a, x, lo, hi) {\n      if (lo == null) lo = 0;\n      if (hi == null) hi = a.length;\n      while (lo < hi) {\n        var mid = lo + hi >>> 1;\n        if (compare(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      }\n      return lo;\n    },\n    right: function(a, x, lo, hi) {\n      if (lo == null) lo = 0;\n      if (hi == null) hi = a.length;\n      while (lo < hi) {\n        var mid = lo + hi >>> 1;\n        if (compare(a[mid], x) > 0) hi = mid;\n        else lo = mid + 1;\n      }\n      return lo;\n    }\n  };\n}\n\nfunction ascendingComparator(f) {\n  return function(d, x) {\n    return ascending(f(d), x);\n  };\n}\n", "export default function(values, valueof) {\n  var n = values.length,\n      i = -1,\n      value,\n      min,\n      max;\n\n  if (valueof == null) {\n    while (++i < n) { // Find the first comparable value.\n      if ((value = values[i]) != null && value >= value) {\n        min = max = value;\n        while (++i < n) { // Compare the remaining values.\n          if ((value = values[i]) != null) {\n            if (min > value) min = value;\n            if (max < value) max = value;\n          }\n        }\n      }\n    }\n  }\n\n  else {\n    while (++i < n) { // Find the first comparable value.\n      if ((value = valueof(values[i], i, values)) != null && value >= value) {\n        min = max = value;\n        while (++i < n) { // Compare the remaining values.\n          if ((value = valueof(values[i], i, values)) != null) {\n            if (min > value) min = value;\n            if (max < value) max = value;\n          }\n        }\n      }\n    }\n  }\n\n  return [min, max];\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "var e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nexport default function(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    start = Math.ceil(start / step);\n    stop = Math.floor(stop / step);\n    ticks = new Array(n = Math.ceil(stop - start + 1));\n    while (++i < n) ticks[i] = (start + i) * step;\n  } else {\n    start = Math.floor(start * step);\n    stop = Math.ceil(stop * step);\n    ticks = new Array(n = Math.ceil(start - stop + 1));\n    while (++i < n) ticks[i] = (start - i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0\n      ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)\n      : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nexport function tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;\n  else if (error >= e5) step1 *= 5;\n  else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}\n", "export var slice = Array.prototype.slice;\n", "export default function(x) {\n  return x;\n}\n", "import {slice} from \"./array\";\nimport identity from \"./identity\";\n\nvar top = 1,\n    right = 2,\n    bottom = 3,\n    left = 4,\n    epsilon = 1e-6;\n\nfunction translateX(x) {\n  return \"translate(\" + (x + 0.5) + \",0)\";\n}\n\nfunction translateY(y) {\n  return \"translate(0,\" + (y + 0.5) + \")\";\n}\n\nfunction number(scale) {\n  return function(d) {\n    return +scale(d);\n  };\n}\n\nfunction center(scale) {\n  var offset = Math.max(0, scale.bandwidth() - 1) / 2; // Adjust for 0.5px offset.\n  if (scale.round()) offset = Math.round(offset);\n  return function(d) {\n    return +scale(d) + offset;\n  };\n}\n\nfunction entering() {\n  return !this.__axis;\n}\n\nfunction axis(orient, scale) {\n  var tickArguments = [],\n      tickValues = null,\n      tickFormat = null,\n      tickSizeInner = 6,\n      tickSizeOuter = 6,\n      tickPadding = 3,\n      k = orient === top || orient === left ? -1 : 1,\n      x = orient === left || orient === right ? \"x\" : \"y\",\n      transform = orient === top || orient === bottom ? translateX : translateY;\n\n  function axis(context) {\n    var values = tickValues == null ? (scale.ticks ? scale.ticks.apply(scale, tickArguments) : scale.domain()) : tickValues,\n        format = tickFormat == null ? (scale.tickFormat ? scale.tickFormat.apply(scale, tickArguments) : identity) : tickFormat,\n        spacing = Math.max(tickSizeInner, 0) + tickPadding,\n        range = scale.range(),\n        range0 = +range[0] + 0.5,\n        range1 = +range[range.length - 1] + 0.5,\n        position = (scale.bandwidth ? center : number)(scale.copy()),\n        selection = context.selection ? context.selection() : context,\n        path = selection.selectAll(\".domain\").data([null]),\n        tick = selection.selectAll(\".tick\").data(values, scale).order(),\n        tickExit = tick.exit(),\n        tickEnter = tick.enter().append(\"g\").attr(\"class\", \"tick\"),\n        line = tick.select(\"line\"),\n        text = tick.select(\"text\");\n\n    path = path.merge(path.enter().insert(\"path\", \".tick\")\n        .attr(\"class\", \"domain\")\n        .attr(\"stroke\", \"currentColor\"));\n\n    tick = tick.merge(tickEnter);\n\n    line = line.merge(tickEnter.append(\"line\")\n        .attr(\"stroke\", \"currentColor\")\n        .attr(x + \"2\", k * tickSizeInner));\n\n    text = text.merge(tickEnter.append(\"text\")\n        .attr(\"fill\", \"currentColor\")\n        .attr(x, k * spacing)\n        .attr(\"dy\", orient === top ? \"0em\" : orient === bottom ? \"0.71em\" : \"0.32em\"));\n\n    if (context !== selection) {\n      path = path.transition(context);\n      tick = tick.transition(context);\n      line = line.transition(context);\n      text = text.transition(context);\n\n      tickExit = tickExit.transition(context)\n          .attr(\"opacity\", epsilon)\n          .attr(\"transform\", function(d) { return isFinite(d = position(d)) ? transform(d) : this.getAttribute(\"transform\"); });\n\n      tickEnter\n          .attr(\"opacity\", epsilon)\n          .attr(\"transform\", function(d) { var p = this.parentNode.__axis; return transform(p && isFinite(p = p(d)) ? p : position(d)); });\n    }\n\n    tickExit.remove();\n\n    path\n        .attr(\"d\", orient === left || orient == right\n            ? (tickSizeOuter ? \"M\" + k * tickSizeOuter + \",\" + range0 + \"H0.5V\" + range1 + \"H\" + k * tickSizeOuter : \"M0.5,\" + range0 + \"V\" + range1)\n            : (tickSizeOuter ? \"M\" + range0 + \",\" + k * tickSizeOuter + \"V0.5H\" + range1 + \"V\" + k * tickSizeOuter : \"M\" + range0 + \",0.5H\" + range1));\n\n    tick\n        .attr(\"opacity\", 1)\n        .attr(\"transform\", function(d) { return transform(position(d)); });\n\n    line\n        .attr(x + \"2\", k * tickSizeInner);\n\n    text\n        .attr(x, k * spacing)\n        .text(format);\n\n    selection.filter(entering)\n        .attr(\"fill\", \"none\")\n        .attr(\"font-size\", 10)\n        .attr(\"font-family\", \"sans-serif\")\n        .attr(\"text-anchor\", orient === right ? \"start\" : orient === left ? \"end\" : \"middle\");\n\n    selection\n        .each(function() { this.__axis = position; });\n  }\n\n  axis.scale = function(_) {\n    return arguments.length ? (scale = _, axis) : scale;\n  };\n\n  axis.ticks = function() {\n    return tickArguments = slice.call(arguments), axis;\n  };\n\n  axis.tickArguments = function(_) {\n    return arguments.length ? (tickArguments = _ == null ? [] : slice.call(_), axis) : tickArguments.slice();\n  };\n\n  axis.tickValues = function(_) {\n    return arguments.length ? (tickValues = _ == null ? null : slice.call(_), axis) : tickValues && tickValues.slice();\n  };\n\n  axis.tickFormat = function(_) {\n    return arguments.length ? (tickFormat = _, axis) : tickFormat;\n  };\n\n  axis.tickSize = function(_) {\n    return arguments.length ? (tickSizeInner = tickSizeOuter = +_, axis) : tickSizeInner;\n  };\n\n  axis.tickSizeInner = function(_) {\n    return arguments.length ? (tickSizeInner = +_, axis) : tickSizeInner;\n  };\n\n  axis.tickSizeOuter = function(_) {\n    return arguments.length ? (tickSizeOuter = +_, axis) : tickSizeOuter;\n  };\n\n  axis.tickPadding = function(_) {\n    return arguments.length ? (tickPadding = +_, axis) : tickPadding;\n  };\n\n  return axis;\n}\n\nexport function axisTop(scale) {\n  return axis(top, scale);\n}\n\nexport function axisRight(scale) {\n  return axis(right, scale);\n}\n\nexport function axisBottom(scale) {\n  return axis(bottom, scale);\n}\n\nexport function axisLeft(scale) {\n  return axis(left, scale);\n}\n", "import 'requestanimationframe';\n\nconst renderQueue = function(func) {\n  let _queue = [], // data to be rendered\n    _rate = 1000, // number of calls per frame\n    _invalidate = function() {}, // invalidate last render queue\n    _clear = function() {}; // clearing function\n\n  let rq = function(data) {\n    if (data) rq.data(data);\n    _invalidate();\n    _clear();\n    rq.render();\n  };\n\n  rq.render = function() {\n    let valid = true;\n    _invalidate = rq.invalidate = function() {\n      valid = false;\n    };\n\n    function doFrame() {\n      if (!valid) return true;\n      let chunk = _queue.splice(0, _rate);\n      chunk.map(func);\n      requestAnimationFrame(doFrame);\n    }\n\n    doFrame();\n  };\n\n  rq.data = function(data) {\n    _invalidate();\n    _queue = data.slice(0); // creates a copy of the data\n    return rq;\n  };\n\n  rq.add = function(data) {\n    _queue = _queue.concat(data);\n  };\n\n  rq.rate = function(value) {\n    if (!arguments.length) return _rate;\n    _rate = value;\n    return rq;\n  };\n\n  rq.remaining = function() {\n    return _queue.length;\n  };\n\n  // clear the canvas\n  rq.clear = function(func) {\n    if (!arguments.length) {\n      _clear();\n      return rq;\n    }\n    _clear = func;\n    return rq;\n  };\n\n  rq.invalidate = _invalidate;\n\n  return rq;\n};\n\nexport default renderQueue;\n", "const w = config => config.width - config.margin.right - config.margin.left;\n\nexport default w;\n", "const invertCategorical = (selection, scale) => {\n  if (selection.length === 0) {\n    return [];\n  }\n  const domain = scale.domain();\n  const range = scale.range();\n  const found = [];\n  range.forEach((d, i) => {\n    if (d >= selection[0] && d <= selection[1]) {\n      found.push(domain[i]);\n    }\n  });\n  return found;\n};\n\nconst invertByScale = (selection, scale) => {\n  if (scale === null) return [];\n  return typeof scale.invert === 'undefined'\n    ? invertCategorical(selection, scale)\n    : selection.map(d => scale.invert(d));\n};\n\nexport default invertByScale;\nexport { invertByScale };\n", "import { select } from 'd3-selection';\nimport { brushSelection } from 'd3-brush';\n\nimport invertByScale from '../invertByScale';\n\nconst brushExtents = (state, config, pc) => extents => {\n  const { brushes, brushNodes } = state;\n\n  if (typeof extents === 'undefined') {\n    return Object.keys(config.dimensions).reduce((acc, cur) => {\n      const brush = brushes[cur];\n      //todo: brush check\n      if (brush !== undefined && brushSelection(brushNodes[cur]) !== null) {\n        const raw = brushSelection(brushNodes[cur]);\n        const yScale = config.dimensions[cur].yscale;\n        const scaled = invertByScale(raw, yScale);\n\n        acc[cur] = {\n          extent: brush.extent(),\n          selection: {\n            raw,\n            scaled,\n          },\n        };\n      }\n\n      return acc;\n    }, {});\n  } else {\n    //first get all the brush selections\n    const brushSelections = {};\n    pc.g()\n      .selectAll('.brush')\n      .each(function(d) {\n        brushSelections[d] = select(this);\n      });\n\n    // loop over each dimension and update appropriately (if it was passed in through extents)\n    Object.keys(config.dimensions).forEach(d => {\n      if (extents[d] === undefined) {\n        return;\n      }\n\n      const brush = brushes[d];\n      if (brush !== undefined) {\n        const dim = config.dimensions[d];\n        const yExtent = extents[d].map(dim.yscale);\n\n        //update the extent\n        //sets the brushable extent to the specified array of points [[x0, y0], [x1, y1]]\n        //we actually don't need this since we are using brush.move below\n        //extents set the limits of the brush which means a user will not be able\n        //to move or drag the brush beyond the limits set by brush.extent\n        //brush.extent([[-15, yExtent[1]], [15, yExtent[0]]]);\n\n        //redraw the brush\n        //https://github.com/d3/d3-brush#brush_move\n        // For an x-brush, it must be defined as [x0, x1]; for a y-brush, it must be defined as [y0, y1].\n        brushSelections[d].call(brush).call(brush.move, yExtent.reverse());\n\n        //fire some events\n        // brush.event(brushSelections[d]);\n      }\n    });\n\n    //redraw the chart\n    pc.renderBrushed();\n\n    return pc;\n  }\n};\n\nexport default brushExtents;\n", "import { brushSelection } from 'd3-brush';\n//https://github.com/d3/d3-brush/issues/10\n\n// data within extents\nconst selected = (state, config, brushGroup) => () => {\n  const { brushNodes } = state;\n  const is_brushed = p =>\n    brushNodes[p] && brushSelection(brushNodes[p]) !== null;\n\n  const actives = Object.keys(config.dimensions).filter(is_brushed);\n  const extents = actives.map(p => {\n    const _brushRange = brushSelection(brushNodes[p]);\n\n    if (typeof config.dimensions[p].yscale.invert === 'function') {\n      return [\n        config.dimensions[p].yscale.invert(_brushRange[1]),\n        config.dimensions[p].yscale.invert(_brushRange[0]),\n      ];\n    } else {\n      return _brushRange;\n    }\n  });\n  // We don't want to return the full data set when there are no axes brushed.\n  // Actually, when there are no axes brushed, by definition, no items are\n  // selected. So, let's avoid the filtering and just return false.\n  //if (actives.length === 0) return false;\n\n  // Resolves broken examples for now. They expect to get the full dataset back from empty brushes\n  if (actives.length === 0) return config.data;\n\n  // test if within range\n  const within = {\n    date: (d, p, dimension) => {\n      if (typeof config.dimensions[p].yscale.bandwidth === 'function') {\n        // if it is ordinal\n        return (\n          extents[dimension][0] <= config.dimensions[p].yscale(d[p]) &&\n          config.dimensions[p].yscale(d[p]) <= extents[dimension][1]\n        );\n      } else {\n        return extents[dimension][0] <= d[p] && d[p] <= extents[dimension][1];\n      }\n    },\n    number: (d, p, dimension) => {\n      if (typeof config.dimensions[p].yscale.bandwidth === 'function') {\n        // if it is ordinal\n        return (\n          extents[dimension][0] <= config.dimensions[p].yscale(d[p]) &&\n          config.dimensions[p].yscale(d[p]) <= extents[dimension][1]\n        );\n      } else {\n        return extents[dimension][0] <= d[p] && d[p] <= extents[dimension][1];\n      }\n    },\n    string: (d, p, dimension) => {\n      return (\n        extents[dimension][0] <= config.dimensions[p].yscale(d[p]) &&\n        config.dimensions[p].yscale(d[p]) <= extents[dimension][1]\n      );\n    },\n  };\n\n  return config.data.filter(d => {\n    switch (brushGroup.predicate) {\n      case 'AND':\n        return actives.every(function(p, dimension) {\n          return within[config.dimensions[p].type](d, p, dimension);\n        });\n      case 'OR':\n        return actives.some(function(p, dimension) {\n          return within[config.dimensions[p].type](d, p, dimension);\n        });\n      default:\n        throw new Error('Unknown brush predicate ' + config.brushPredicate);\n    }\n  });\n};\n\nexport default selected;\n", "import { brushY, brushSelection } from 'd3-brush';\nimport { event } from 'd3-selection';\nimport invertByScale from '../invertByScale';\nimport selected from './selected';\n\nconst brushUpdated = (config, pc, events, args) => newSelection => {\n  config.brushed = newSelection;\n  events.call('brush', pc, config.brushed, args);\n  pc.renderBrushed();\n};\n\nconst brushFor = (state, config, pc, events, brushGroup) => (\n  axis,\n  _selector\n) => {\n  // handle hidden axes which will not be a property of dimensions\n  if (!config.dimensions.hasOwnProperty(axis)) {\n    return () => {};\n  }\n\n  const brushRangeMax =\n    config.dimensions[axis].type === 'string'\n      ? config.dimensions[axis].yscale.range()[\n          config.dimensions[axis].yscale.range().length - 1\n        ]\n      : config.dimensions[axis].yscale.range()[0];\n\n  const _brush = brushY(_selector).extent([[-15, 0], [15, brushRangeMax]]);\n\n  const convertBrushArguments = args => {\n    const args_array = Array.prototype.slice.call(args);\n    const axis = args_array[0];\n\n    const raw = brushSelection(args_array[2][0]) || [];\n\n    // handle hidden axes which will not have a yscale\n    let yscale = null;\n    if (config.dimensions.hasOwnProperty(axis)) {\n      yscale = config.dimensions[axis].yscale;\n    }\n\n    // ordinal scales do not have invert\n    const scaled = invertByScale(raw, yscale);\n\n    return {\n      axis: args_array[0],\n      node: args_array[2][0],\n      selection: {\n        raw,\n        scaled,\n      },\n    };\n  };\n\n  _brush\n    .on('start', function() {\n      if (event.sourceEvent !== null) {\n        events.call(\n          'brushstart',\n          pc,\n          config.brushed,\n          convertBrushArguments(arguments)\n        );\n        if (typeof event.sourceEvent.stopPropagation === 'function') {\n          event.sourceEvent.stopPropagation();\n        }\n      }\n    })\n    .on('brush', function() {\n      brushUpdated(\n        config,\n        pc,\n        events,\n        convertBrushArguments(arguments)\n      )(selected(state, config, brushGroup)());\n    })\n    .on('end', function() {\n      brushUpdated(config, pc, events)(selected(state, config, brushGroup)());\n      events.call(\n        'brushend',\n        pc,\n        config.brushed,\n        convertBrushArguments(arguments)\n      );\n    });\n\n  state.brushes[axis] = _brush;\n  state.brushNodes[axis] = _selector.node();\n\n  return _brush;\n};\n\nexport default brushFor;\n", "import { select } from 'd3-selection';\nimport brushExtents from './brushExtents';\nimport brushReset from './brushReset';\nimport brushFor from './brushFor';\n\nconst install = (state, config, pc, events, brushGroup) => () => {\n  if (!pc.g()) {\n    pc.createAxes();\n  }\n\n  // Add and store a brush for each axis.\n  const brush = pc\n    .g()\n    .append('svg:g')\n    .attr('class', 'brush')\n    .each(function(d) {\n      select(this).call(\n        brushFor(state, config, pc, events, brushGroup)(d, select(this))\n      );\n    });\n  brush\n    .selectAll('rect')\n    .style('visibility', null)\n    .attr('x', -15)\n    .attr('width', 30);\n\n  brush.selectAll('rect.background').style('fill', 'transparent');\n\n  brush\n    .selectAll('rect.extent')\n    .style('fill', 'rgba(255,255,255,0.25)')\n    .style('stroke', 'rgba(0,0,0,0.6)');\n\n  brush.selectAll('.resize rect').style('fill', 'rgba(0,0,0,0.1)');\n\n  pc.brushExtents = brushExtents(state, config, pc);\n  pc.brushReset = brushReset(state, config, pc);\n  return pc;\n};\n\nexport default install;\n", "import { select } from 'd3-selection';\n\nconst brushReset = (state, config, pc) => dimension => {\n  const { brushes } = state;\n\n  if (dimension === undefined) {\n    config.brushed = false;\n    if (pc.g() !== undefined && pc.g() !== null) {\n      pc.g()\n        .selectAll('.brush')\n        .each(function(d) {\n          if (brushes[d] !== undefined) {\n            select(this).call(brushes[d].move, null);\n          }\n        });\n      pc.renderBrushed();\n    }\n  } else {\n    config.brushed = false;\n    if (pc.g() !== undefined && pc.g() !== null) {\n      pc.g()\n        .selectAll('.brush')\n        .each(function(d) {\n          if (d !== dimension) return;\n          select(this).call(brushes[d].move, null);\n          if (typeof brushes[d].type === 'function') {\n            brushes[d].event(select(this));\n          }\n        });\n      pc.renderBrushed();\n    }\n  }\n  return this;\n};\n\nexport default brushReset;\n", "const uninstall = (state, pc) => () => {\n  if (pc.g() !== undefined && pc.g() !== null)\n    pc.g()\n      .selectAll('.brush')\n      .remove();\n\n  state.brushes = {};\n  delete pc.brushExtents;\n  delete pc.brushReset;\n};\n\nexport default uninstall;\n", "import { select } from 'd3-selection';\n\nconst drawBrushes = (brushes, config, pc, axis, selector) => {\n  const brushSelection = selector.selectAll('.brush').data(brushes, d => d.id);\n\n  brushSelection\n    .enter()\n    .insert('g', '.brush')\n    .attr('class', 'brush')\n    .attr('dimension', axis)\n    .attr(\n      'id',\n      b => 'brush-' + Object.keys(config.dimensions).indexOf(axis) + '-' + b.id\n    )\n    .each(function(brushObject) {\n      brushObject.brush(select(this));\n    });\n\n  brushSelection.each(function(brushObject) {\n    select(this)\n      .attr('class', 'brush')\n      .selectAll('.overlay')\n      .style('pointer-events', function() {\n        const brush = brushObject.brush;\n        if (brushObject.id === brushes.length - 1 && brush !== undefined) {\n          return 'all';\n        } else {\n          return 'none';\n        }\n      });\n  });\n\n  brushSelection.exit().remove();\n};\n\nexport default drawBrushes;\n", "import { brushSelection } from 'd3-brush';\n\n// data within extents\nconst selected = (state, config, pc, events, brushGroup) => {\n  const { brushes } = state;\n\n  const is_brushed = (p, pos) => {\n    const axisBrushes = brushes[p];\n\n    for (let i = 0; i < axisBrushes.length; i++) {\n      const brush = document.getElementById('brush-' + pos + '-' + i);\n\n      if (brush && brushSelection(brush) !== null) {\n        return true;\n      }\n    }\n\n    return false;\n  };\n\n  const actives = Object.keys(config.dimensions).filter(is_brushed);\n  const extents = actives.map(p => {\n    const axisBrushes = brushes[p];\n\n    return axisBrushes\n      .filter(d => !pc.hideAxis().includes(d))\n      .map((d, i) =>\n        brushSelection(\n          document.getElementById(\n            'brush-' + Object.keys(config.dimensions).indexOf(p) + '-' + i\n          )\n        )\n      )\n      .map((d, i) => {\n        if (d === null || d === undefined) {\n          return null;\n        } else if (typeof config.dimensions[p].yscale.invert === 'function') {\n          return [\n            config.dimensions[p].yscale.invert(d[1]),\n            config.dimensions[p].yscale.invert(d[0]),\n          ];\n        } else {\n          return d;\n        }\n      });\n  });\n\n  // We don't want to return the full data set when there are no axes brushed.\n  // Actually, when there are no axes brushed, by definition, no items are\n  // selected. So, let's avoid the filtering and just return false.\n  //if (actives.length === 0) return false;\n\n  // Resolves broken examples for now. They expect to get the full dataset back from empty brushes\n  if (actives.length === 0) return config.data;\n\n  // test if within range\n  const within = {\n    date: (d, p, i) => {\n      const dimExt = extents[i];\n\n      if (typeof config.dimensions[p].yscale.bandwidth === 'function') {\n        // if it is ordinal\n        for (const e of dimExt) {\n          if (e === null || e === undefined) {\n            continue;\n          }\n\n          if (\n            e[0] <= config.dimensions[p].yscale(d[p]) &&\n            config.dimensions[p].yscale(d[p]) <= e[1]\n          ) {\n            return true;\n          }\n        }\n\n        return false;\n      } else {\n        for (const e of dimExt) {\n          if (e === null || e === undefined) {\n            continue;\n          }\n\n          if (e[0] <= d[p] && d[p] <= e[1]) {\n            return true;\n          }\n        }\n\n        return false;\n      }\n    },\n    number: (d, p, i) => {\n      const dimExt = extents[i];\n\n      if (typeof config.dimensions[p].yscale.bandwidth === 'function') {\n        // if it is ordinal\n        for (const e of dimExt) {\n          if (e === null || e === undefined) {\n            continue;\n          }\n\n          if (\n            e[0] <= config.dimensions[p].yscale(d[p]) &&\n            config.dimensions[p].yscale(d[p]) <= e[1]\n          ) {\n            return true;\n          }\n        }\n\n        return false;\n      } else {\n        for (const e of dimExt) {\n          if (e === null || e === undefined) {\n            continue;\n          }\n\n          if (e[0] <= d[p] && d[p] <= e[1]) {\n            return true;\n          }\n        }\n\n        return false;\n      }\n    },\n    string: (d, p, i) => {\n      const dimExt = extents[i];\n\n      for (const e of dimExt) {\n        if (e === null || e === undefined) {\n          continue;\n        }\n\n        if (\n          e[0] <= config.dimensions[p].yscale(d[p]) &&\n          config.dimensions[p].yscale(d[p]) <= e[1]\n        ) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n  };\n\n  return config.data.filter(d => {\n    switch (brushGroup.predicate) {\n      case 'AND':\n        return actives.every((p, i) =>\n          within[config.dimensions[p].type](d, p, i)\n        );\n      case 'OR':\n        return actives.some((p, i) =>\n          within[config.dimensions[p].type](d, p, i)\n        );\n      default:\n        throw new Error('Unknown brush predicate ' + config.brushPredicate);\n    }\n  });\n};\n\nexport default selected;\n", "import { brushY, brushSelection } from 'd3-brush';\nimport { event, select } from 'd3-selection';\nimport drawBrushes from './drawBrushes';\nimport selected from './selected';\n\nconst brushUpdated = (config, pc, events) => newSelection => {\n  config.brushed = newSelection;\n  events.call('brush', pc, config.brushed);\n  pc.renderBrushed();\n};\n\nconst newBrush = (state, config, pc, events, brushGroup) => (\n  axis,\n  _selector\n) => {\n  const { brushes, brushNodes } = state;\n\n  const brushRangeMax =\n    config.dimensions[axis].type === 'string'\n      ? config.dimensions[axis].yscale.range()[\n          config.dimensions[axis].yscale.range().length - 1\n        ]\n      : config.dimensions[axis].yscale.range()[0];\n\n  const brush = brushY().extent([[-15, 0], [15, brushRangeMax]]);\n  const id = brushes[axis] ? brushes[axis].length : 0;\n  const node =\n    'brush-' + Object.keys(config.dimensions).indexOf(axis) + '-' + id;\n\n  if (brushes[axis]) {\n    brushes[axis].push({\n      id,\n      brush,\n      node,\n    });\n  } else {\n    brushes[axis] = [{ id, brush, node }];\n  }\n\n  if (brushNodes[axis]) {\n    brushNodes[axis].push({ id, node });\n  } else {\n    brushNodes[axis] = [{ id, node }];\n  }\n\n  brush\n    .on('start', function() {\n      if (event.sourceEvent !== null) {\n        events.call('brushstart', pc, config.brushed);\n        if (typeof event.sourceEvent.stopPropagation === 'function') {\n          event.sourceEvent.stopPropagation();\n        }\n      }\n    })\n    .on('brush', function(e) {\n      // record selections\n      brushUpdated(\n        config,\n        pc,\n        events\n      )(selected(state, config, pc, events, brushGroup));\n    })\n    .on('end', function() {\n      // Figure out if our latest brush has a selection\n      const lastBrushID = brushes[axis][brushes[axis].length - 1].id;\n      const lastBrush = document.getElementById(\n        'brush-' +\n          Object.keys(config.dimensions).indexOf(axis) +\n          '-' +\n          lastBrushID\n      );\n      const selection = brushSelection(lastBrush);\n\n      if (\n        selection !== undefined &&\n        selection !== null &&\n        selection[0] !== selection[1]\n      ) {\n        newBrush(state, config, pc, events, brushGroup)(axis, _selector);\n\n        drawBrushes(brushes[axis], config, pc, axis, _selector);\n\n        brushUpdated(config, pc, events)(\n          selected(state, config, pc, events, brushGroup)\n        );\n      } else {\n        if (\n          event.sourceEvent &&\n          event.sourceEvent.toString() === '[object MouseEvent]' &&\n          event.selection === null\n        ) {\n          pc.brushReset(axis);\n        }\n      }\n\n      events.call('brushend', pc, config.brushed);\n    });\n\n  return brush;\n};\n\nexport default newBrush;\n", "import { select } from 'd3-selection';\nimport { brushSelection } from 'd3-brush';\nimport newBrush from './newBrush';\nimport drawBrushes from './drawBrushes';\nimport invertByScale from '../invertByScale';\n\n/**\n *\n * extents are in format of [[2,6], [3,5]]\n *\n * * @param state\n * @param config\n * @param pc\n * @returns {Function}\n */\nconst brushExtents = (state, config, pc, events, brushGroup) => extents => {\n  const { brushes } = state;\n  const hiddenAxes = pc.hideAxis();\n\n  if (typeof extents === 'undefined') {\n    return Object.keys(config.dimensions)\n      .filter(d => !hiddenAxes.includes(d))\n      .reduce((acc, cur, pos) => {\n        const axisBrushes = brushes[cur];\n\n        if (axisBrushes === undefined || axisBrushes === null) {\n          acc[cur] = [];\n        } else {\n          acc[cur] = axisBrushes.reduce((d, p, i) => {\n            const raw = brushSelection(\n              document.getElementById('brush-' + pos + '-' + i)\n            );\n\n            if (raw) {\n              const yScale = config.dimensions[cur].yscale;\n              const scaled = invertByScale(raw, yScale);\n\n              d.push({\n                extent: p.brush.extent(),\n                selection: {\n                  raw,\n                  scaled,\n                },\n              });\n            }\n            return d;\n          }, []);\n        }\n\n        return acc;\n      }, {});\n  } else {\n    // //first get all the brush selections\n    // loop over each dimension and update appropriately (if it was passed in through extents)\n    Object.keys(config.dimensions).forEach((d, pos) => {\n      if (extents[d] === undefined || extents[d] === null) {\n        return;\n      }\n\n      const dim = config.dimensions[d];\n\n      const yExtents = extents[d].map(e => e.map(dim.yscale));\n\n      const _bs = yExtents.map((e, j) => {\n        const _brush = newBrush(state, config, pc, events, brushGroup)(\n          d,\n          select('#brush-group-' + pos)\n        );\n        //update the extent\n        //sets the brushable extent to the specified array of points [[x0, y0], [x1, y1]]\n        _brush.extent([[-15, e[1]], [15, e[0]]]);\n\n        return {\n          id: j,\n          brush: _brush,\n          ext: e,\n        };\n      });\n\n      brushes[d] = _bs;\n\n      drawBrushes(_bs, config, pc, d, select('#brush-group-' + pos));\n\n      //redraw the brush\n      //https://github.com/d3/d3-brush#brush_move\n      // For an x-brush, it must be defined as [x0, x1]; for a y-brush, it must be defined as [y0, y1].\n      _bs.forEach((f, k) => {\n        select('#brush-' + pos + '-' + k)\n          .call(f.brush)\n          .call(f.brush.move, f.ext.reverse());\n      });\n    });\n\n    //redraw the chart\n    pc.renderBrushed();\n\n    return pc;\n  }\n};\n\nexport default brushExtents;\n", "import { select } from 'd3-selection';\nimport brushExtents from './brushExtents';\nimport brushReset from './brushReset';\nimport brushFor from './brushFor';\n\nconst install = (state, config, pc, events, brushGroup) => () => {\n  if (!pc.g()) {\n    pc.createAxes();\n  }\n\n  const hiddenAxes = pc.hideAxis();\n\n  pc.g()\n    .append('svg:g')\n    .attr('id', (d, i) => 'brush-group-' + i)\n    .attr('class', 'brush-group')\n    .attr('dimension', d => d)\n    .each(function(d) {\n      if (!hiddenAxes.includes(d)) {\n        brushFor(state, config, pc, events, brushGroup)(d, select(this));\n      }\n    });\n\n  pc.brushExtents = brushExtents(state, config, pc, events, brushGroup);\n  pc.brushReset = brushReset(state, config, pc);\n  return pc;\n};\n\nexport default install;\n", "import newBrush from './newBrush';\nimport drawBrushes from './drawBrushes';\n\nconst brushFor = (state, config, pc, events, brushGroup) => (\n  axis,\n  _selector\n) => {\n  const { brushes } = state;\n  newBrush(state, config, pc, events, brushGroup)(axis, _selector);\n  drawBrushes(brushes[axis], config, pc, axis, _selector);\n};\n\nexport default brushFor;\n", "import { select } from 'd3-selection';\nimport { brushSelection } from 'd3-brush';\n\nconst brushReset = (state, config, pc) => dimension => {\n  const { brushes } = state;\n\n  if (dimension === undefined) {\n    if (pc.g() !== undefined && pc.g() !== null) {\n      Object.keys(config.dimensions).forEach((d, pos) => {\n        const axisBrush = brushes[d];\n\n        // hidden axes will be undefined\n        if (axisBrush) {\n          axisBrush.forEach((e, i) => {\n            const brush = document.getElementById('brush-' + pos + '-' + i);\n            if (brush && brushSelection(brush) !== null) {\n              pc.g()\n                .select('#brush-' + pos + '-' + i)\n                .call(e.brush.move, null);\n            }\n          });\n        }\n      });\n\n      pc.renderBrushed();\n    }\n  } else {\n    if (pc.g() !== undefined && pc.g() !== null) {\n      const axisBrush = brushes[dimension];\n      const pos = Object.keys(config.dimensions).indexOf(dimension);\n\n      if (axisBrush) {\n        axisBrush.forEach((e, i) => {\n          const brush = document.getElementById('brush-' + pos + '-' + i);\n          if (brushSelection(brush) !== null) {\n            pc.g()\n              .select('#brush-' + pos + '-' + i)\n              .call(e.brush.move, null);\n\n            if (typeof e.event === 'function') {\n              e.event(select('#brush-' + pos + '-' + i));\n            }\n          }\n        });\n      }\n\n      pc.renderBrushed();\n    }\n  }\n  return this;\n};\n\nexport default brushReset;\n", "const uninstall = (state, pc) => () => {\n  if (pc.g() !== undefined && pc.g() !== null)\n    pc.g()\n      .selectAll('.brush-group')\n      .remove();\n\n  state.brushes = {};\n  delete pc.brushExtents;\n  delete pc.brushReset;\n};\n\nexport default uninstall;\n", "const uninstall = (state, pc) => () => {\n  pc.selection\n    .select('svg')\n    .select('g#strums')\n    .remove();\n  pc.selection\n    .select('svg')\n    .select('rect#strum-events')\n    .remove();\n  pc.on('axesreorder.strums', undefined);\n  delete pc.brushReset;\n\n  state.strumRect = undefined;\n};\n\nexport default uninstall;\n", "// test if point falls between lines\nconst containmentTest = (strum, width) => p => {\n  const p1 = [strum.p1[0] - strum.minX, strum.p1[1] - strum.minX],\n    p2 = [strum.p2[0] - strum.minX, strum.p2[1] - strum.minX],\n    m1 = 1 - width / p1[0],\n    b1 = p1[1] * (1 - m1),\n    m2 = 1 - width / p2[0],\n    b2 = p2[1] * (1 - m2);\n\n  const x = p[0],\n    y = p[1],\n    y1 = m1 * x + b1,\n    y2 = m2 * x + b2;\n\n  return y > Math.min(y1, y2) && y < Math.max(y1, y2);\n};\n\nconst crossesStrum = (state, config) => (d, id) => {\n  let strum = state.strums[id],\n    test = containmentTest(strum, state.strums.width(id)),\n    d1 = strum.dims.left,\n    d2 = strum.dims.right,\n    y1 = config.dimensions[d1].yscale,\n    y2 = config.dimensions[d2].yscale,\n    point = [y1(d[d1]) - strum.minX, y2(d[d2]) - strum.minX];\n  return test(point);\n};\n\nconst selected = (brushGroup, state, config) => {\n  // Get the ids of the currently active strums.\n  const ids = Object.getOwnPropertyNames(state.strums).filter(d => !isNaN(d)),\n    brushed = config.data;\n\n  if (ids.length === 0) {\n    return brushed;\n  }\n\n  const crossTest = crossesStrum(state, config);\n\n  return brushed.filter(d => {\n    switch (brushGroup.predicate) {\n      case 'AND':\n        return ids.every(id => crossTest(d, id));\n      case 'OR':\n        return ids.some(id => crossTest(d, id));\n      default:\n        throw new Error('Unknown brush predicate ' + config.brushPredicate);\n    }\n  });\n};\n\nexport default selected;\n", "const removeStrum = (state, pc) => {\n  const strum = state.strums[state.strums.active],\n    svg = pc.selection.select('svg').select('g#strums');\n\n  delete state.strums[state.strums.active];\n  svg.selectAll('line#strum-' + strum.dims.i).remove();\n  svg.selectAll('circle#strum-' + strum.dims.i).remove();\n};\n\nexport default removeStrum;\n", "import selected from './selected';\nimport removeStrum from './removeStrum';\n\nconst onDragEnd = (brushGroup, state, config, pc, events) => () => {\n  const strum = state.strums[state.strums.active];\n\n  // Okay, somewhat unexpected, but not totally unsurprising, a mousclick is\n  // considered a drag without move. So we have to deal with that case\n  if (strum && strum.p1[0] === strum.p2[0] && strum.p1[1] === strum.p2[1]) {\n    removeStrum(state, pc);\n  }\n\n  const brushed = selected(brushGroup, state, config);\n  state.strums.active = undefined;\n  config.brushed = brushed;\n  pc.renderBrushed();\n  events.call('brushend', pc, config.brushed);\n};\n\nexport default onDragEnd;\n", "import { event, select } from 'd3-selection';\nimport { drag } from 'd3-drag';\nimport onDragEnd from './onDragEnd';\n\nconst drawStrum = (\n  brushGroup,\n  state,\n  config,\n  pc,\n  events,\n  strum,\n  activePoint\n) => {\n  let _svg = pc.selection.select('svg').select('g#strums'),\n    id = strum.dims.i,\n    points = [strum.p1, strum.p2],\n    _line = _svg.selectAll('line#strum-' + id).data([strum]),\n    circles = _svg.selectAll('circle#strum-' + id).data(points),\n    _drag = drag();\n\n  _line\n    .enter()\n    .append('line')\n    .attr('id', 'strum-' + id)\n    .attr('class', 'strum');\n\n  _line\n    .attr('x1', d => d.p1[0])\n    .attr('y1', d => d.p1[1])\n    .attr('x2', d => d.p2[0])\n    .attr('y2', d => d.p2[1])\n    .attr('stroke', 'black')\n    .attr('stroke-width', 2);\n\n  _drag\n    .on('drag', function(d, i) {\n      const ev = event;\n      i = i + 1;\n      strum['p' + i][0] = Math.min(Math.max(strum.minX + 1, ev.x), strum.maxX);\n      strum['p' + i][1] = Math.min(Math.max(strum.minY, ev.y), strum.maxY);\n      drawStrum(brushGroup, state, config, pc, events, strum, i - 1);\n    })\n    .on('end', onDragEnd(brushGroup, state, config, pc, events));\n\n  circles\n    .enter()\n    .append('circle')\n    .attr('id', 'strum-' + id)\n    .attr('class', 'strum');\n\n  circles\n    .attr('cx', d => d[0])\n    .attr('cy', d => d[1])\n    .attr('r', 5)\n    .style(\n      'opacity',\n      (d, i) => (activePoint !== undefined && i === activePoint ? 0.8 : 0)\n    )\n    .on('mouseover', function() {\n      select(this).style('opacity', 0.8);\n    })\n    .on('mouseout', function() {\n      select(this).style('opacity', 0);\n    })\n    .call(_drag);\n};\n\nconst onDrag = (brushGroup, state, config, pc, events) => () => {\n  const ev = event,\n    strum = state.strums[state.strums.active];\n\n  // Make sure that the point is within the bounds\n  strum.p2[0] = Math.min(\n    Math.max(strum.minX + 1, ev.x - config.margin.left),\n    strum.maxX\n  );\n  strum.p2[1] = Math.min(\n    Math.max(strum.minY, ev.y - config.margin.top),\n    strum.maxY\n  );\n\n  drawStrum(brushGroup, state, config, pc, events, strum, 1);\n};\n\nexport default onDrag;\n", "const h = config => config.height - config.margin.top - config.margin.bottom;\n\nexport default h;\n", "const dimensionsForPoint = (config, pc, xscale, p) => {\n  const dims = { i: -1, left: undefined, right: undefined };\n  Object.keys(config.dimensions).some((dim, i) => {\n    if (xscale(dim) < p[0]) {\n      dims.i = i;\n      dims.left = dim;\n      dims.right = Object.keys(config.dimensions)[\n        pc.getOrderedDimensionKeys().indexOf(dim) + 1\n      ];\n      return false;\n    }\n    return true;\n  });\n\n  if (dims.left === undefined) {\n    // Event on the left side of the first axis.\n    dims.i = 0;\n    dims.left = pc.getOrderedDimensionKeys()[0];\n    dims.right = pc.getOrderedDimensionKeys()[1];\n  } else if (dims.right === undefined) {\n    // Event on the right side of the last axis\n    dims.i = Object.keys(config.dimensions).length - 1;\n    dims.right = dims.left;\n    dims.left = pc.getOrderedDimensionKeys()[\n      Object.keys(config.dimensions).length - 2\n    ];\n  }\n\n  return dims;\n};\n\nexport default dimensionsForPoint;\n", "// Checks if the first dimension is directly left of the second dimension.\nconst consecutive = dimensions => (first, second) => {\n  const keys = Object.keys(dimensions);\n\n  return keys.some(\n    (d, i) =>\n      d === first ? i + i < keys.length && dimensions[i + 1] === second : false\n  );\n};\n\nexport default consecutive;\n", "import { drag } from 'd3-drag';\nimport onDragEnd from './onDragEnd';\nimport onDrag from './onDrag';\nimport onDragStart from './onDragStart';\nimport removeStrum from './removeStrum';\nimport brushReset from './brushReset';\nimport w from '../../util/width';\nimport h from '../../util/height';\nimport consecutive from '../consecutive';\n\nconst install = (brushGroup, state, config, pc, events, xscale) => () => {\n  if (pc.g() === undefined || pc.g() === null) {\n    pc.createAxes();\n  }\n\n  const _drag = drag();\n\n  // Map of current strums. Strums are stored per segment of the PC. A segment,\n  // being the area between two axes. The left most area is indexed at 0.\n  state.strums.active = undefined;\n  // Returns the width of the PC segment where currently a strum is being\n  // placed. NOTE: even though they are evenly spaced in our current\n  // implementation, we keep for when non-even spaced segments are supported as\n  // well.\n  state.strums.width = id =>\n    state.strums[id] === undefined\n      ? undefined\n      : state.strums[id].maxX - state.strums[id].minX;\n\n  pc.on('axesreorder.strums', () => {\n    const ids = Object.getOwnPropertyNames(state.strums).filter(d => !isNaN(d));\n\n    if (ids.length > 0) {\n      // We have some strums, which might need to be removed.\n      ids.forEach(d => {\n        const dims = state.strums[d].dims;\n        state.strums.active = d;\n        // If the two dimensions of the current strum are not next to each other\n        // any more, than we'll need to remove the strum. Otherwise we keep it.\n        if (!consecutive(config.dimensions)(dims.left, dims.right)) {\n          removeStrum(state, pc);\n        }\n      });\n      onDragEnd(brushGroup, state, config, pc, events)();\n    }\n  });\n\n  // Add a new svg group in which we draw the strums.\n  pc.selection\n    .select('svg')\n    .append('g')\n    .attr('id', 'strums')\n    .attr(\n      'transform',\n      'translate(' + config.margin.left + ',' + config.margin.top + ')'\n    );\n\n  // Install the required brushReset function\n  pc.brushReset = brushReset(brushGroup, state, config, pc, events);\n\n  _drag\n    .on('start', onDragStart(state, config, pc, xscale))\n    .on('drag', onDrag(brushGroup, state, config, pc, events))\n    .on('end', onDragEnd(brushGroup, state, config, pc, events));\n\n  // NOTE: The styling needs to be done here and not in the css. This is because\n  //       for 1D brushing, the canvas layers should not listen to\n  //       pointer-events._.\n  state.strumRect = pc.selection\n    .select('svg')\n    .insert('rect', 'g#strums')\n    .attr('id', 'strum-events')\n    .attr('x', config.margin.left)\n    .attr('y', config.margin.top)\n    .attr('width', w(config))\n    .attr('height', h(config) + 2)\n    .style('opacity', 0)\n    .call(_drag);\n};\n\nexport default install;\n", "import onDragEnd from './onDragEnd';\nimport removeStrum from './removeStrum';\n\nconst brushReset = (brushGroup, state, config, pc, events) => () => {\n  const ids = Object.getOwnPropertyNames(state.strums).filter(d => !isNaN(d));\n\n  ids.forEach(d => {\n    state.strums.active = d;\n    removeStrum(state, pc);\n  });\n  onDragEnd(brushGroup, state, config, pc, events)();\n};\n\nexport default brushReset;\n", "import { mouse } from 'd3-selection';\nimport h from '../../util/height';\nimport dimensionsForPoint from '../dimensionsForPoint';\n\n// First we need to determine between which two axes the sturm was started.\n// This will determine the freedom of movement, because a strum can\n// logically only happen between two axes, so no movement outside these axes\n// should be allowed.\nconst onDragStart = (state, config, pc, xscale) => () => {\n  let p = mouse(state.strumRect.node());\n\n  p[0] = p[0] - config.margin.left;\n  p[1] = p[1] - config.margin.top;\n\n  const dims = dimensionsForPoint(config, pc, xscale, p);\n  const strum = {\n    p1: p,\n    dims: dims,\n    minX: xscale(dims.left),\n    maxX: xscale(dims.right),\n    minY: 0,\n    maxY: h(config),\n  };\n\n  // Make sure that the point is within the bounds\n  strum.p1[0] = Math.min(Math.max(strum.minX, p[0]), strum.maxX);\n  strum.p2 = strum.p1.slice();\n\n  state.strums[dims.i] = strum;\n  state.strums.active = dims.i;\n};\n\nexport default onDragStart;\n", "const uninstall = (state, pc) => () => {\n  pc.selection\n    .select('svg')\n    .select('g#arcs')\n    .remove();\n  pc.selection\n    .select('svg')\n    .select('rect#arc-events')\n    .remove();\n  pc.on('axesreorder.arcs', undefined);\n\n  delete pc.brushReset;\n\n  state.strumRect = undefined;\n};\n\nexport default uninstall;\n", "const hypothenuse = (a, b) => Math.sqrt(a * a + b * b);\n\nexport default hypothenuse;\n", "import hypothenuse from './util/hypothenuse';\n\n// [0, 2*PI] -> [-PI/2, PI/2]\nconst signedAngle = angle =>\n  angle > Math.PI ? 1.5 * Math.PI - angle : 0.5 * Math.PI - angle;\n\n/**\n * angles are stored in radians from in [0, 2*PI], where 0 in 12 o'clock.\n * However, one can only select lines from 0 to PI, so we compute the\n * 'signed' angle, where 0 is the horizontal line (3 o'clock), and +/- PI/2\n * are 12 and 6 o'clock respectively.\n */\nconst containmentTest = arc => a => {\n  let startAngle = signedAngle(arc.startAngle);\n  let endAngle = signedAngle(arc.endAngle);\n\n  if (startAngle > endAngle) {\n    const tmp = startAngle;\n    startAngle = endAngle;\n    endAngle = tmp;\n  }\n\n  // test if segment angle is contained in angle interval\n  return a >= startAngle && a <= endAngle;\n};\n\nconst crossesStrum = (state, config) => (d, id) => {\n  const arc = state.arcs[id],\n    test = containmentTest(arc),\n    d1 = arc.dims.left,\n    d2 = arc.dims.right,\n    y1 = config.dimensions[d1].yscale,\n    y2 = config.dimensions[d2].yscale,\n    a = state.arcs.width(id),\n    b = y1(d[d1]) - y2(d[d2]),\n    c = hypothenuse(a, b),\n    angle = Math.asin(b / c); // rad in [-PI/2, PI/2]\n  return test(angle);\n};\n\nconst selected = (brushGroup, state, config) => {\n  const ids = Object.getOwnPropertyNames(state.arcs).filter(d => !isNaN(d));\n  const brushed = config.data;\n\n  if (ids.length === 0) {\n    return brushed;\n  }\n\n  const crossTest = crossesStrum(state, config);\n\n  return brushed.filter(d => {\n    switch (brushGroup.predicate) {\n      case 'AND':\n        return ids.every(id => crossTest(d, id));\n      case 'OR':\n        return ids.some(id => crossTest(d, id));\n      default:\n        throw new Error('Unknown brush predicate ' + config.brushPredicate);\n    }\n  });\n};\n\nexport default selected;\n", "const removeStrum = (state, pc) => {\n  const arc = state.arcs[state.arcs.active],\n    svg = pc.selection.select('svg').select('g#arcs');\n\n  delete state.arcs[state.arcs.active];\n  state.arcs.active = undefined;\n  svg.selectAll('line#arc-' + arc.dims.i).remove();\n  svg.selectAll('circle#arc-' + arc.dims.i).remove();\n  svg.selectAll('path#arc-' + arc.dims.i).remove();\n};\n\nexport default removeStrum;\n", "import selected from './selected';\nimport removeStrum from './removeStrum';\n\nconst onDragEnd = (brushGroup, state, config, pc, events) => () => {\n  const arc = state.arcs[state.arcs.active];\n\n  // Okay, somewhat unexpected, but not totally unsurprising, a mousclick is\n  // considered a drag without move. So we have to deal with that case\n  if (arc && arc.p1[0] === arc.p2[0] && arc.p1[1] === arc.p2[1]) {\n    removeStrum(state, pc);\n  }\n\n  if (arc) {\n    const angle = state.arcs.startAngle(state.arcs.active);\n\n    arc.startAngle = angle;\n    arc.endAngle = angle;\n    arc.arc\n      .outerRadius(state.arcs.length(state.arcs.active))\n      .startAngle(angle)\n      .endAngle(angle);\n  }\n\n  state.arcs.active = undefined;\n  config.brushed = selected(brushGroup, state, config);\n  pc.renderBrushed();\n  events.call('brushend', pc, config.brushed);\n};\n\nexport default onDragEnd;\n", "import { event, select } from 'd3-selection';\nimport { drag } from 'd3-drag';\nimport onDragEnd from './onDragEnd';\n\nconst drawStrum = (brushGroup, state, config, pc, events, arc, activePoint) => {\n  const svg = pc.selection.select('svg').select('g#arcs'),\n    id = arc.dims.i,\n    points = [arc.p2, arc.p3],\n    _line = svg\n      .selectAll('line#arc-' + id)\n      .data([{ p1: arc.p1, p2: arc.p2 }, { p1: arc.p1, p2: arc.p3 }]),\n    circles = svg.selectAll('circle#arc-' + id).data(points),\n    _drag = drag(),\n    _path = svg.selectAll('path#arc-' + id).data([arc]);\n\n  _path\n    .enter()\n    .append('path')\n    .attr('id', 'arc-' + id)\n    .attr('class', 'arc')\n    .style('fill', 'orange')\n    .style('opacity', 0.5);\n\n  _path\n    .attr('d', arc.arc)\n    .attr('transform', 'translate(' + arc.p1[0] + ',' + arc.p1[1] + ')');\n\n  _line\n    .enter()\n    .append('line')\n    .attr('id', 'arc-' + id)\n    .attr('class', 'arc');\n\n  _line\n    .attr('x1', d => d.p1[0])\n    .attr('y1', d => d.p1[1])\n    .attr('x2', d => d.p2[0])\n    .attr('y2', d => d.p2[1])\n    .attr('stroke', 'black')\n    .attr('stroke-width', 2);\n\n  _drag\n    .on('drag', (d, i) => {\n      const ev = event;\n      i = i + 2;\n\n      arc['p' + i][0] = Math.min(Math.max(arc.minX + 1, ev.x), arc.maxX);\n      arc['p' + i][1] = Math.min(Math.max(arc.minY, ev.y), arc.maxY);\n\n      const angle =\n        i === 3 ? state.arcs.startAngle(id) : state.arcs.endAngle(id);\n\n      if (\n        (arc.startAngle < Math.PI &&\n          arc.endAngle < Math.PI &&\n          angle < Math.PI) ||\n        (arc.startAngle >= Math.PI &&\n          arc.endAngle >= Math.PI &&\n          angle >= Math.PI)\n      ) {\n        if (i === 2) {\n          arc.endAngle = angle;\n          arc.arc.endAngle(angle);\n        } else if (i === 3) {\n          arc.startAngle = angle;\n          arc.arc.startAngle(angle);\n        }\n      }\n\n      drawStrum(brushGroup, state, config, pc, events, arc, i - 2);\n    })\n    .on('end', onDragEnd(brushGroup, state, config, pc, events));\n\n  circles\n    .enter()\n    .append('circle')\n    .attr('id', 'arc-' + id)\n    .attr('class', 'arc');\n\n  circles\n    .attr('cx', d => d[0])\n    .attr('cy', d => d[1])\n    .attr('r', 5)\n    .style(\n      'opacity',\n      (d, i) => (activePoint !== undefined && i === activePoint ? 0.8 : 0)\n    )\n    .on('mouseover', function() {\n      select(this).style('opacity', 0.8);\n    })\n    .on('mouseout', function() {\n      select(this).style('opacity', 0);\n    })\n    .call(_drag);\n};\n\nconst onDrag = (brushGroup, state, config, pc, events) => () => {\n  const ev = event,\n    arc = state.arcs[state.arcs.active];\n\n  // Make sure that the point is within the bounds\n  arc.p2[0] = Math.min(\n    Math.max(arc.minX + 1, ev.x - config.margin.left),\n    arc.maxX\n  );\n  arc.p2[1] = Math.min(Math.max(arc.minY, ev.y - config.margin.top), arc.maxY);\n  arc.p3 = arc.p2.slice();\n  drawStrum(brushGroup, state, config, pc, events, arc, 1);\n};\n\nexport default onDrag;\n", "import { drag } from 'd3-drag';\nimport onDragEnd from './onDragEnd';\nimport onDrag from './onDrag';\nimport onDragStart from './onDragStart';\nimport removeStrum from './removeStrum';\nimport brushReset from './brushReset';\nimport w from '../../util/width';\nimport h from '../../util/height';\n\nimport hypothenuse from './util/hypothenuse';\nimport consecutive from '../consecutive';\n\n// returns angles in [-PI/2, PI/2]\nconst angle = (p1, p2) => {\n  const a = p1[0] - p2[0],\n    b = p1[1] - p2[1],\n    c = hypothenuse(a, b);\n\n  return Math.asin(b / c);\n};\n\nconst endAngle = state => id => {\n  const arc = state.arcs[id];\n  if (arc === undefined) {\n    return undefined;\n  }\n  let sAngle = angle(arc.p1, arc.p2),\n    uAngle = -sAngle + Math.PI / 2;\n\n  if (arc.p1[0] > arc.p2[0]) {\n    uAngle = 2 * Math.PI - uAngle;\n  }\n\n  return uAngle;\n};\n\nconst startAngle = state => id => {\n  const arc = state.arcs[id];\n  if (arc === undefined) {\n    return undefined;\n  }\n\n  let sAngle = angle(arc.p1, arc.p3),\n    uAngle = -sAngle + Math.PI / 2;\n\n  if (arc.p1[0] > arc.p3[0]) {\n    uAngle = 2 * Math.PI - uAngle;\n  }\n\n  return uAngle;\n};\n\nconst length = state => id => {\n  const arc = state.arcs[id];\n\n  if (arc === undefined) {\n    return undefined;\n  }\n\n  const a = arc.p1[0] - arc.p2[0],\n    b = arc.p1[1] - arc.p2[1];\n\n  return hypothenuse(a, b);\n};\n\nconst install = (brushGroup, state, config, pc, events, xscale) => () => {\n  if (!pc.g()) {\n    pc.createAxes();\n  }\n\n  const _drag = drag();\n\n  // Map of current arcs. arcs are stored per segment of the PC. A segment,\n  // being the area between two axes. The left most area is indexed at 0.\n  state.arcs.active = undefined;\n  // Returns the width of the PC segment where currently a arc is being\n  // placed. NOTE: even though they are evenly spaced in our current\n  // implementation, we keep for when non-even spaced segments are supported as\n  // well.\n  state.arcs.width = id => {\n    const arc = state.arcs[id];\n    return arc === undefined ? undefined : arc.maxX - arc.minX;\n  };\n\n  // returns angles in [0, 2 * PI]\n  state.arcs.endAngle = endAngle(state);\n  state.arcs.startAngle = startAngle(state);\n  state.arcs.length = length(state);\n\n  pc.on('axesreorder.arcs', () => {\n    const ids = Object.getOwnPropertyNames(arcs).filter(d => !isNaN(d));\n\n    if (ids.length > 0) {\n      // We have some arcs, which might need to be removed.\n      ids.forEach(d => {\n        const dims = arcs[d].dims;\n        state.arcs.active = d;\n        // If the two dimensions of the current arc are not next to each other\n        // any more, than we'll need to remove the arc. Otherwise we keep it.\n        if (!consecutive(dims)(dims.left, dims.right)) {\n          removeStrum(state, pc);\n        }\n      });\n      onDragEnd(brushGroup, state, config, pc, events)();\n    }\n  });\n\n  // Add a new svg group in which we draw the arcs.\n  pc.selection\n    .select('svg')\n    .append('g')\n    .attr('id', 'arcs')\n    .attr(\n      'transform',\n      'translate(' + config.margin.left + ',' + config.margin.top + ')'\n    );\n\n  // Install the required brushReset function\n  pc.brushReset = brushReset(brushGroup, state, config, pc, events);\n\n  _drag\n    .on('start', onDragStart(state, config, pc, xscale))\n    .on('drag', onDrag(brushGroup, state, config, pc, events))\n    .on('end', onDragEnd(brushGroup, state, config, pc, events));\n\n  // NOTE: The styling needs to be done here and not in the css. This is because\n  //       for 1D brushing, the canvas layers should not listen to\n  //       pointer-events._.\n  state.strumRect = pc.selection\n    .select('svg')\n    .insert('rect', 'g#arcs')\n    .attr('id', 'arc-events')\n    .attr('x', config.margin.left)\n    .attr('y', config.margin.top)\n    .attr('width', w(config))\n    .attr('height', h(config) + 2)\n    .style('opacity', 0)\n    .call(_drag);\n};\n\nexport default install;\n", "import onDragEnd from './onDragEnd';\nimport removeStrum from './removeStrum';\n\nconst brushReset = (brushGroup, state, config, pc, events) => () => {\n  const ids = Object.getOwnPropertyNames(state.arcs).filter(d => !isNaN(d));\n\n  ids.forEach(d => {\n    state.arcs.active = d;\n    removeStrum(state, pc);\n  });\n  onDragEnd(brushGroup, state, config, pc, events)();\n};\n\nexport default brushReset;\n", "import { mouse } from 'd3-selection';\nimport { arc as d3Arc } from 'd3-shape';\nimport dimensionsForPoint from '../dimensionsForPoint';\nimport h from '../../util/height';\n\n// First we need to determine between which two axes the arc was started.\n// This will determine the freedom of movement, because a arc can\n// logically only happen between two axes, so no movement outside these axes\n// should be allowed.\nconst onDragStart = (state, config, pc, xscale) => () => {\n  const p = mouse(state.strumRect.node());\n\n  p[0] = p[0] - config.margin.left;\n  p[1] = p[1] - config.margin.top;\n\n  const dims = dimensionsForPoint(config, pc, xscale, p);\n  const arc = {\n    p1: p,\n    dims: dims,\n    minX: xscale(dims.left),\n    maxX: xscale(dims.right),\n    minY: 0,\n    maxY: h(config),\n    startAngle: undefined,\n    endAngle: undefined,\n    arc: d3Arc().innerRadius(0),\n  };\n\n  // Make sure that the point is within the bounds\n  arc.p1[0] = Math.min(Math.max(arc.minX, p[0]), arc.maxX);\n  arc.p2 = arc.p1.slice();\n  arc.p3 = arc.p1.slice();\n\n  state.arcs[dims.i] = arc;\n  state.arcs.active = dims.i;\n};\n\nexport default onDragStart;\n", "// calculate 2d intersection of line a->b with line c->d\n// points are objects with x and y properties\nconst intersection = (a, b, c, d) => {\n  return {\n    x:\n      ((a.x * b.y - a.y * b.x) * (c.x - d.x) -\n        (a.x - b.x) * (c.x * d.y - c.y * d.x)) /\n      ((a.x - b.x) * (c.y - d.y) - (a.y - b.y) * (c.x - d.x)),\n    y:\n      ((a.x * b.y - a.y * b.x) * (c.y - d.y) -\n        (a.y - b.y) * (c.x * d.y - c.y * d.x)) /\n      ((a.x - b.x) * (c.y - d.y) - (a.y - b.y) * (c.x - d.x)),\n  };\n};\n\nexport default intersection;\n", "const brushPredicate = (brushGroup, config, pc) => (predicate = null) => {\n  if (predicate === null) {\n    return brushGroup.predicate;\n  }\n\n  predicate = String(predicate).toUpperCase();\n  if (predicate !== 'AND' && predicate !== 'OR') {\n    throw new Error('Invalid predicate ' + predicate);\n  }\n\n  brushGroup.predicate = predicate;\n  config.brushed = brushGroup.currentMode().selected();\n  pc.renderBrushed();\n  return pc;\n};\n\nconst brushMode = (brushGroup, config, pc) => (mode = null) => {\n  if (mode === null) {\n    return brushGroup.mode;\n  }\n\n  if (pc.brushModes().indexOf(mode) === -1) {\n    throw new Error('pc.brushmode: Unsupported brush mode: ' + mode);\n  }\n\n  // Make sure that we don't trigger unnecessary events by checking if the mode\n  // actually changes.\n  if (mode !== brushGroup.mode) {\n    // When changing brush modes, the first thing we need to do is clearing any\n    // brushes from the current mode, if any.\n    if (brushGroup.mode !== 'None') {\n      pc.brushReset();\n    }\n\n    // Next, we need to 'uninstall' the current brushMode.\n    brushGroup.modes[brushGroup.mode].uninstall(pc);\n    // Finally, we can install the requested one.\n    brushGroup.mode = mode;\n    brushGroup.modes[brushGroup.mode].install();\n    if (mode === 'None') {\n      delete pc.brushPredicate;\n    } else {\n      pc.brushPredicate = brushPredicate(brushGroup, config, pc);\n    }\n  }\n\n  return pc;\n};\n\nexport default brushMode;\n", "/**\n * dimension display names\n *\n * @param config\n * @param d\n * @returns {*}\n */\nconst dimensionLabels = config => d =>\n  config.dimensions[d].title ? config.dimensions[d].title : d;\n\nexport default dimensionLabels;\n", "import { select, selectAll } from 'd3-selection';\n\nconst flipAxisAndUpdatePCP = (config, pc, axis) =>\n  function(dimension) {\n    pc.flip(dimension);\n    pc.brushReset(dimension);\n\n    // select(this.parentElement)\n    pc.selection\n      .select('svg')\n      .selectAll('g.axis')\n      .filter(d => d === dimension)\n      .transition()\n      .duration(config.animationTime)\n      .call(axis.scale(config.dimensions[dimension].yscale));\n    pc.render();\n  };\n\nexport default flipAxisAndUpdatePCP;\n", "import { event } from 'd3-selection';\n\nconst rotateLabels = (config, pc) => {\n  if (!config.rotateLabels) return;\n\n  let delta = event.deltaY;\n  delta = delta < 0 ? -5 : delta;\n  delta = delta > 0 ? 5 : delta;\n\n  config.dimensionTitleRotation += delta;\n  pc.svg\n    .selectAll('text.label')\n    .attr(\n      'transform',\n      'translate(0,-5) rotate(' + config.dimensionTitleRotation + ')'\n    );\n  event.preventDefault();\n};\n\nexport default rotateLabels;\n", "/** adjusts an axis' default range [h()+1, 1] if a NullValueSeparator is set */\nconst getRange = config => {\n  const h = config.height - config.margin.top - config.margin.bottom;\n\n  if (config.nullValueSeparator == 'bottom') {\n    return [\n      h +\n        1 -\n        config.nullValueSeparatorPadding.bottom -\n        config.nullValueSeparatorPadding.top,\n      1,\n    ];\n  } else if (config.nullValueSeparator == 'top') {\n    return [\n      h + 1,\n      1 +\n        config.nullValueSeparatorPadding.bottom +\n        config.nullValueSeparatorPadding.top,\n    ];\n  }\n  return [h + 1, 1];\n};\n\nexport default getRange;\n", "const isValid = d => d !== null && d !== undefined;\n\nconst applyDimensionDefaults = (config, pc) =>\n  function(dims) {\n    const types = pc.detectDimensionTypes(config.data);\n    dims = dims ? dims : Object.keys(types);\n\n    return dims.reduce((acc, cur, i) => {\n      const k = config.dimensions[cur] ? config.dimensions[cur] : {};\n      acc[cur] = {\n        ...k,\n        orient: isValid(k.orient) ? k.orient : 'left',\n        ticks: isValid(k.ticks) ? k.ticks : 5,\n        innerTickSize: isValid(k.innerTickSize) ? k.innerTickSize : 6,\n        outerTickSize: isValid(k.outerTickSize) ? k.outerTickSize : 0,\n        tickPadding: isValid(k.tickPadding) ? k.tickPadding : 3,\n        type: isValid(k.type) ? k.type : types[cur],\n        index: isValid(k.index) ? k.index : i,\n      };\n\n      return acc;\n    }, {});\n  };\n\nexport default applyDimensionDefaults;\n", "import { axisBottom, axisLeft, axisRight, axisTop } from 'd3-axis';\n\nconst applyAxisConfig = (axis, dimension) => {\n  let axisCfg;\n\n  switch (dimension.orient) {\n    case 'left':\n      axisCfg = axisLeft(dimension.yscale);\n      break;\n    case 'right':\n      axisCfg = axisRight(dimension.yscale);\n      break;\n    case 'top':\n      axisCfg = axisTop(dimension.yscale);\n      break;\n    case 'bottom':\n      axisCfg = axisBottom(dimension.yscale);\n      break;\n    default:\n      axisCfg = axisLeft(dimension.yscale);\n      break;\n  }\n\n  axisCfg\n    .ticks(dimension.ticks)\n    .tickValues(dimension.tickValues)\n    .tickSizeInner(dimension.innerTickSize)\n    .tickSizeOuter(dimension.outerTickSize)\n    .tickPadding(dimension.tickPadding)\n    .tickFormat(dimension.tickFormat);\n\n  return axisCfg;\n};\n\nexport default applyAxisConfig;\n", "const isBrushed = (config, brushGroup) => {\n  if (config.brushed && config.brushed.length !== config.data.length)\n    return true;\n\n  const object = brushGroup.currentMode().brushState();\n\n  for (let key in object) {\n    if (object.hasOwnProperty(key)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nexport default isBrushed;\n", "export const PRECISION = 1e-6;", "\"use strict\";\r\n\r\nimport { PRECISION } from \"./PRECISION\";\r\nimport { Vector } from \"./Vector\";\r\n\r\nexport class Matrix\r\n{\r\n    constructor (elements)\r\n    {\r\n        this.setElements(elements);\r\n    }\r\n\r\n    e (i,j)\r\n    {\r\n        if (i < 1 || i > this.elements.length || j < 1 || j > this.elements[0].length)\r\n        {\r\n            return null;\r\n        }\r\n        return this.elements[i-1][j-1];\r\n    }\r\n\r\n    row (i)\r\n    {\r\n        if (i > this.elements.length)\r\n        {\r\n            return null;\r\n        }\r\n        return new Vector(this.elements[i-1]);\r\n    }\r\n\r\n    col (j)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return null;\r\n        }\r\n        if (j > this.elements[0].length)\r\n        {\r\n            return null;\r\n        }\r\n        var col = [], n = this.elements.length;\r\n        for (var i = 0; i < n; i++)\r\n        {\r\n            col.push(this.elements[i][j-1]);\r\n        }\r\n        return new Vector(col);\r\n    }\r\n\r\n    dimensions ()\r\n    {\r\n        var cols = (this.elements.length === 0) ? 0 : this.elements[0].length;\r\n        return {rows: this.elements.length, cols: cols};\r\n    }\r\n\r\n    rows ()\r\n    {\r\n        return this.elements.length;\r\n    }\r\n\r\n    cols ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return 0;\r\n        }\r\n        return this.elements[0].length;\r\n    }\r\n\r\n    eql (matrix)\r\n    {\r\n        var M = matrix.elements || matrix;\r\n        if (!M[0] || typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        if (this.elements.length === 0 || M.length === 0)\r\n        {\r\n            return this.elements.length === M.length;\r\n        }\r\n        if (this.elements.length !== M.length)\r\n        {\r\n            return false;\r\n        }\r\n        if (this.elements[0].length !== M[0].length)\r\n        {\r\n            return false;\r\n        }\r\n        var i = this.elements.length, nj = this.elements[0].length, j;\r\n        while (i--)\r\n        {\r\n            j = nj;\r\n            while (j--)\r\n            {\r\n                if (Math.abs(this.elements[i][j] - M[i][j]) > PRECISION)\r\n                {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    dup ()\r\n    {\r\n        return new Matrix(this.elements);\r\n    }\r\n\r\n    map (fn, context)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return new Matrix([]);\r\n        }\r\n        var els = [], i = this.elements.length, nj = this.elements[0].length, j;\r\n        while (i--)\r\n        {\r\n            j = nj;\r\n            els[i] = [];\r\n            while (j--)\r\n            {\r\n                els[i][j] = fn.call(context, this.elements[i][j], i + 1, j + 1);\r\n            }\r\n        }\r\n        return new Matrix(els);\r\n    }\r\n\r\n    isSameSizeAs (matrix)\r\n    {\r\n        var M = matrix.elements || matrix;\r\n        if (typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        if (this.elements.length === 0)\r\n        {\r\n            return M.length === 0;\r\n        }\r\n        return (this.elements.length === M.length && this.elements[0].length === M[0].length);\r\n    }\r\n\r\n    add (matrix)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return this.map(function(x)\r\n                {\r\n                    return x\r\n                });\r\n        }\r\n        var M = matrix.elements || matrix;\r\n        if (typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        if (!this.isSameSizeAs(M))\r\n        {\r\n            return null;\r\n        }\r\n        return this.map(function(x, i, j)\r\n            {\r\n                return x + M[i-1][j-1];\r\n            });\r\n    }\r\n\r\n    subtract (matrix)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return this.map(function(x)\r\n            {\r\n                return x;\r\n            });\r\n        }\r\n        var M = matrix.elements || matrix;\r\n        if (typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        if (!this.isSameSizeAs(M))\r\n        {\r\n            return null;\r\n        }\r\n        return this.map(function(x, i, j)\r\n            {\r\n                return x - M[i-1][j-1];\r\n            });\r\n    }\r\n\r\n    canMultiplyFromLeft (matrix)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return false;\r\n        }\r\n        var M = matrix.elements || matrix;\r\n        if (typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        // this.columns should equal matrix.rows\r\n        return (this.elements[0].length === M.length);\r\n    }\r\n\r\n    multiply (matrix)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return null;\r\n        }\r\n        if (!matrix.elements)\r\n        {\r\n            return this.map(function(x)\r\n            {\r\n                return x * matrix;\r\n            });\r\n        }\r\n        var returnVector = matrix.modulus ? true : false;\r\n        var M = matrix.elements || matrix;\r\n        if (typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        if (!this.canMultiplyFromLeft(M))\r\n        {\r\n            return null;\r\n        }\r\n        var i = this.elements.length, nj = M[0].length, j;\r\n        var cols = this.elements[0].length, c, elements = [], sum;\r\n        while (i--)\r\n        {\r\n            j = nj;\r\n            elements[i] = [];\r\n            while (j--)\r\n            {\r\n                c = cols;\r\n                sum = 0;\r\n                while (c--)\r\n                {\r\n                    sum += this.elements[i][c] * M[c][j];\r\n                }\r\n                elements[i][j] = sum;\r\n            }\r\n        }\r\n        var M = new Matrix(elements);\r\n        return returnVector ? M.col(1) : M;\r\n    }\r\n\r\n    minor (a, b, c, d)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return null;\r\n        }\r\n        var elements = [], ni = c, i, nj, j;\r\n        var rows = this.elements.length, cols = this.elements[0].length;\r\n        while (ni--)\r\n        {\r\n            i = c - ni - 1;\r\n            elements[i] = [];\r\n            nj = d;\r\n            while (nj--)\r\n            {\r\n                j = d - nj - 1;\r\n                elements[i][j] = this.elements[(a+i-1)%rows][(b+j-1)%cols];\r\n            }\r\n        }\r\n        return new Matrix(elements);\r\n    }\r\n\r\n    transpose ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return new Matrix([]);\r\n        }\r\n        var rows = this.elements.length, i, cols = this.elements[0].length, j;\r\n        var elements = [], i = cols;\r\n        while (i--)\r\n        {\r\n            j = rows;\r\n            elements[i] = [];\r\n            while (j--)\r\n            {\r\n                elements[i][j] = this.elements[j][i];\r\n            }\r\n        }\r\n        return new Matrix(elements);\r\n    }\r\n\r\n    isSquare ()\r\n    {\r\n        var cols = (this.elements.length === 0) ? 0 : this.elements[0].length;\r\n        return (this.elements.length === cols);\r\n    }\r\n\r\n    max ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return null;\r\n        }\r\n        var m = 0, i = this.elements.length, nj = this.elements[0].length, j;\r\n        while (i--)\r\n        {\r\n            j = nj;\r\n            while (j--)\r\n            {\r\n                if (Math.abs(this.elements[i][j]) > Math.abs(m))\r\n                {\r\n                    m = this.elements[i][j];\r\n                }\r\n            }\r\n        }\r\n        return m;\r\n    }\r\n\r\n    indexOf (x)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return null;\r\n        }\r\n        var index = null, ni = this.elements.length, i, nj = this.elements[0].length, j;\r\n        for (i = 0; i < ni; i++)\r\n        {\r\n            for (j = 0; j < nj; j++)\r\n            {\r\n                if (this.elements[i][j] === x)\r\n                {\r\n                    return {\r\n                        i: i+1,\r\n                        j: j+1\r\n                    };\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    diagonal ()\r\n    {\r\n        if (!this.isSquare)\r\n        {\r\n            return null;\r\n        }\r\n        var els = [], n = this.elements.length;\r\n        for (var i = 0; i < n; i++)\r\n        {\r\n            els.push(this.elements[i][i]);\r\n        }\r\n        return new Vector(els);\r\n    }\r\n\r\n    toRightTriangular ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return new Matrix([]);\r\n        }\r\n        var M = this.dup(), els;\r\n        var n = this.elements.length, i, j, np = this.elements[0].length, p;\r\n        for (i = 0; i < n; i++)\r\n        {\r\n            if (M.elements[i][i] === 0)\r\n            {\r\n                for (j = i + 1; j < n; j++)\r\n                {\r\n                    if (M.elements[j][i] !== 0)\r\n                    {\r\n                        els = [];\r\n                        for (p = 0; p < np; p++)\r\n                        {\r\n                            els.push(M.elements[i][p] + M.elements[j][p]);\r\n                        }\r\n                        M.elements[i] = els;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            if (M.elements[i][i] !== 0)\r\n            {\r\n                for (j = i + 1; j < n; j++)\r\n                {\r\n                    var multiplier = M.elements[j][i] / M.elements[i][i];\r\n                    els = [];\r\n                    for (p = 0; p < np; p++)\r\n                    {\r\n                        // Elements with column numbers up to an including the number of the\r\n                        // row that we're subtracting can safely be set straight to zero,\r\n                        // since that's the point of this routine and it avoids having to\r\n                        // loop over and correct rounding errors later\r\n                        els.push(p <= i ? 0 : M.elements[j][p] - M.elements[i][p] * multiplier);\r\n                    }\r\n                    M.elements[j] = els;\r\n                }\r\n            }\r\n        }\r\n        return M;\r\n    }\r\n\r\n    determinant ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return 1;\r\n        }\r\n        if (!this.isSquare())\r\n        {\r\n            return null;\r\n        }\r\n        var M = this.toRightTriangular();\r\n        var det = M.elements[0][0], n = M.elements.length;\r\n        for (var i = 1; i < n; i++)\r\n        {\r\n            det = det * M.elements[i][i];\r\n        }\r\n        return det;\r\n    }\r\n\r\n    isSingular ()\r\n    {\r\n        return (this.isSquare() && this.determinant() === 0);\r\n    }\r\n\r\n    trace ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return 0;\r\n        }\r\n        if (!this.isSquare())\r\n        {\r\n            return null;\r\n        }\r\n        var tr = this.elements[0][0], n = this.elements.length;\r\n        for (var i = 1; i < n; i++)\r\n        {\r\n            tr += this.elements[i][i];\r\n        }\r\n        return tr;\r\n    }\r\n\r\n    rank ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return 0;\r\n        }\r\n        var M = this.toRightTriangular(), rank = 0;\r\n        var i = this.elements.length, nj = this.elements[0].length, j;\r\n        while (i--)\r\n        {\r\n            j = nj;\r\n            while (j--)\r\n            {\r\n                if (Math.abs(M.elements[i][j]) > PRECISION)\r\n                {\r\n                    rank++;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        return rank;\r\n    }\r\n\r\n    augment (matrix)\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return this.dup();\r\n        }\r\n        var M = matrix.elements || matrix;\r\n        if (typeof(M[0][0]) === 'undefined')\r\n        {\r\n            M = new Matrix(M).elements;\r\n        }\r\n        var T = this.dup(), cols = T.elements[0].length;\r\n        var i = T.elements.length, nj = M[0].length, j;\r\n        if (i !== M.length)\r\n        {\r\n            return null;\r\n        }\r\n        while (i--)\r\n        {\r\n            j = nj;\r\n            while (j--)\r\n            {\r\n                T.elements[i][cols + j] = M[i][j];\r\n            }\r\n        }\r\n        return T;\r\n    }\r\n\r\n    inverse ()\r\n    {\r\n        if (this.elements.length === 0)\r\n        {\r\n            return null;\r\n        }\r\n        if (!this.isSquare() || this.isSingular())\r\n        {\r\n            return null;\r\n        }\r\n        var n = this.elements.length, i= n, j;\r\n        var M = this.augment(Matrix.I(n)).toRightTriangular();\r\n        var np = M.elements[0].length, p, els, divisor;\r\n        var inverse_elements = [], new_element;\r\n        // Matrix is non-singular so there will be no zeros on the\r\n        // diagonal. Cycle through rows from last to first.\r\n        while (i--)\r\n        {\r\n            // First, normalise diagonal elements to 1\r\n            els = [];\r\n            inverse_elements[i] = [];\r\n            divisor = M.elements[i][i];\r\n            for (p = 0; p < np; p++)\r\n            {\r\n                new_element = M.elements[i][p] / divisor;\r\n                els.push(new_element);\r\n                // Shuffle off the current row of the right hand side into the results\r\n                // array as it will not be modified by later runs through this loop\r\n                if (p >= n)\r\n                {\r\n                    inverse_elements[i].push(new_element);\r\n                }\r\n            }\r\n            M.elements[i] = els;\r\n            // Then, subtract this row from those above it to give the identity matrix\r\n            // on the left hand side\r\n            j = i;\r\n            while (j--)\r\n            {\r\n                els = [];\r\n                for (p = 0; p < np; p++)\r\n                {\r\n                    els.push(M.elements[j][p] - M.elements[i][p] * M.elements[j][i]);\r\n                }\r\n                M.elements[j] = els;\r\n            }\r\n        }\r\n        return new Matrix(inverse_elements);\r\n    }\r\n\r\n    round ()\r\n    {\r\n        return this.map(function(x)\r\n            {\r\n                return Math.round(x);\r\n            });\r\n    }\r\n\r\n    snapTo (x)\r\n    {\r\n        return this.map(function(p)\r\n            {\r\n                return (Math.abs(p - x) <= PRECISION) ? x : p;\r\n            });\r\n    }\r\n\r\n    inspect ()\r\n    {\r\n        var matrix_rows = [];\r\n        var n = this.elements.length;\r\n        if (n === 0) return '[]';\r\n        for (var i = 0; i < n; i++)\r\n        {\r\n            matrix_rows.push(new Vector(this.elements[i]).inspect());\r\n        }\r\n        return matrix_rows.join('\\n');\r\n    }\r\n\r\n    setElements (els)\r\n    {\r\n        var i, j, elements = els.elements || els;\r\n        if (elements[0] && typeof(elements[0][0]) !== 'undefined')\r\n        {\r\n            i = elements.length;\r\n            this.elements = [];\r\n            while (i--)\r\n            {\r\n                j = elements[i].length;\r\n                this.elements[i] = [];\r\n                while (j--)\r\n                {\r\n                    this.elements[i][j] = elements[i][j];\r\n                }\r\n            }\r\n            return this;\r\n        }\r\n        var n = elements.length;\r\n        this.elements = [];\r\n        for (i = 0; i < n; i++)\r\n        {\r\n            this.elements.push([elements[i]]);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    //From glUtils.js\r\n    flatten ()\r\n    {\r\n        var result = [];\r\n        if (this.elements.length == 0)\r\n        {\r\n            return [];\r\n        }\r\n\r\n\r\n        for (var j = 0; j < this.elements[0].length; j++)\r\n        {\r\n            for (var i = 0; i < this.elements.length; i++)\r\n            {\r\n                result.push(this.elements[i][j]);\r\n            }\r\n        }\r\n        return result;\r\n    }\r\n\r\n    //From glUtils.js\r\n    ensure4x4 ()\r\n    {\r\n        if (this.elements.length == 4 && this.elements[0].length == 4)\r\n        {\r\n            return this;\r\n        }\r\n\r\n        if (this.elements.length > 4 || this.elements[0].length > 4)\r\n        {\r\n            return null;\r\n        }\r\n\r\n        for (var i = 0; i < this.elements.length; i++)\r\n        {\r\n            for (var j = this.elements[i].length; j < 4; j++)\r\n            {\r\n                if (i == j)\r\n                {\r\n                    this.elements[i].push(1);\r\n                }\r\n                else\r\n                {\r\n                    this.elements[i].push(0);\r\n                }\r\n            }\r\n        }\r\n\r\n        for (var i = this.elements.length; i < 4; i++)\r\n        {\r\n            if (i == 0)\r\n            {\r\n                this.elements.push([1, 0, 0, 0]);\r\n            }\r\n            else if (i == 1)\r\n            {\r\n                this.elements.push([0, 1, 0, 0]);\r\n            }\r\n            else if (i == 2)\r\n            {\r\n                this.elements.push([0, 0, 1, 0]);\r\n            }\r\n            else if (i == 3)\r\n            {\r\n                this.elements.push([0, 0, 0, 1]);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    //From glUtils.js\r\n    make3x3 ()\r\n    {\r\n        if (this.elements.length != 4 || this.elements[0].length != 4)\r\n        {\r\n            return null;\r\n        }\r\n\r\n        return new Matrix([[this.elements[0][0], this.elements[0][1], this.elements[0][2]],\r\n                              [this.elements[1][0], this.elements[1][1], this.elements[1][2]],\r\n                              [this.elements[2][0], this.elements[2][1], this.elements[2][2]]]);\r\n    };\r\n}\r\n\r\nMatrix.I = function(n)\r\n{\r\n    var els = [], i = n, j;\r\n    while (i--)\r\n    {\r\n        j = n;\r\n        els[i] = [];\r\n        while (j--)\r\n        {\r\n            els[i][j] = (i === j) ? 1 : 0;\r\n        }\r\n    }\r\n    return new Matrix(els);\r\n};\r\n\r\nMatrix.Diagonal = function(elements)\r\n{\r\n    var i = elements.length;\r\n    var M = Matrix.I(i);\r\n    while (i--)\r\n    {\r\n        M.elements[i][i] = elements[i];\r\n    }\r\n    return M;\r\n};\r\n\r\nMatrix.Rotation = function(theta, a)\r\n{\r\n    if (!a)\r\n    {\r\n        return new Matrix([\r\n            [Math.cos(theta),  -Math.sin(theta)],\r\n            [Math.sin(theta),   Math.cos(theta)]\r\n        ]);\r\n    }\r\n    var axis = a.dup();\r\n    if (axis.elements.length !== 3)\r\n    {\r\n        return null;\r\n    }\r\n    var mod = axis.modulus();\r\n    var x = axis.elements[0]/mod, y = axis.elements[1]/mod, z = axis.elements[2]/mod;\r\n    var s = Math.sin(theta), c = Math.cos(theta), t = 1 - c;\r\n    // Formula derived here: http://www.gamedev.net/reference/articles/article1199.asp\r\n    // That proof rotates the co-ordinate system so theta becomes -theta and sin\r\n    // becomes -sin here.\r\n    return new Matrix([\r\n        [ t*x*x + c, t*x*y - s*z, t*x*z + s*y ],\r\n        [ t*x*y + s*z, t*y*y + c, t*y*z - s*x ],\r\n        [ t*x*z - s*y, t*y*z + s*x, t*z*z + c ]\r\n    ]);\r\n};\r\n\r\nMatrix.RotationX = function(t)\r\n{\r\n    var c = Math.cos(t), s = Math.sin(t);\r\n    return new Matrix([\r\n        [  1,  0,  0 ],\r\n        [  0,  c, -s ],\r\n        [  0,  s,  c ]\r\n    ]);\r\n};\r\nMatrix.RotationY = function(t)\r\n{\r\n    var c = Math.cos(t), s = Math.sin(t);\r\n    return new Matrix([\r\n        [  c,  0,  s ],\r\n        [  0,  1,  0 ],\r\n        [ -s,  0,  c ]\r\n    ]);\r\n};\r\nMatrix.RotationZ = function(t)\r\n{\r\n    var c = Math.cos(t), s = Math.sin(t);\r\n    return new Matrix([\r\n        [  c, -s,  0 ],\r\n        [  s,  c,  0 ],\r\n        [  0,  0,  1 ]\r\n    ]);\r\n};\r\n\r\nMatrix.Random = function(n, m)\r\n{\r\n    return Matrix.Zero(n, m).map(function()\r\n        {\r\n            return Math.random();\r\n        });\r\n};\r\n\r\n//From glUtils.js\r\nMatrix.Translation = function (v)\r\n{\r\n    if (v.elements.length == 2)\r\n    {\r\n        var r = Matrix.I(3);\r\n        r.elements[2][0] = v.elements[0];\r\n        r.elements[2][1] = v.elements[1];\r\n        return r;\r\n    }\r\n\r\n    if (v.elements.length == 3)\r\n    {\r\n        var r = Matrix.I(4);\r\n        r.elements[0][3] = v.elements[0];\r\n        r.elements[1][3] = v.elements[1];\r\n        r.elements[2][3] = v.elements[2];\r\n        return r;\r\n    }\r\n\r\n    throw \"Invalid length for Translation\";\r\n};\r\n\r\nMatrix.Zero = function(n, m)\r\n{\r\n    var els = [], i = n, j;\r\n    while (i--)\r\n    {\r\n        j = m;\r\n        els[i] = [];\r\n        while (j--)\r\n        {\r\n            els[i][j] = 0;\r\n        }\r\n    }\r\n    return new Matrix(els);\r\n};\r\n\r\nMatrix.prototype.toUpperTriangular = Matrix.prototype.toRightTriangular;\r\nMatrix.prototype.det = Matrix.prototype.determinant;\r\nMatrix.prototype.tr = Matrix.prototype.trace;\r\nMatrix.prototype.rk = Matrix.prototype.rank;\r\nMatrix.prototype.inv = Matrix.prototype.inverse;\r\nMatrix.prototype.x = Matrix.prototype.multiply;", "\"use strict\";\r\n\r\nimport { Matrix } from \"./Matrix\";\r\nimport { PRECISION } from \"./PRECISION\";\r\n\r\nexport class Vector\r\n{\r\n    constructor (elements)\r\n    {\r\n        this.setElements(elements);\r\n    }\r\n\r\n    e (i)\r\n    {\r\n        return (i < 1 || i > this.elements.length) ? null : this.elements[i-1];\r\n    }\r\n\r\n    dimensions ()\r\n    {\r\n        return this.elements.length;\r\n    }\r\n\r\n    modulus ()\r\n    {\r\n        return Math.sqrt(this.dot(this));\r\n    }\r\n\r\n    eql (vector)\r\n    {\r\n        var n = this.elements.length;\r\n        var V = vector.elements || vector;\r\n        if (n !== V.length)\r\n        {\r\n            return false;\r\n        }\r\n        while (n--)\r\n        {\r\n            if (Math.abs(this.elements[n] - V[n]) > PRECISION)\r\n            {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    dup () {\r\n        return new Vector(this.elements);\r\n    }\r\n\r\n    map (fn, context)\r\n    {\r\n        var elements = [];\r\n        this.each(function(x, i)\r\n            {\r\n                elements.push(fn.call(context, x, i));\r\n            });\r\n        return new Vector(elements);\r\n    }\r\n\r\n    forEach (fn, context)\r\n    {\r\n        var n = this.elements.length;\r\n        for (var i = 0; i < n; i++)\r\n        {\r\n            fn.call(context, this.elements[i], i+1);\r\n        }\r\n    }\r\n\r\n    toUnitVector ()\r\n    {\r\n        var r = this.modulus();\r\n        if (r === 0)\r\n        {\r\n            return this.dup();\r\n        }\r\n        return this.map(function(x)\r\n            {\r\n                return x/r;\r\n            });\r\n    }\r\n\r\n    angleFrom (vector)\r\n    {\r\n        var V = vector.elements || vector;\r\n        var n = this.elements.length, k = n, i;\r\n        if (n !== V.length)\r\n        {\r\n            return null;\r\n        }\r\n        var dot = 0, mod1 = 0, mod2 = 0;\r\n        // Work things out in parallel to save time\r\n        this.each(function(x, i)\r\n            {\r\n                dot += x * V[i-1];\r\n                mod1 += x * x;\r\n                mod2 += V[i-1] * V[i-1];\r\n            });\r\n        mod1 = Math.sqrt(mod1); mod2 = Math.sqrt(mod2);\r\n        if (mod1*mod2 === 0)\r\n        {\r\n            return null;\r\n        }\r\n        var theta = dot / (mod1*mod2);\r\n        if (theta < -1)\r\n        {\r\n            theta = -1;\r\n        }\r\n        if (theta > 1)\r\n        {\r\n            theta = 1;\r\n        }\r\n        return Math.acos(theta);\r\n    }\r\n\r\n    isParallelTo (vector)\r\n    {\r\n        var angle = this.angleFrom(vector);\r\n        return (angle === null) ? null : (angle <= PRECISION);\r\n    }\r\n\r\n    isAntiparallelTo (vector)\r\n    {\r\n        var angle = this.angleFrom(vector);\r\n        return (angle === null) ? null : (Math.abs(angle - Math.PI) <= PRECISION);\r\n    }\r\n\r\n    isPerpendicularTo (vector)\r\n    {\r\n        var dot = this.dot(vector);\r\n        return (dot === null) ? null : (Math.abs(dot) <= PRECISION);\r\n    }\r\n\r\n    add (vector)\r\n    {\r\n        var V = vector.elements || vector;\r\n        if (this.elements.length !== V.length)\r\n        {\r\n            return null;\r\n        }\r\n        return this.map(function(x, i) { return x + V[i-1]; });\r\n    }\r\n\r\n    subtract (vector)\r\n    {\r\n        var V = vector.elements || vector;\r\n        if (this.elements.length !== V.length)\r\n        {\r\n            return null;\r\n        }\r\n        return this.map(function(x, i)\r\n            {\r\n                return x - V[i-1];\r\n            });\r\n    }\r\n\r\n    multiply (k)\r\n    {\r\n        return this.map(function(x)\r\n            {\r\n                return x*k;\r\n            });\r\n    }\r\n\r\n    dot (vector)\r\n    {\r\n        var V = vector.elements || vector;\r\n        var i, product = 0, n = this.elements.length;\r\n        if (n !== V.length)\r\n        {\r\n            return null;\r\n        }\r\n        while (n--)\r\n        {\r\n            product += this.elements[n] * V[n];\r\n        }\r\n        return product;\r\n    }\r\n\r\n    cross (vector)\r\n    {\r\n        var B = vector.elements || vector;\r\n        if (this.elements.length !== 3 || B.length !== 3)\r\n        {\r\n            return null;\r\n        }\r\n        var A = this.elements;\r\n        return new Vector([\r\n            (A[1] * B[2]) - (A[2] * B[1]),\r\n            (A[2] * B[0]) - (A[0] * B[2]),\r\n            (A[0] * B[1]) - (A[1] * B[0])\r\n        ]);\r\n    }\r\n\r\n    max ()\r\n    {\r\n        var m = 0, i = this.elements.length;\r\n        while (i--)\r\n        {\r\n            if (Math.abs(this.elements[i]) > Math.abs(m))\r\n            {\r\n                m = this.elements[i];\r\n            }\r\n        }\r\n        return m;\r\n    }\r\n\r\n    indexOf (x)\r\n    {\r\n        var index = null, n = this.elements.length;\r\n        for (var i = 0; i < n; i++)\r\n        {\r\n            if (index === null && this.elements[i] === x)\r\n            {\r\n                index = i + 1;\r\n            }\r\n        }\r\n        return index;\r\n    }\r\n\r\n    toDiagonalMatrix ()\r\n    {\r\n        return Matrix.Diagonal(this.elements);\r\n    }\r\n\r\n    round ()\r\n    {\r\n        return this.map(function(x)\r\n            {\r\n                return Math.round(x);\r\n            });\r\n    }\r\n\r\n    snapTo (x)\r\n    {\r\n        return this.map(function(y)\r\n            {\r\n                return (Math.abs(y - x) <= PRECISION) ? x : y;\r\n            });\r\n    }\r\n\r\n    distanceFrom (obj)\r\n    {\r\n        if (obj.anchor || (obj.start && obj.end))\r\n        {\r\n            return obj.distanceFrom(this);\r\n        }\r\n        var V = obj.elements || obj;\r\n        if (V.length !== this.elements.length)\r\n        {\r\n            return null;\r\n        }\r\n        var sum = 0, part;\r\n        this.each(function(x, i)\r\n            {\r\n                part = x - V[i-1];\r\n                sum += part * part;\r\n            });\r\n        return Math.sqrt(sum);\r\n    }\r\n\r\n    liesOn (line)\r\n    {\r\n        return line.contains(this);\r\n    }\r\n\r\n    liesIn (plane)\r\n    {\r\n        return plane.contains(this);\r\n    }\r\n\r\n    rotate (t, obj)\r\n    {\r\n        var V, R = null, x, y, z;\r\n        if (t.determinant)\r\n        {\r\n            R = t.elements;\r\n        }\r\n        switch (this.elements.length)\r\n        {\r\n            case 2:\r\n            {\r\n                V = obj.elements || obj;\r\n                if (V.length !== 2)\r\n                {\r\n                    return null;\r\n                }\r\n                if (!R)\r\n                {\r\n                    R = Matrix.Rotation(t).elements;\r\n                }\r\n                x = this.elements[0] - V[0];\r\n                y = this.elements[1] - V[1];\r\n                return new Vector([\r\n                    V[0] + R[0][0] * x + R[0][1] * y,\r\n                    V[1] + R[1][0] * x + R[1][1] * y\r\n                ]);\r\n                break;\r\n            }\r\n            case 3:\r\n            {\r\n                if (!obj.direction)\r\n                {\r\n                    return null;\r\n                }\r\n                var C = obj.pointClosestTo(this).elements;\r\n                if (!R)\r\n                {\r\n                    R = Matrix.Rotation(t, obj.direction).elements;\r\n                }\r\n                x = this.elements[0] - C[0];\r\n                y = this.elements[1] - C[1];\r\n                z = this.elements[2] - C[2];\r\n                return new Vector([\r\n                    C[0] + R[0][0] * x + R[0][1] * y + R[0][2] * z,\r\n                    C[1] + R[1][0] * x + R[1][1] * y + R[1][2] * z,\r\n                    C[2] + R[2][0] * x + R[2][1] * y + R[2][2] * z\r\n                ]);\r\n                break;\r\n            }\r\n            default:\r\n            {\r\n                return null;\r\n            }\r\n        }\r\n    }\r\n\r\n    reflectionIn (obj)\r\n    {\r\n        if (obj.anchor)\r\n        {\r\n            // obj is a plane or line\r\n            var P = this.elements.slice();\r\n            var C = obj.pointClosestTo(P).elements;\r\n            return new Vector([C[0] + (C[0] - P[0]), C[1] + (C[1] - P[1]), C[2] + (C[2] - (P[2] || 0))]);\r\n        }\r\n        else\r\n        {\r\n            // obj is a point\r\n            var Q = obj.elements || obj;\r\n            if (this.elements.length !== Q.length)\r\n            {\r\n                return null;\r\n            }\r\n            return this.map(function(x, i) { return Q[i-1] + (Q[i-1] - x); });\r\n        }\r\n    }\r\n\r\n    to3D ()\r\n    {\r\n        var V = this.dup();\r\n        switch (V.elements.length)\r\n        {\r\n            case 3:\r\n            {\r\n                break;\r\n            }\r\n            case 2:\r\n            {\r\n                V.elements.push(0);\r\n                break;\r\n            }\r\n            default:\r\n            {\r\n                return null;\r\n            }\r\n        }\r\n        return V;\r\n    }\r\n\r\n    inspect ()\r\n    {\r\n        return '[' + this.elements.join(', ') + ']';\r\n    }\r\n\r\n    setElements (els)\r\n    {\r\n        this.elements = (els.elements || els).slice();\r\n        return this;\r\n    }\r\n\r\n    //From glUtils.js\r\n    flatten ()\r\n    {\r\n        return this.elements;\r\n    }\r\n}\r\n\r\nVector.Random = function(n)\r\n{\r\n    var elements = [];\r\n    while (n--)\r\n    {\r\n        elements.push(Math.random());\r\n    }\r\n    return new Vector(elements);\r\n};\r\n\r\nVector.Zero = function(n)\r\n{\r\n    var elements = [];\r\n    while (n--)\r\n    {\r\n        elements.push(0);\r\n    }\r\n    return new Vector(elements);\r\n};\r\n\r\nVector.prototype.x = Vector.prototype.multiply;\r\nVector.prototype.each = Vector.prototype.forEach;\r\n\r\nVector.i = new Vector([1,0,0]);\r\nVector.j = new Vector([0,1,0]);\r\nVector.k = new Vector([0,0,1]);", "import { Vector } from 'sylvester-es6/src/Vector';\n\nconst computeCentroids = (config, position, row) => {\n  const centroids = [];\n\n  const p = Object.keys(config.dimensions);\n  const cols = p.length;\n  const a = 0.5; // center between axes\n  for (let i = 0; i < cols; ++i) {\n    // centroids on 'real' axes\n    const x = position(p[i]);\n    const y = config.dimensions[p[i]].yscale(row[p[i]]);\n    centroids.push(new Vector([x, y]));\n\n    // centroids on 'virtual' axes\n    if (i < cols - 1) {\n      const cx = x + a * (position(p[i + 1]) - x);\n      let cy = y + a * (config.dimensions[p[i + 1]].yscale(row[p[i + 1]]) - y);\n      if (config.bundleDimension !== null) {\n        const leftCentroid = config.clusterCentroids\n          .get(\n            config.dimensions[config.bundleDimension].yscale(\n              row[config.bundleDimension]\n            )\n          )\n          .get(p[i]);\n        const rightCentroid = config.clusterCentroids\n          .get(\n            config.dimensions[config.bundleDimension].yscale(\n              row[config.bundleDimension]\n            )\n          )\n          .get(p[i + 1]);\n        let centroid = 0.5 * (leftCentroid + rightCentroid);\n        cy = centroid + (1 - config.bundlingStrength) * (cy - centroid);\n      }\n      centroids.push(new Vector([cx, cy]));\n    }\n  }\n\n  return centroids;\n};\n\nexport default computeCentroids;\n", "// draw single cubic bezier curve\nimport computeCentroids from './computeCentroids';\nimport computeControlPoints from './computeControlPoints';\nimport h from './height';\n\nconst singleCurve = (config, position, d, ctx) => {\n  const centroids = computeCentroids(config, position, d);\n  const cps = computeControlPoints(config.smoothness, centroids);\n\n  ctx.moveTo(cps[0].e(1), cps[0].e(2));\n\n  for (let i = 1; i < cps.length; i += 3) {\n    if (config.showControlPoints) {\n      for (let j = 0; j < 3; j++) {\n        ctx.fillRect(cps[i + j].e(1), cps[i + j].e(2), 2, 2);\n      }\n    }\n    ctx.bezierCurveTo(\n      cps[i].e(1),\n      cps[i].e(2),\n      cps[i + 1].e(1),\n      cps[i + 1].e(2),\n      cps[i + 2].e(1),\n      cps[i + 2].e(2)\n    );\n  }\n};\n\n// returns the y-position just beyond the separating null value line\nconst getNullPosition = config => {\n  if (config.nullValueSeparator === 'bottom') {\n    return h(config) + 1;\n  } else if (config.nullValueSeparator === 'top') {\n    return 1;\n  } else {\n    console.log(\n      \"A value is NULL, but nullValueSeparator is not set; set it to 'bottom' or 'top'.\"\n    );\n  }\n  return h(config) + 1;\n};\n\nconst singlePath = (config, position, d, ctx) => {\n  Object.keys(config.dimensions)\n    .map(p => [\n      position(p),\n      d[p] === undefined\n        ? getNullPosition(config)\n        : config.dimensions[p].yscale(d[p]),\n    ])\n    .sort((a, b) => a[0] - b[0])\n    .forEach((p, i) => {\n      i === 0 ? ctx.moveTo(p[0], p[1]) : ctx.lineTo(p[0], p[1]);\n    });\n};\n\n// draw single polyline\nconst colorPath = (config, position, d, ctx) => {\n  ctx.beginPath();\n  if (\n    (config.bundleDimension !== null && config.bundlingStrength > 0) ||\n    config.smoothness > 0\n  ) {\n    singleCurve(config, position, d, ctx);\n  } else {\n    singlePath(config, position, d, ctx);\n  }\n  ctx.stroke();\n};\n\nexport default colorPath;\n", "import { Vector } from 'sylvester-es6/src/Vector';\n\nconst computeControlPoints = (smoothness, centroids) => {\n  const cols = centroids.length;\n  const a = smoothness;\n  const cps = [];\n\n  cps.push(centroids[0]);\n  cps.push(\n    new Vector([\n      centroids[0].e(1) + a * 2 * (centroids[1].e(1) - centroids[0].e(1)),\n      centroids[0].e(2),\n    ])\n  );\n  for (let col = 1; col < cols - 1; ++col) {\n    let mid = centroids[col];\n    let left = centroids[col - 1];\n    let right = centroids[col + 1];\n\n    let diff = left.subtract(right);\n    cps.push(mid.add(diff.x(a)));\n    cps.push(mid);\n    cps.push(mid.subtract(diff.x(a)));\n  }\n\n  cps.push(\n    new Vector([\n      centroids[cols - 1].e(1) +\n        a * 2 * (centroids[cols - 2].e(1) - centroids[cols - 1].e(1)),\n      centroids[cols - 1].e(2),\n    ])\n  );\n  cps.push(centroids[cols - 1]);\n\n  return cps;\n};\n\nexport default computeControlPoints;\n", "const _functor = v => (typeof v === 'function' ? v : () => v);\n\nexport default _functor;\n", "import colorPath from '../util/colorPath';\nimport functor from '../util/functor';\n\nconst pathMark = (config, ctx, position) => (d, i) => {\n  ctx.marked.strokeStyle = functor(config.color)(d, i);\n  return colorPath(config, position, d, ctx.marked);\n};\n\nconst renderMarkedDefault = (config, pc, ctx, position) => () => {\n  pc.clear('marked');\n\n  if (config.marked.length) {\n    config.marked.forEach(pathMark(config, ctx, position));\n  }\n};\n\nconst renderMarkedQueue = (config, markedQueue) => () => {\n  if (config.marked) {\n    markedQueue(config.marked);\n  } else {\n    markedQueue([]); // This is needed to clear the currently marked items\n  }\n};\n\nconst renderMarked = (config, pc, events) =>\n  function() {\n    if (!Object.keys(config.dimensions).length) pc.detectDimensions();\n\n    pc.renderMarked[config.mode]();\n    events.call('render', this);\n    return this;\n  };\n\nexport { pathMark, renderMarked, renderMarkedDefault, renderMarkedQueue };\n", "import isBrushed from '../util/isBrushed';\nimport colorPath from '../util/colorPath';\nimport functor from '../util/functor';\n\nconst pathBrushed = (config, ctx, position) => (d, i) => {\n  if (config.brushedColor !== null) {\n    ctx.brushed.strokeStyle = functor(config.brushedColor)(d, i);\n  } else {\n    ctx.brushed.strokeStyle = functor(config.color)(d, i);\n  }\n  return colorPath(config, position, d, ctx.brushed);\n};\n\nconst renderBrushedDefault = (config, ctx, position, pc, brushGroup) => () => {\n  pc.clear('brushed');\n\n  if (isBrushed(config, brushGroup) && config.brushed !== false) {\n    config.brushed.forEach(pathBrushed(config, ctx, position));\n  }\n};\n\nconst renderBrushedQueue = (config, brushGroup, brushedQueue) => () => {\n  if (isBrushed(config, brushGroup)) {\n    brushedQueue(config.brushed);\n  } else {\n    brushedQueue([]); // This is needed to clear the currently brushed items\n  }\n};\n\nconst renderBrushed = (config, pc, events) =>\n  function() {\n    if (!Object.keys(config.dimensions).length) pc.detectDimensions();\n\n    pc.renderBrushed[config.mode]();\n    events.call('render', this);\n    return this;\n  };\n\nexport { pathBrushed, renderBrushed, renderBrushedDefault, renderBrushedQueue };\n", "// a better \"typeof\" from this post: http://stackoverflow.com/questions/7390426/better-way-to-get-type-of-a-javascript-variable\nconst toType = v => {\n  return {}.toString\n    .call(v)\n    .match(/\\s([a-zA-Z]+)/)[1]\n    .toLowerCase();\n};\n\nexport default toType;\n", "// pairs of adjacent dimensions\nconst adjacentPairs = arr => {\n  let ret = [];\n  for (let i = 0; i < arr.length - 1; i++) {\n    ret.push([arr[i], arr[i + 1]]);\n  }\n  return ret;\n};\n\nexport default adjacentPairs;\n", "import { selectAll } from 'd3-selection';\n\nimport colorPath from '../util/colorPath';\nimport functor from '../util/functor';\n\nconst pathHighlight = (config, ctx, position) => (d, i) => {\n  ctx.highlight.strokeStyle = functor(config.color)(d, i);\n  return colorPath(config, position, d, ctx.highlight);\n};\n\n// highlight an array of data\nconst highlight = (config, pc, canvas, events, ctx, position) =>\n  function(data = null) {\n    if (data === null) {\n      return config.highlighted;\n    }\n\n    config.highlighted = data;\n    pc.clear('highlight');\n    selectAll([canvas.foreground, canvas.brushed]).classed('faded', true);\n    data.forEach(pathHighlight(config, ctx, position));\n    events.call('highlight', this, data);\n    return this;\n  };\n\nexport default highlight;\n", "import colorPath from '../util/colorPath';\nimport functor from '../util/functor';\n\nconst pathForeground = (config, ctx, position) => (d, i) => {\n  ctx.foreground.strokeStyle = functor(config.color)(d, i);\n  return colorPath(config, position, d, ctx.foreground);\n};\n\nconst renderDefault = (config, pc, ctx, position) => () => {\n  pc.clear('foreground');\n  pc.clear('highlight');\n\n  pc.renderBrushed.default();\n  pc.renderMarked.default();\n\n  config.data.forEach(pathForeground(config, ctx, position));\n};\n\nconst renderDefaultQueue = (config, pc, foregroundQueue) => () => {\n  pc.renderBrushed.queue();\n  pc.renderMarked.queue();\n  foregroundQueue(config.data);\n};\n\nexport default renderDefault;\n\nexport { pathForeground, renderDefaultQueue };\n", "import toType from './toType';\n\n// try to coerce to number before returning type\nconst toTypeCoerceNumbers = v =>\n  parseFloat(v) == v && v !== null ? 'number' : toType(v);\n\nexport default toTypeCoerceNumbers;\n", "import toTypeCoerceNumbers from './toTypeCoerceNumbers';\n\n// attempt to determine types of each dimension based on first row of data\nconst detectDimensionTypes = data =>\n  Object.keys(data[0]).reduce((acc, cur) => {\n    const key = isNaN(Number(cur)) ? cur : parseInt(cur);\n    acc[key] = toTypeCoerceNumbers(data[0][cur]);\n\n    return acc;\n  }, {});\n\nexport default detectDimensionTypes;\n", "const DefaultConfig = {\n  data: [],\n  highlighted: [],\n  marked: [],\n  dimensions: {},\n  dimensionTitleRotation: 0,\n  brushes: [],\n  brushed: false,\n  brushedColor: null,\n  alphaOnBrushed: 0.0,\n  lineWidth: 1.4,\n  highlightedLineWidth: 3,\n  mode: 'default',\n  markedLineWidth: 3,\n  markedShadowColor: '#ffffff',\n  markedShadowBlur: 10,\n  rate: 20,\n  width: 600,\n  height: 300,\n  margin: { top: 24, right: 20, bottom: 12, left: 20 },\n  nullValueSeparator: 'undefined', // set to \"top\" or \"bottom\"\n  nullValueSeparatorPadding: { top: 8, right: 0, bottom: 8, left: 0 },\n  color: '#069',\n  composite: 'source-over',\n  alpha: 0.7,\n  bundlingStrength: 0.5,\n  bundleDimension: null,\n  smoothness: 0.0,\n  showControlPoints: false,\n  hideAxis: [],\n  flipAxes: [],\n  animationTime: 1100, // How long it takes to flip the axis when you double click\n  rotateLabels: false,\n};\n\nexport default DefaultConfig;\n", "// side effects for setters\nimport { dispatch } from 'd3-dispatch';\nimport computeClusterCentroids from '../util/computeClusterCentroids';\nimport flipAxisAndUpdatePCP from '../util/flipAxisAndUpdatePCP';\n\nconst without = (arr, items) => {\n  items.forEach(el => {\n    delete arr[el];\n  });\n  return arr;\n};\n\nconst sideEffects = (\n  config,\n  ctx,\n  pc,\n  xscale,\n  axis,\n  flags,\n  brushedQueue,\n  markedQueue,\n  foregroundQueue\n) =>\n  dispatch\n    .apply(this, Object.keys(config))\n    .on('composite', d => {\n      ctx.foreground.globalCompositeOperation = d.value;\n      ctx.brushed.globalCompositeOperation = d.value;\n    })\n    .on('alpha', d => {\n      ctx.foreground.globalAlpha = d.value;\n      ctx.brushed.globalAlpha = d.value;\n    })\n    .on('brushedColor', d => {\n      ctx.brushed.strokeStyle = d.value;\n    })\n    .on('width', d => pc.resize())\n    .on('height', d => pc.resize())\n    .on('margin', d => pc.resize())\n    .on('rate', d => {\n      brushedQueue.rate(d.value);\n      markedQueue.rate(d.value);\n      foregroundQueue.rate(d.value);\n    })\n    .on('dimensions', d => {\n      config.dimensions = pc.applyDimensionDefaults(Object.keys(d.value));\n      xscale.domain(pc.getOrderedDimensionKeys());\n      pc.sortDimensions();\n      if (flags.interactive) {\n        pc.render().updateAxes();\n      }\n    })\n    .on('bundleDimension', d => {\n      if (!Object.keys(config.dimensions).length) pc.detectDimensions();\n      pc.autoscale();\n      if (typeof d.value === 'number') {\n        if (d.value < Object.keys(config.dimensions).length) {\n          config.bundleDimension = config.dimensions[d.value];\n        } else if (d.value < config.hideAxis.length) {\n          config.bundleDimension = config.hideAxis[d.value];\n        }\n      } else {\n        config.bundleDimension = d.value;\n      }\n\n      config.clusterCentroids = computeClusterCentroids(\n        config,\n        config.bundleDimension\n      );\n      if (flags.interactive) {\n        pc.render();\n      }\n    })\n    .on('hideAxis', d => {\n      pc.brushReset();\n      pc.dimensions(pc.applyDimensionDefaults());\n      pc.dimensions(without(config.dimensions, d.value));\n      pc.render();\n    })\n    .on('flipAxes', d => {\n      if (d.value && d.value.length) {\n        d.value.forEach(function(dimension) {\n          flipAxisAndUpdatePCP(config, pc, axis)(dimension);\n        });\n        pc.updateAxes(0);\n      }\n    });\n\nexport default sideEffects;\n", "const computeClusterCentroids = (config, d) => {\n  const clusterCentroids = new Map();\n  const clusterCounts = new Map();\n  // determine clusterCounts\n  config.data.forEach(function(row) {\n    let scaled = config.dimensions[d].yscale(row[d]);\n    if (!clusterCounts.has(scaled)) {\n      clusterCounts.set(scaled, 0);\n    }\n    let count = clusterCounts.get(scaled);\n    clusterCounts.set(scaled, count + 1);\n  });\n\n  config.data.forEach(function(row) {\n    Object.keys(config.dimensions).map(p => {\n      let scaled = config.dimensions[d].yscale(row[d]);\n      if (!clusterCentroids.has(scaled)) {\n        const _map = new Map();\n        clusterCentroids.set(scaled, _map);\n      }\n      if (!clusterCentroids.get(scaled).has(p)) {\n        clusterCentroids.get(scaled).set(p, 0);\n      }\n      let value = clusterCentroids.get(scaled).get(p);\n      value += config.dimensions[p].yscale(row[p]) / clusterCounts.get(scaled);\n      clusterCentroids.get(scaled).set(p, value);\n    });\n  });\n\n  return clusterCentroids;\n};\n\nexport default computeClusterCentroids;\n", "// side effects for setters\nimport sideEffects from './state/sideEffects';\nimport getset from './util/getset';\n\nconst d3_rebind = (target, source, method) =>\n  function() {\n    const value = method.apply(source, arguments);\n    return value === source ? target : value;\n  };\n\nconst _rebind = (target, source, method) => {\n  target[method] = d3_rebind(target, source, source[method]);\n  return target;\n};\n\nconst bindEvents = (\n  __,\n  ctx,\n  pc,\n  xscale,\n  flags,\n  brushedQueue,\n  markedQueue,\n  foregroundQueue,\n  events,\n  axis\n) => {\n  const side_effects = sideEffects(\n    __,\n    ctx,\n    pc,\n    xscale,\n    axis,\n    flags,\n    brushedQueue,\n    markedQueue,\n    foregroundQueue\n  );\n\n  // create getter/setters\n  getset(pc, __, events, side_effects);\n\n  // expose events\n  // getter/setter with event firing\n  _rebind(pc, events, 'on');\n\n  _rebind(\n    pc,\n    axis,\n    'ticks',\n    'orient',\n    'tickValues',\n    'tickSubdivide',\n    'tickSize',\n    'tickPadding',\n    'tickFormat'\n  );\n};\n\nexport default bindEvents;\n", "const getset = (obj, state, events, side_effects) => {\n  Object.keys(state).forEach(function(key) {\n    obj[key] = function(x) {\n      if (!arguments.length) {\n        return state[key];\n      }\n      if (\n        key === 'dimensions' &&\n        Object.prototype.toString.call(x) === '[object Array]'\n      ) {\n        console.warn('pc.dimensions([]) is deprecated, use pc.dimensions({})');\n        x = obj.applyDimensionDefaults(x);\n      }\n      let old = state[key];\n      state[key] = x;\n      side_effects.call(key, obj, { value: x, previous: old });\n      events.call(key, obj, { value: x, previous: old });\n      return obj;\n    };\n  });\n};\n\nexport default getset;\n", "// misc\nimport renderQueue from './util/renderQueue';\nimport w from './util/width';\n\n// brush\nimport install1DAxes from './brush/1d';\nimport install1DAxesMulti from './brush/1d-multi';\nimport install2DStrums from './brush/strums';\nimport installAngularBrush from './brush/angular';\n\n// api\nimport intersection from './api/intersection';\nimport mergeParcoords from './api/mergeParcoords';\nimport selected from './api/selected';\nimport brushMode from './api/brushMode';\nimport updateAxes from './api/updateAxes';\nimport autoscale from './api/autoscale';\nimport brushable from './api/brushable';\nimport commonScale from './api/commonScale';\nimport computeRealCentroids from './api/computeRealCentroids';\nimport applyDimensionDefaults from './api/applyDimensionDefaults';\nimport createAxes from './api/createAxes';\nimport axisDots from './api/axisDots';\nimport applyAxisConfig from './api/applyAxisConfig';\nimport reorderable from './api/reorderable';\nimport resize from './api/resize';\nimport reorder from './api/reorder';\nimport sortDimensions from './api/sortDimensions';\nimport sortDimensionsByRowData from './api/sortDimensionsByRowData';\nimport clear from './api/clear';\nimport {\n  pathMark,\n  renderMarked,\n  renderMarkedDefault,\n  renderMarkedQueue,\n} from './api/renderMarked';\nimport {\n  pathBrushed,\n  renderBrushed,\n  renderBrushedDefault,\n  renderBrushedQueue,\n} from './api/renderBrushed';\nimport brushReset from './api/brushReset';\nimport toType from './api/toType';\nimport toString from './api/toString';\nimport adjacentPairs from './api/adjacentPairs';\nimport highlight from './api/highlight';\nimport unhighlight from './api/unhighlight';\nimport mark from './api/mark';\nimport unmark from './api/unmark';\nimport removeAxes from './api/removeAxes';\nimport render from './api/render';\nimport renderDefault, {\n  pathForeground,\n  renderDefaultQueue,\n} from './api/renderDefault';\nimport toTypeCoerceNumbers from './api/toTypeCoerceNumbers';\nimport detectDimensionTypes from './api/detectDimensionTypes';\nimport getOrderedDimensionKeys from './api/getOrderedDimensionKeys';\nimport interactive from './api/interactive';\nimport shadows from './api/shadows';\nimport init from './api/init';\nimport flip from './api/flip';\nimport detectDimensions from './api/detectDimensions';\nimport scale from './api/scale';\n\nimport { version } from '../package.json';\nimport initState from './state';\nimport bindEvents from './bindEvents';\n\n//css\nimport './parallel-coordinates.css';\n\nconst ParCoords = userConfig => {\n  const state = initState(userConfig);\n  const {\n    config,\n    events,\n    flags,\n    xscale,\n    dragging,\n    axis,\n    ctx,\n    canvas,\n    brush,\n  } = state;\n\n  const pc = init(config, canvas, ctx);\n\n  const position = d => {\n    if (xscale.range().length === 0) {\n      xscale.range([0, w(config)], 1);\n    }\n    return dragging[d] == null ? xscale(d) : dragging[d];\n  };\n\n  const brushedQueue = renderQueue(pathBrushed(config, ctx, position))\n    .rate(50)\n    .clear(() => pc.clear('brushed'));\n\n  const markedQueue = renderQueue(pathMark(config, ctx, position))\n    .rate(50)\n    .clear(() => pc.clear('marked'));\n\n  const foregroundQueue = renderQueue(pathForeground(config, ctx, position))\n    .rate(50)\n    .clear(function() {\n      pc.clear('foreground');\n      pc.clear('highlight');\n    });\n\n  bindEvents(\n    config,\n    ctx,\n    pc,\n    xscale,\n    flags,\n    brushedQueue,\n    markedQueue,\n    foregroundQueue,\n    events,\n    axis\n  );\n\n  // expose the state of the chart\n  pc.state = config;\n  pc.flags = flags;\n\n  pc.autoscale = autoscale(config, pc, xscale, ctx);\n  pc.scale = scale(config, pc);\n  pc.flip = flip(config);\n  pc.commonScale = commonScale(config, pc);\n  pc.detectDimensions = detectDimensions(pc);\n  // attempt to determine types of each dimension based on first row of data\n  pc.detectDimensionTypes = detectDimensionTypes;\n  pc.applyDimensionDefaults = applyDimensionDefaults(config, pc);\n  pc.getOrderedDimensionKeys = getOrderedDimensionKeys(config);\n\n  //Renders the polylines.\n  pc.render = render(config, pc, events);\n  pc.renderBrushed = renderBrushed(config, pc, events);\n  pc.renderMarked = renderMarked(config, pc, events);\n  pc.render.default = renderDefault(config, pc, ctx, position);\n  pc.render.queue = renderDefaultQueue(config, pc, foregroundQueue);\n  pc.renderBrushed.default = renderBrushedDefault(\n    config,\n    ctx,\n    position,\n    pc,\n    brush\n  );\n  pc.renderBrushed.queue = renderBrushedQueue(config, brush, brushedQueue);\n  pc.renderMarked.default = renderMarkedDefault(config, pc, ctx, position);\n  pc.renderMarked.queue = renderMarkedQueue(config, markedQueue);\n\n  pc.compute_real_centroids = computeRealCentroids(config, position);\n  pc.shadows = shadows(flags, pc);\n  pc.axisDots = axisDots(config, pc, position);\n  pc.clear = clear(config, pc, ctx, brush);\n  pc.createAxes = createAxes(config, pc, xscale, flags, axis);\n  pc.removeAxes = removeAxes(pc);\n  pc.updateAxes = updateAxes(config, pc, position, axis, flags);\n  pc.applyAxisConfig = applyAxisConfig;\n  pc.brushable = brushable(config, pc, flags);\n  pc.brushReset = brushReset(config, pc);\n  pc.selected = selected(config, pc);\n  pc.reorderable = reorderable(config, pc, xscale, position, dragging, flags);\n\n  // Reorder dimensions, such that the highest value (visually) is on the left and\n  // the lowest on the right. Visual values are determined by the data values in\n  // the given row.\n  pc.reorder = reorder(config, pc, xscale);\n  pc.sortDimensionsByRowData = sortDimensionsByRowData(config);\n  pc.sortDimensions = sortDimensions(config, position);\n\n  // pairs of adjacent dimensions\n  pc.adjacent_pairs = adjacentPairs;\n  pc.interactive = interactive(flags);\n\n  // expose internal state\n  pc.xscale = xscale;\n  pc.ctx = ctx;\n  pc.canvas = canvas;\n  pc.g = () => pc._g;\n\n  // rescale for height, width and margins\n  // TODO currently assumes chart is brushable, and destroys old brushes\n  pc.resize = resize(config, pc, flags, events);\n\n  // highlight an array of data\n  pc.highlight = highlight(config, pc, canvas, events, ctx, position);\n  // clear highlighting\n  pc.unhighlight = unhighlight(config, pc, canvas);\n\n  // mark an array of data\n  pc.mark = mark(config, pc, canvas, events, ctx, position);\n  // clear marked data\n  pc.unmark = unmark(config, pc, canvas);\n\n  // calculate 2d intersection of line a->b with line c->d\n  // points are objects with x and y properties\n  pc.intersection = intersection;\n\n  // Merges the canvases and SVG elements into one canvas element which is then passed into the callback\n  // (so you can choose to save it to disk, etc.)\n  pc.mergeParcoords = mergeParcoords(pc);\n  pc.brushModes = () => Object.getOwnPropertyNames(brush.modes);\n  pc.brushMode = brushMode(brush, config, pc);\n\n  // install brushes\n  install1DAxes(brush, config, pc, events);\n  install2DStrums(brush, config, pc, events, xscale);\n  installAngularBrush(brush, config, pc, events, xscale);\n  install1DAxesMulti(brush, config, pc, events);\n\n  pc.version = version;\n  // this descriptive text should live with other introspective methods\n  pc.toString = toString(config);\n  pc.toType = toType;\n  // try to coerce to number before returning type\n  pc.toTypeCoerceNumbers = toTypeCoerceNumbers;\n\n  return pc;\n};\n\nexport default ParCoords;\n", "import { entries, keys } from 'd3-collection';\nimport { axisLeft } from 'd3-axis';\nimport { dispatch } from 'd3-dispatch';\nimport { scalePoint } from 'd3-scale';\n\nimport DefaultConfig from './defaultConfig';\n\nconst initState = userConfig => {\n  const config = Object.assign({}, DefaultConfig, userConfig);\n\n  if (userConfig && userConfig.dimensionTitles) {\n    console.warn(\n      'dimensionTitles passed in userConfig is deprecated. Add title to dimension object.'\n    );\n    entries(userConfig.dimensionTitles).forEach(d => {\n      if (config.dimensions[d.key]) {\n        config.dimensions[d.key].title = config.dimensions[d.key].title\n          ? config.dimensions[d.key].title\n          : d.value;\n      } else {\n        config.dimensions[d.key] = {\n          title: d.value,\n        };\n      }\n    });\n  }\n\n  const eventTypes = [\n    'render',\n    'resize',\n    'highlight',\n    'mark',\n    'brush',\n    'brushend',\n    'brushstart',\n    'axesreorder',\n  ].concat(keys(config));\n\n  const events = dispatch.apply(this, eventTypes),\n    flags = {\n      brushable: false,\n      reorderable: false,\n      axes: false,\n      interactive: false,\n      debug: false,\n    },\n    xscale = scalePoint(),\n    dragging = {},\n    axis = axisLeft().ticks(5),\n    ctx = {},\n    canvas = {};\n\n  const brush = {\n    modes: {\n      None: {\n        install: function(pc) {}, // Nothing to be done.\n        uninstall: function(pc) {}, // Nothing to be done.\n        selected: function() {\n          return [];\n        }, // Nothing to return\n        brushState: function() {\n          return {};\n        },\n      },\n    },\n    mode: 'None',\n    predicate: 'AND',\n    currentMode: function() {\n      return this.modes[this.mode];\n    },\n  };\n\n  return {\n    config,\n    events,\n    eventTypes,\n    flags,\n    xscale,\n    dragging,\n    axis,\n    ctx,\n    canvas,\n    brush,\n  };\n};\n\nexport default initState;\n", "import { select } from 'd3-selection';\n\n/**\n * Setup a new parallel coordinates chart.\n *\n * @param config\n * @param canvas\n * @param ctx\n * @returns {pc} a parcoords closure\n */\nconst init = (config, canvas, ctx) => {\n  /**\n   * Create the chart within a container. The selector can also be a d3 selection.\n   *\n   * @param selection a d3 selection\n   * @returns {pc} instance for chained api\n   */\n  const pc = function(selection) {\n    selection = pc.selection = select(selection);\n\n    config.width = selection.node().clientWidth;\n    config.height = selection.node().clientHeight;\n    // canvas data layers\n    ['dots', 'foreground', 'brushed', 'marked', 'highlight'].forEach(layer => {\n      canvas[layer] = selection\n        .append('canvas')\n        .attr('class', layer)\n        .node();\n      ctx[layer] = canvas[layer].getContext('2d');\n    });\n\n    // svg tick and brush layers\n    pc.svg = selection\n      .append('svg')\n      .attr('width', config.width)\n      .attr('height', config.height)\n      .style('font', '14px sans-serif')\n      .style('position', 'absolute')\n\n      .append('svg:g')\n      .attr(\n        'transform',\n        'translate(' + config.margin.left + ',' + config.margin.top + ')'\n      );\n    // for chained api\n    return pc;\n  };\n\n  // for partial-application style programming\n  return pc;\n};\n\nexport default init;\n", "import { scaleLinear, scaleOrdinal, scalePoint, scaleTime } from 'd3-scale';\nimport { extent } from 'd3-array';\n\nimport getRange from '../util/getRange';\nimport w from '../util/width';\nimport h from '../util/height';\n\nconst autoscale = (config, pc, xscale, ctx) =>\n  function() {\n    // yscale\n    const defaultScales = {\n      date: function(k) {\n        let _extent = extent(config.data, d => (d[k] ? d[k].getTime() : null));\n        // special case if single value\n        if (_extent[0] === _extent[1]) {\n          return scalePoint()\n            .domain(_extent)\n            .range(getRange(config));\n        }\n        if (config.flipAxes.includes(k)) {\n          _extent = _extent.map(val => tempDate.unshift(val));\n        }\n        return scaleTime()\n          .domain(_extent)\n          .range(getRange(config));\n      },\n      number: function(k) {\n        let _extent = extent(config.data, d => +d[k]);\n        // special case if single value\n        if (_extent[0] === _extent[1]) {\n          return scalePoint()\n            .domain(_extent)\n            .range(getRange(config));\n        }\n        if (config.flipAxes.includes(k)) {\n          _extent = _extent.map(val => tempDate.unshift(val));\n        }\n        return scaleLinear()\n          .domain(_extent)\n          .range(getRange(config));\n      },\n      string: function(k) {\n        let counts = {},\n          domain = [];\n        // Let's get the count for each value so that we can sort the domain based\n        // on the number of items for each value.\n        config.data.map(p => {\n          if (p[k] === undefined && config.nullValueSeparator !== 'undefined') {\n            return null; // null values will be drawn beyond the horizontal null value separator!\n          }\n          if (counts[p[k]] === undefined) {\n            counts[p[k]] = 1;\n          } else {\n            counts[p[k]] = counts[p[k]] + 1;\n          }\n        });\n        if (config.flipAxes.includes(k)) {\n          domain = Object.getOwnPropertyNames(counts).sort();\n        } else {\n          let tempArr = Object.getOwnPropertyNames(counts).sort();\n          for (let i = 0; i < Object.getOwnPropertyNames(counts).length; i++) {\n            domain.push(tempArr.pop());\n          }\n        }\n\n        //need to create an ordinal scale for categorical data\n        let categoricalRange = [];\n        if (domain.length === 1) {\n          //edge case\n          domain = [' ', domain[0], ' '];\n        }\n        let addBy = getRange(config)[0] / (domain.length - 1);\n        for (let j = 0; j < domain.length; j++) {\n          if (categoricalRange.length === 0) {\n            categoricalRange.push(0);\n            continue;\n          }\n          categoricalRange.push(categoricalRange[j - 1] + addBy);\n        }\n        return scaleOrdinal()\n          .domain(domain)\n          .range(categoricalRange);\n      },\n    };\n    Object.keys(config.dimensions).forEach(function(k) {\n      if (\n        config.dimensions[k].yscale === undefined ||\n        config.dimensions[k].yscale === null\n      ) {\n        config.dimensions[k].yscale = defaultScales[config.dimensions[k].type](\n          k\n        );\n      }\n    });\n\n    // xscale\n    // add padding for d3 >= v4 default 0.2\n    xscale.range([0, w(config)]).padding(0.2);\n\n    // Retina display, etc.\n    const devicePixelRatio = window.devicePixelRatio || 1;\n\n    // canvas sizes\n    pc.selection\n      .selectAll('canvas')\n      .style('margin-top', config.margin.top + 'px')\n      .style('margin-left', config.margin.left + 'px')\n      .style('width', w(config) + 2 + 'px')\n      .style('height', h(config) + 2 + 'px')\n      .attr('width', (w(config) + 2) * devicePixelRatio)\n      .attr('height', (h(config) + 2) * devicePixelRatio);\n    // default styles, needs to be set when canvas width changes\n    ctx.foreground.strokeStyle = config.color;\n    ctx.foreground.lineWidth = config.lineWidth;\n    ctx.foreground.globalCompositeOperation = config.composite;\n    ctx.foreground.globalAlpha = config.alpha;\n    ctx.foreground.scale(devicePixelRatio, devicePixelRatio);\n    ctx.brushed.strokeStyle = config.brushedColor;\n    ctx.brushed.lineWidth = config.lineWidth;\n    ctx.brushed.globalCompositeOperation = config.composite;\n    ctx.brushed.globalAlpha = config.alpha;\n    ctx.brushed.scale(devicePixelRatio, devicePixelRatio);\n    ctx.highlight.lineWidth = config.highlightedLineWidth;\n    ctx.highlight.scale(devicePixelRatio, devicePixelRatio);\n    ctx.marked.lineWidth = config.markedLineWidth;\n    ctx.marked.shadowColor = config.markedShadowColor;\n    ctx.marked.shadowBlur = config.markedShadowBlur;\n    ctx.marked.scale(devicePixelRatio, devicePixelRatio);\n\n    return this;\n  };\n\nexport default autoscale;\n", "const scale = (config, pc) =>\n  function(d, domain) {\n    config.dimensions[d].yscale.domain(domain);\n    pc.render.default();\n    pc.updateAxes();\n\n    return this;\n  };\n\nexport default scale;\n", "const flip = config =>\n  function(d) {\n    //__.dimensions[d].yscale.domain().reverse();                               // does not work\n    config.dimensions[d].yscale.domain(\n      config.dimensions[d].yscale.domain().reverse()\n    ); // works\n\n    return this;\n  };\n\nexport default flip;\n", "import { extent } from 'd3-array';\n\nconst commonScale = (config, pc) =>\n  function(global, type) {\n    const t = type || 'number';\n    if (typeof global === 'undefined') {\n      global = true;\n    }\n\n    // try to autodetect dimensions and create scales\n    if (!Object.keys(config.dimensions).length) {\n      pc.detectDimensions();\n    }\n    pc.autoscale();\n\n    // scales of the same type\n    const scales = Object.keys(config.dimensions).filter(\n      p => config.dimensions[p].type == t\n    );\n\n    if (global) {\n      let _extent = extent(\n        scales\n          .map(d => config.dimensions[d].yscale.domain())\n          .reduce((cur, acc) => cur.concat(acc))\n      );\n\n      scales.forEach(d => {\n        config.dimensions[d].yscale.domain(_extent);\n      });\n    } else {\n      scales.forEach(d => {\n        config.dimensions[d].yscale.domain(extent(config.data, d => +d[k]));\n      });\n    }\n\n    // update centroids\n    if (config.bundleDimension !== null) {\n      pc.bundleDimension(config.bundleDimension);\n    }\n\n    return this;\n  };\n\nexport default commonScale;\n", "const detectDimensions = pc =>\n  function() {\n    pc.dimensions(pc.applyDimensionDefaults());\n    return this;\n  };\n\nexport default detectDimensions;\n", "import { ascending } from 'd3-array';\n\nconst getOrderedDimensionKeys = config => () =>\n  Object.keys(config.dimensions).sort((x, y) =>\n    ascending(config.dimensions[x].index, config.dimensions[y].index)\n  );\n\nexport default getOrderedDimensionKeys;\n", "/**\n * Renders the polylines.\n * If no dimensions have been specified, it will attempt to detect quantitative\n * dimensions based on the first data entry. If scales haven't been set, it will\n * autoscale based on the extent for each dimension.\n *\n * @param config\n * @param pc\n * @param events\n * @returns {Function}\n */\nconst render = (config, pc, events) =>\n  function() {\n    // try to autodetect dimensions and create scales\n    if (!Object.keys(config.dimensions).length) {\n      pc.detectDimensions();\n    }\n    pc.autoscale();\n\n    pc.render[config.mode]();\n\n    events.call('render', this);\n    return this;\n  };\n\nexport default render;\n", "const computeRealCentroids = (config, position) => row =>\n  Object.keys(config.dimensions).map(d => {\n    const x = position(d);\n    const y = config.dimensions[d].yscale(row[d]);\n    return [x, y];\n  });\n\nexport default computeRealCentroids;\n", "const shadows = (flags, pc) =>\n  function() {\n    flags.shadows = true;\n    pc.alphaOnBrushed(0.1);\n    pc.render();\n    return this;\n  };\n\nexport default shadows;\n", "import { entries } from 'd3-collection';\nimport { min } from 'd3-array';\n\n//draw dots with radius r on the axis line where data intersects\nconst axisDots = (config, pc, position) => _r => {\n  const r = _r || 0.1;\n  const ctx = pc.ctx.dots;\n  const startAngle = 0;\n  const endAngle = 2 * Math.PI;\n  ctx.globalAlpha = min([1 / Math.pow(config.data.length, 1 / 2), 1]);\n  config.data.forEach(d => {\n    entries(config.dimensions).forEach((p, i) => {\n      ctx.beginPath();\n      ctx.arc(\n        position(p),\n        config.dimensions[p.key].yscale(d[p]),\n        r,\n        startAngle,\n        endAngle\n      );\n      ctx.stroke();\n      ctx.fill();\n    });\n  });\n  return this;\n};\n\nexport default axisDots;\n", "export default function(values, valueof) {\n  var n = values.length,\n      i = -1,\n      value,\n      min;\n\n  if (valueof == null) {\n    while (++i < n) { // Find the first comparable value.\n      if ((value = values[i]) != null && value >= value) {\n        min = value;\n        while (++i < n) { // Compare the remaining values.\n          if ((value = values[i]) != null && min > value) {\n            min = value;\n          }\n        }\n      }\n    }\n  }\n\n  else {\n    while (++i < n) { // Find the first comparable value.\n      if ((value = valueof(values[i], i, values)) != null && value >= value) {\n        min = value;\n        while (++i < n) { // Compare the remaining values.\n          if ((value = valueof(values[i], i, values)) != null && min > value) {\n            min = value;\n          }\n        }\n      }\n    }\n  }\n\n  return min;\n}\n", "import isBrushed from '../util/isBrushed';\nimport w from '../util/width';\nimport h from '../util/height';\n\nconst clear = (config, pc, ctx, brushGroup) =>\n  function(layer) {\n    ctx[layer].clearRect(0, 0, w(config) + 2, h(config) + 2);\n\n    // This will make sure that the foreground items are transparent\n    // without the need for changing the opacity style of the foreground canvas\n    // as this would stop the css styling from working\n    if (layer === 'brushed' && isBrushed(config, brushGroup)) {\n      ctx.brushed.fillStyle = pc.selection.style('background-color');\n      ctx.brushed.globalAlpha = 1 - config.alphaOnBrushed;\n      ctx.brushed.fillRect(0, 0, w(config) + 2, h(config) + 2);\n      ctx.brushed.globalAlpha = config.alpha;\n    }\n    return this;\n  };\n\nexport default clear;\n", "import { select } from 'd3-selection';\n\nimport dimensionLabels from '../util/dimensionLabels';\nimport flipAxisAndUpdatePCP from '../util/flipAxisAndUpdatePCP';\nimport rotateLabels from '../util/rotateLabels';\n\nimport w from '../util/width';\nimport h from '../util/height';\n\n/**\n * Create static SVG axes with dimension names, ticks, and labels.\n *\n * @param config\n * @param pc\n * @param xscale\n * @param flags\n * @param axis\n * @returns {Function}\n */\nconst createAxes = (config, pc, xscale, flags, axis) =>\n  function() {\n    if (pc.g() !== undefined) {\n      pc.removeAxes();\n    }\n    // Add a group element for each dimension.\n    pc._g = pc.svg\n      .selectAll('.dimension')\n      .data(pc.getOrderedDimensionKeys(), function(d) {\n        return d;\n      })\n      .enter()\n      .append('svg:g')\n      .attr('class', 'dimension')\n      .attr('transform', function(d) {\n        return 'translate(' + xscale(d) + ')';\n      });\n    // Add an axis and title.\n    pc._g\n      .append('svg:g')\n      .attr('class', 'axis')\n      .attr('transform', 'translate(0,0)')\n      .each(function(d) {\n        let axisElement = select(this).call(\n          pc.applyAxisConfig(axis, config.dimensions[d])\n        );\n\n        axisElement\n          .selectAll('path')\n          .style('fill', 'none')\n          .style('stroke', '#222')\n          .style('shape-rendering', 'crispEdges');\n\n        axisElement\n          .selectAll('line')\n          .style('fill', 'none')\n          .style('stroke', '#222')\n          .style('shape-rendering', 'crispEdges');\n      })\n\n      .append('svg:text')\n      .attr('text-anchor', 'middle')\n      .attr('y', 0)\n      .attr(\n        'transform',\n        'translate(0,-5) rotate(' + config.dimensionTitleRotation + ')'\n      )\n      .attr('x', 0)\n      .attr('class', 'label')\n      .text(dimensionLabels(config))\n      .on('dblclick', flipAxisAndUpdatePCP(config, pc, axis))\n      .on('wheel', rotateLabels(config, pc));\n\n    if (config.nullValueSeparator === 'top') {\n      pc.svg\n        .append('line')\n        .attr('x1', 0)\n        .attr('y1', 1 + config.nullValueSeparatorPadding.top)\n        .attr('x2', w(config))\n        .attr('y2', 1 + config.nullValueSeparatorPadding.top)\n        .attr('stroke-width', 1)\n        .attr('stroke', '#777')\n        .attr('fill', 'none')\n        .attr('shape-rendering', 'crispEdges');\n    } else if (config.nullValueSeparator === 'bottom') {\n      pc.svg\n        .append('line')\n        .attr('x1', 0)\n        .attr('y1', h(config) + 1 - config.nullValueSeparatorPadding.bottom)\n        .attr('x2', w(config))\n        .attr('y2', h(config) + 1 - config.nullValueSeparatorPadding.bottom)\n        .attr('stroke-width', 1)\n        .attr('stroke', '#777')\n        .attr('fill', 'none')\n        .attr('shape-rendering', 'crispEdges');\n    }\n\n    flags.axes = true;\n    return this;\n  };\n\nexport default createAxes;\n", "const removeAxes = pc =>\n  function() {\n    pc._g.remove();\n\n    delete pc._g;\n    return this;\n  };\n\nexport default removeAxes;\n", "import { select } from 'd3-selection';\n\nimport dimensionLabels from '../util/dimensionLabels';\nimport flipAxisAndUpdatePCP from '../util/flipAxisAndUpdatePCP';\nimport rotateLabels from '../util/rotateLabels';\n\nconst updateAxes = (config, pc, position, axis, flags) => (\n  animationTime = null\n) => {\n  if (animationTime === null) {\n    animationTime = config.animationTime;\n  }\n\n  const g_data = pc.svg\n    .selectAll('.dimension')\n    .data(pc.getOrderedDimensionKeys());\n  // Enter\n  g_data\n    .enter()\n    .append('svg:g')\n    .attr('class', 'dimension')\n    .attr('transform', p => 'translate(' + position(p) + ')')\n    .style('opacity', 0)\n    .append('svg:g')\n    .attr('class', 'axis')\n    .attr('transform', 'translate(0,0)')\n    .each(function(d) {\n      const axisElement = select(this).call(\n        pc.applyAxisConfig(axis, config.dimensions[d])\n      );\n\n      axisElement\n        .selectAll('path')\n        .style('fill', 'none')\n        .style('stroke', '#222')\n        .style('shape-rendering', 'crispEdges');\n\n      axisElement\n        .selectAll('line')\n        .style('fill', 'none')\n        .style('stroke', '#222')\n        .style('shape-rendering', 'crispEdges');\n    })\n    .append('svg:text')\n    .attr('text-anchor', 'middle')\n    .attr('class', 'label')\n    .attr('x', 0)\n    .attr('y', 0)\n    .attr(\n      'transform',\n      'translate(0,-5) rotate(' + config.dimensionTitleRotation + ')'\n    )\n    .text(dimensionLabels(config))\n    .on('dblclick', flipAxisAndUpdatePCP(config, pc, axis))\n    .on('wheel', rotateLabels(config, pc));\n\n  // Update\n  g_data.attr('opacity', 0);\n  g_data\n    .select('.axis')\n    .transition()\n    .duration(animationTime)\n    .each(function(d) {\n      select(this).call(pc.applyAxisConfig(axis, config.dimensions[d]));\n    });\n  g_data\n    .select('.label')\n    .transition()\n    .duration(animationTime)\n    .text(dimensionLabels(config))\n    .attr(\n      'transform',\n      'translate(0,-5) rotate(' + config.dimensionTitleRotation + ')'\n    );\n\n  // Exit\n  g_data.exit().remove();\n\n  const g = pc.svg.selectAll('.dimension');\n  g.transition()\n    .duration(animationTime)\n    .attr('transform', p => 'translate(' + position(p) + ')')\n    .style('opacity', 1);\n\n  pc.svg\n    .selectAll('.axis')\n    .transition()\n    .duration(animationTime)\n    .each(function(d) {\n      select(this).call(pc.applyAxisConfig(axis, config.dimensions[d]));\n    });\n\n  if (flags.brushable) pc.brushable();\n  if (flags.reorderable) pc.reorderable();\n  if (pc.brushMode() !== 'None') {\n    const mode = pc.brushMode();\n    pc.brushMode('None');\n    pc.brushMode(mode);\n  }\n  return this;\n};\n\nexport default updateAxes;\n", "import { brushSelection, brushY } from 'd3-brush';\nimport { event, select } from 'd3-selection';\n\nconst brushable = (config, pc, flags) =>\n  function() {\n    if (!pc.g()) {\n      pc.createAxes();\n    }\n\n    const g = pc.g();\n\n    // Add and store a brush for each axis.\n    g.append('svg:g')\n      .attr('class', 'brush')\n      .each(function(d) {\n        if (config.dimensions[d] !== undefined) {\n          config.dimensions[d]['brush'] = brushY(select(this)).extent([\n            [-15, 0],\n            [15, config.dimensions[d].yscale.range()[0]],\n          ]);\n          select(this).call(\n            config.dimensions[d]['brush']\n              .on('start', function() {\n                if (event.sourceEvent !== null && !event.sourceEvent.ctrlKey) {\n                  pc.brushReset();\n                }\n              })\n              .on('brush', function() {\n                if (!event.sourceEvent.ctrlKey) {\n                  pc.brush();\n                }\n              })\n              .on('end', function() {\n                // save brush selection is ctrl key is held\n                // store important brush information and\n                // the html element of the selection,\n                // to make a dummy selection element\n                if (event.sourceEvent.ctrlKey) {\n                  let html = select(this)\n                    .select('.selection')\n                    .nodes()[0].outerHTML;\n                  html = html.replace(\n                    'class=\"selection\"',\n                    'class=\"selection dummy' +\n                      ' selection-' +\n                      config.brushes.length +\n                      '\"'\n                  );\n                  let dat = select(this).nodes()[0].__data__;\n                  let brush = {\n                    id: config.brushes.length,\n                    extent: brushSelection(this),\n                    html: html,\n                    data: dat,\n                  };\n                  config.brushes.push(brush);\n                  select(select(this).nodes()[0].parentNode)\n                    .select('.axis')\n                    .nodes()[0].outerHTML += html;\n                  pc.brush();\n                  config.dimensions[d].brush.move(select(this, null));\n                  select(this)\n                    .select('.selection')\n                    .attr('style', 'display:none');\n                  pc.brushable();\n                } else {\n                  pc.brush();\n                }\n              })\n          );\n          select(this).on('dblclick', function() {\n            pc.brushReset(d);\n          });\n        }\n      });\n\n    flags.brushable = true;\n    return this;\n  };\n\nexport default brushable;\n", "import { select } from 'd3-selection';\n\nconst brushReset = (config, pc) =>\n  function(dimension) {\n    const brushesToKeep = [];\n    for (let j = 0; j < config.brushes.length; j++) {\n      if (config.brushes[j].data !== dimension) {\n        brushesToKeep.push(config.brushes[j]);\n      }\n    }\n\n    config.brushes = brushesToKeep;\n    config.brushed = false;\n\n    if (pc.g() !== undefined) {\n      const nodes = pc\n        .g()\n        .selectAll('.brush')\n        .nodes();\n      for (let i = 0; i < nodes.length; i++) {\n        if (nodes[i].__data__ === dimension) {\n          // remove all dummy brushes for this axis or the real brush\n          select(select(nodes[i]).nodes()[0].parentNode)\n            .selectAll('.dummy')\n            .remove();\n          config.dimensions[dimension].brush.move(select(nodes[i], null));\n        }\n      }\n    }\n\n    return this;\n  };\n\nexport default brushReset;\n", "import { brushSelection } from 'd3-brush';\n\nconst selected = (config, pc) => () => {\n  let actives = [];\n  let extents = [];\n  let ranges = {};\n  //get brush selections from each node, convert to actual values\n  //invert order of values in array to comply with the parcoords architecture\n  if (config.brushes.length === 0) {\n    let nodes = pc\n      .g()\n      .selectAll('.brush')\n      .nodes();\n    for (let k = 0; k < nodes.length; k++) {\n      if (brushSelection(nodes[k]) !== null) {\n        actives.push(nodes[k].__data__);\n        let values = [];\n        let ranger = brushSelection(nodes[k]);\n        if (\n          typeof config.dimensions[nodes[k].__data__].yscale.domain()[0] ===\n          'number'\n        ) {\n          for (let i = 0; i < ranger.length; i++) {\n            if (\n              actives.includes(nodes[k].__data__) &&\n              config.flipAxes.includes(nodes[k].__data__)\n            ) {\n              values.push(\n                config.dimensions[nodes[k].__data__].yscale.invert(ranger[i])\n              );\n            } else if (config.dimensions[nodes[k].__data__].yscale() !== 1) {\n              values.unshift(\n                config.dimensions[nodes[k].__data__].yscale.invert(ranger[i])\n              );\n            }\n          }\n          extents.push(values);\n          for (let ii = 0; ii < extents.length; ii++) {\n            if (extents[ii].length === 0) {\n              extents[ii] = [1, 1];\n            }\n          }\n        } else {\n          ranges[nodes[k].__data__] = brushSelection(nodes[k]);\n          let dimRange = config.dimensions[nodes[k].__data__].yscale.range();\n          let dimDomain = config.dimensions[nodes[k].__data__].yscale.domain();\n          for (let j = 0; j < dimRange.length; j++) {\n            if (\n              dimRange[j] >= ranger[0] &&\n              dimRange[j] <= ranger[1] &&\n              actives.includes(nodes[k].__data__) &&\n              config.flipAxes.includes(nodes[k].__data__)\n            ) {\n              values.push(dimRange[j]);\n            } else if (dimRange[j] >= ranger[0] && dimRange[j] <= ranger[1]) {\n              values.unshift(dimRange[j]);\n            }\n          }\n          extents.push(values);\n          for (let ii = 0; ii < extents.length; ii++) {\n            if (extents[ii].length === 0) {\n              extents[ii] = [1, 1];\n            }\n          }\n        }\n      }\n    }\n    // test if within range\n    const within = {\n      date: function(d, p, dimension) {\n        let category = d[p];\n        let categoryIndex = config.dimensions[p].yscale\n          .domain()\n          .indexOf(category);\n        let categoryRangeValue = config.dimensions[p].yscale.range()[\n          categoryIndex\n        ];\n        return (\n          categoryRangeValue >= ranges[p][0] &&\n          categoryRangeValue <= ranges[p][1]\n        );\n      },\n      number: function(d, p, dimension) {\n        return extents[dimension][0] <= d[p] && d[p] <= extents[dimension][1];\n      },\n      string: function(d, p, dimension) {\n        let category = d[p];\n        let categoryIndex = config.dimensions[p].yscale\n          .domain()\n          .indexOf(category);\n        let categoryRangeValue = config.dimensions[p].yscale.range()[\n          categoryIndex\n        ];\n        return (\n          categoryRangeValue >= ranges[p][0] &&\n          categoryRangeValue <= ranges[p][1]\n        );\n      },\n    };\n    return config.data.filter(d =>\n      actives.every((p, dimension) =>\n        within[config.dimensions[p].type](d, p, dimension)\n      )\n    );\n  } else {\n    // need to get data from each brush instead of each axis\n    // first must find active axes by iterating through all brushes\n    // then go through similiar process as above.\n    let multiBrushData = [];\n    for (let idx = 0; idx < config.brushes.length; idx++) {\n      let brush = config.brushes[idx];\n      let values = [];\n      let ranger = brush.extent;\n      let actives = [brush.data];\n      if (\n        typeof config.dimensions[brush.data].yscale.domain()[0] === 'number'\n      ) {\n        for (let i = 0; i < ranger.length; i++) {\n          if (\n            actives.includes(brush.data) &&\n            config.flipAxes.includes(brush.data)\n          ) {\n            values.push(config.dimensions[brush.data].yscale.invert(ranger[i]));\n          } else if (config.dimensions[brush.data].yscale() !== 1) {\n            values.unshift(\n              config.dimensions[brush.data].yscale.invert(ranger[i])\n            );\n          }\n        }\n        extents.push(values);\n        for (let ii = 0; ii < extents.length; ii++) {\n          if (extents[ii].length === 0) {\n            extents[ii] = [1, 1];\n          }\n        }\n      } else {\n        ranges[brush.data] = brush.extent;\n        let dimRange = config.dimensions[brush.data].yscale.range();\n        let dimDomain = config.dimensions[brush.data].yscale.domain();\n        for (let j = 0; j < dimRange.length; j++) {\n          if (\n            dimRange[j] >= ranger[0] &&\n            dimRange[j] <= ranger[1] &&\n            actives.includes(brush.data) &&\n            config.flipAxes.includes(brush.data)\n          ) {\n            values.push(dimRange[j]);\n          } else if (dimRange[j] >= ranger[0] && dimRange[j] <= ranger[1]) {\n            values.unshift(dimRange[j]);\n          }\n        }\n        extents.push(values);\n        for (let ii = 0; ii < extents.length; ii++) {\n          if (extents[ii].length === 0) {\n            extents[ii] = [1, 1];\n          }\n        }\n      }\n      let within = {\n        date: function(d, p, dimension) {\n          let category = d[p];\n          let categoryIndex = config.dimensions[p].yscale\n            .domain()\n            .indexOf(category);\n          let categoryRangeValue = config.dimensions[p].yscale.range()[\n            categoryIndex\n          ];\n          return (\n            categoryRangeValue >= ranges[p][0] &&\n            categoryRangeValue <= ranges[p][1]\n          );\n        },\n        number: function(d, p, dimension) {\n          return extents[idx][0] <= d[p] && d[p] <= extents[idx][1];\n        },\n        string: function(d, p, dimension) {\n          let category = d[p];\n          let categoryIndex = config.dimensions[p].yscale\n            .domain()\n            .indexOf(category);\n          let categoryRangeValue = config.dimensions[p].yscale.range()[\n            categoryIndex\n          ];\n          return (\n            categoryRangeValue >= ranges[p][0] &&\n            categoryRangeValue <= ranges[p][1]\n          );\n        },\n      };\n\n      // filter data, but instead of returning it now,\n      // put it into multiBrush data which is returned after\n      // all brushes are iterated through.\n      let filtered = config.data.filter(d =>\n        actives.every((p, dimension) =>\n          within[config.dimensions[p].type](d, p, dimension)\n        )\n      );\n      for (let z = 0; z < filtered.length; z++) {\n        multiBrushData.push(filtered[z]);\n      }\n      actives = [];\n      ranges = {};\n    }\n    return multiBrushData;\n  }\n};\n\nexport default selected;\n", "import { drag } from 'd3-drag';\nimport { event, select } from 'd3-selection';\n\nimport w from '../util/width';\n\n// <PERSON>, http://bl.ocks.org/1341281\nconst reorderable = (config, pc, xscale, position, dragging, flags) =>\n  function() {\n    if (pc.g() === undefined) pc.createAxes();\n    const g = pc.g();\n\n    g.style('cursor', 'move').call(\n      drag()\n        .on('start', function(d) {\n          dragging[d] = this.__origin__ = xscale(d);\n        })\n        .on('drag', function(d) {\n          dragging[d] = Math.min(\n            w(config),\n            Math.max(0, (this.__origin__ += event.dx))\n          );\n          pc.sortDimensions();\n          xscale.domain(pc.getOrderedDimensionKeys());\n          pc.render();\n          g.attr('transform', d => 'translate(' + position(d) + ')');\n        })\n        .on('end', function(d) {\n          delete this.__origin__;\n          delete dragging[d];\n          select(this)\n            .transition()\n            .attr('transform', 'translate(' + xscale(d) + ')');\n          pc.render();\n          pc.renderMarked();\n        })\n    );\n    flags.reorderable = true;\n    return this;\n  };\n\nexport default reorderable;\n", "// Reorder dimensions, such that the highest value (visually) is on the left and\n// the lowest on the right. Visual values are determined by the data values in\n// the given row.\nconst reorder = (config, pc, xscale) => rowdata => {\n  const firstDim = pc.getOrderedDimensionKeys()[0];\n\n  pc.sortDimensionsByRowData(rowdata);\n  // NOTE: this is relatively cheap given that:\n  // number of dimensions < number of data items\n  // Thus we check equality of order to prevent rerendering when this is the case.\n  const reordered = firstDim !== pc.getOrderedDimensionKeys()[0];\n\n  if (reordered) {\n    xscale.domain(pc.getOrderedDimensionKeys());\n    const highlighted = config.highlighted.slice(0);\n    pc.unhighlight();\n\n    const marked = config.marked.slice(0);\n    pc.unmark();\n\n    const g = pc.g();\n    g.transition()\n      .duration(1500)\n      .attr('transform', d => 'translate(' + xscale(d) + ')');\n    pc.render();\n\n    // pc.highlight() does not check whether highlighted is length zero, so we do that here.\n    if (highlighted.length !== 0) {\n      pc.highlight(highlighted);\n    }\n    if (marked.length !== 0) {\n      pc.mark(marked);\n    }\n  }\n};\n\nexport default reorder;\n", "const sortDimensionsByRowData = config => rowdata => {\n  const copy = Object.assign({}, config.dimensions);\n  const positionSortedKeys = Object.keys(config.dimensions).sort((a, b) => {\n    const pixelDifference =\n      config.dimensions[a].yscale(rowdata[a]) -\n      config.dimensions[b].yscale(rowdata[b]);\n\n    // Array.sort is not necessarily stable, this means that if pixelDifference is zero\n    // the ordering of dimensions might change unexpectedly. This is solved by sorting on\n    // variable name in that case.\n    return pixelDifference === 0 ? a.localeCompare(b) : pixelDifference;\n  });\n  config.dimensions = {};\n  positionSortedKeys.forEach((p, i) => {\n    config.dimensions[p] = copy[p];\n    config.dimensions[p].index = i;\n  });\n};\n\nexport default sortDimensionsByRowData;\n", "const sortDimensions = (config, position) => () => {\n  const copy = Object.assign({}, config.dimensions);\n  const positionSortedKeys = Object.keys(config.dimensions).sort(\n    (a, b) => (position(a) - position(b) === 0 ? 1 : position(a) - position(b))\n  );\n  config.dimensions = {};\n  positionSortedKeys.forEach((p, i) => {\n    config.dimensions[p] = copy[p];\n    config.dimensions[p].index = i;\n  });\n};\n\nexport default sortDimensions;\n", "const interactive = flags =>\n  function() {\n    flags.interactive = true;\n    return this;\n  };\n\nexport default interactive;\n", "// rescale for height, width and margins\n// TODO currently assumes chart is brushable, and destroys old brushes\nconst resize = (config, pc, flags, events) => {\n  return function() {\n    // selection size\n    pc.selection\n      .select('svg')\n      .attr('width', config.width)\n      .attr('height', config.height);\n    pc.svg.attr(\n      'transform',\n      'translate(' + config.margin.left + ',' + config.margin.top + ')'\n    );\n\n    // FIXME: the current brush state should pass through\n    if (flags.brushable) pc.brushReset();\n\n    // scales\n    pc.autoscale();\n\n    // axes, destroys old brushes.\n    if (pc.g()) pc.createAxes();\n    if (flags.brushable) pc.brushable();\n    if (flags.reorderable) pc.reorderable();\n\n    events.call('resize', this, {\n      width: config.width,\n      height: config.height,\n      margin: config.margin,\n    });\n\n    return this;\n  };\n};\n\nexport default resize;\n", "import { selectAll } from 'd3-selection';\n\n// clear highlighting\nconst unhighlight = (config, pc, canvas) =>\n  function() {\n    config.highlighted = [];\n    pc.clear('highlight');\n    selectAll([canvas.foreground, canvas.brushed]).classed('faded', false);\n    return this;\n  };\n\nexport default unhighlight;\n", "import { selectAll } from 'd3-selection';\n\nimport { pathMark } from './renderMarked';\n\n// mark an array of data\nconst mark = (config, pc, canvas, events, ctx, position) =>\n  function(data = null) {\n    if (data === null) {\n      return config.marked;\n    }\n\n    // add array to already marked data\n    config.marked = config.marked.concat(data);\n    selectAll([canvas.foreground, canvas.brushed]).classed('dimmed', true);\n    data.forEach(pathMark(config, ctx, position));\n    events.call('mark', this, data);\n    return this;\n  };\n\nexport default mark;\n", "import { selectAll } from 'd3-selection';\n\n// clear marked data arrays\nconst unmark = (config, pc, canvas) =>\n  function() {\n    config.marked = [];\n    pc.clear('marked');\n    selectAll([canvas.foreground, canvas.brushed]).classed('dimmed', false);\n    return this;\n  };\n\nexport default unmark;\n", "import { select, selectAll } from 'd3-selection';\n\n// Merges the canvases and SVG elements into one canvas element which is then passed into the callback\n// (so you can choose to save it to disk, etc.)\nconst mergeParcoords = pc => callback => {\n  // Retina display, etc.\n  const devicePixelRatio = window.devicePixelRatio || 1;\n\n  // Create a canvas element to store the merged canvases\n  const mergedCanvas = document.createElement('canvas');\n\n  const foregroundCanvas = pc.canvas.foreground;\n  // We will need to adjust for canvas margins to align the svg and canvas\n  const canvasMarginLeft = Number(\n    foregroundCanvas.style.marginLeft.replace('px', '')\n  );\n\n  const textTopAdjust = 15;\n  const canvasMarginTop =\n    Number(foregroundCanvas.style.marginTop.replace('px', '')) + textTopAdjust;\n  const width =\n    (foregroundCanvas.clientWidth + canvasMarginLeft) * devicePixelRatio;\n  const height =\n    (foregroundCanvas.clientHeight + canvasMarginTop) * devicePixelRatio;\n  mergedCanvas.width = width + 50; // pad so that svg labels at right will not get cut off\n  mergedCanvas.height = height + 30; // pad so that svg labels at bottom will not get cut off\n  mergedCanvas.style.width = mergedCanvas.width / devicePixelRatio + 'px';\n  mergedCanvas.style.height = mergedCanvas.height / devicePixelRatio + 'px';\n\n  // Give the canvas a white background\n  const context = mergedCanvas.getContext('2d');\n  context.fillStyle = '#ffffff';\n  context.fillRect(0, 0, mergedCanvas.width, mergedCanvas.height);\n\n  // Merge all the canvases\n  for (const key in pc.canvas) {\n    context.drawImage(\n      pc.canvas[key],\n      canvasMarginLeft * devicePixelRatio,\n      canvasMarginTop * devicePixelRatio,\n      width - canvasMarginLeft * devicePixelRatio,\n      height - canvasMarginTop * devicePixelRatio\n    );\n  }\n\n  // Add SVG elements to canvas\n  const DOMURL = window.URL || window.webkitURL || window;\n  const serializer = new XMLSerializer();\n  // axis labels are translated (0,-5) so we will clone the svg\n  //   and translate down so the labels are drawn on the canvas\n  const svgNodeCopy = pc.selection\n    .select('svg')\n    .node()\n    .cloneNode(true);\n  svgNodeCopy.setAttribute('transform', 'translate(0,' + textTopAdjust + ')');\n  svgNodeCopy.setAttribute(\n    'height',\n    svgNodeCopy.getAttribute('height') + textTopAdjust\n  );\n  // text will need fill attribute since css styles will not get picked up\n  //   this is not sophisticated since it doesn't look up css styles\n  //   if the user changes\n  select(svgNodeCopy)\n    .selectAll('text')\n    .attr('fill', 'black');\n  const svgStr = serializer.serializeToString(svgNodeCopy);\n\n  // Create a Data URI.\n  const src = 'data:image/svg+xml;base64,' + window.btoa(svgStr);\n  const img = new Image();\n  img.onload = () => {\n    context.drawImage(\n      img,\n      0,\n      0,\n      img.width * devicePixelRatio,\n      img.height * devicePixelRatio\n    );\n    if (typeof callback === 'function') {\n      callback(mergedCanvas);\n    }\n  };\n  img.src = src;\n};\n\nexport default mergeParcoords;\n", "import brushExtents from './brushExtents';\nimport install from './install';\nimport selected from './selected';\nimport uninstall from './uninstall';\n\nconst install1DAxes = (brushGroup, config, pc, events) => {\n  const state = {\n    brushes: {},\n    brushNodes: {},\n  };\n\n  brushGroup.modes['1D-axes'] = {\n    install: install(state, config, pc, events, brushGroup),\n    uninstall: uninstall(state, pc),\n    selected: selected(state, config, brushGroup),\n    brushState: brushExtents(state, config, pc),\n  };\n};\n\nexport default install1DAxes;\n", "import uninstall from './uninstall';\nimport install from './install';\nimport selected from './selected';\n\nconst install2DStrums = (brushGroup, config, pc, events, xscale) => {\n  const state = {\n    strums: {},\n    strumRect: {},\n  };\n\n  brushGroup.modes['2D-strums'] = {\n    install: install(brushGroup, state, config, pc, events, xscale),\n    uninstall: uninstall(state, pc),\n    selected: selected(brushGroup, state, config),\n    brushState: () => state.strums,\n  };\n};\n\nexport default install2DStrums;\n", "import uninstall from './uninstall';\nimport install from './install';\nimport selected from './selected';\n\nconst installAngularBrush = (brushGroup, config, pc, events, xscale) => {\n  const state = {\n    arcs: {},\n    strumRect: {},\n  };\n\n  brushGroup.modes['angular'] = {\n    install: install(brushGroup, state, config, pc, events, xscale),\n    uninstall: uninstall(state, pc),\n    selected: selected(brushGroup, state, config),\n    brushState: () => state.arcs,\n  };\n};\n\nexport default installAngularBrush;\n", "import brushExtents from './brushExtents';\nimport install from './install';\nimport selected from './selected';\nimport uninstall from './uninstall';\n\nconst install1DMultiAxes = (brushGroup, config, pc, events) => {\n  const state = {\n    brushes: {},\n    brushNodes: {},\n  };\n\n  brushGroup.modes['1D-axes-multi'] = {\n    install: install(state, config, pc, events, brushGroup),\n    uninstall: uninstall(state, pc),\n    selected: selected(state, config, brushGroup),\n    brushState: brushExtents(state, config, pc),\n  };\n};\n\nexport default install1DMultiAxes;\n", "// this descriptive text should live with other introspective methods\nconst toString = config => () =>\n  'Parallel Coordinates: ' +\n  Object.keys(config.dimensions).length +\n  ' dimensions (' +\n  Object.keys(config.data[0]).length +\n  ' total) , ' +\n  config.data.length +\n  ' rows';\n\nexport default toString;\n", "export default function(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "import ascending from \"./ascending\";\n\nexport default function(compare) {\n  if (compare.length === 1) compare = ascendingComparator(compare);\n  return {\n    left: function(a, x, lo, hi) {\n      if (lo == null) lo = 0;\n      if (hi == null) hi = a.length;\n      while (lo < hi) {\n        var mid = lo + hi >>> 1;\n        if (compare(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      }\n      return lo;\n    },\n    right: function(a, x, lo, hi) {\n      if (lo == null) lo = 0;\n      if (hi == null) hi = a.length;\n      while (lo < hi) {\n        var mid = lo + hi >>> 1;\n        if (compare(a[mid], x) > 0) hi = mid;\n        else lo = mid + 1;\n      }\n      return lo;\n    }\n  };\n}\n\nfunction ascendingComparator(f) {\n  return function(d, x) {\n    return ascending(f(d), x);\n  };\n}\n", "import ascending from \"./ascending\";\nimport bisector from \"./bisector\";\n\nvar ascendingBisect = bisector(ascending);\nexport var bisectRight = ascendingBisect.right;\nexport var bisectLeft = ascendingBisect.left;\nexport default bisectRight;\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "var e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nexport default function(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    start = Math.ceil(start / step);\n    stop = Math.floor(stop / step);\n    ticks = new Array(n = Math.ceil(stop - start + 1));\n    while (++i < n) ticks[i] = (start + i) * step;\n  } else {\n    start = Math.floor(start * step);\n    stop = Math.ceil(stop * step);\n    ticks = new Array(n = Math.ceil(start - stop + 1));\n    while (++i < n) ticks[i] = (start - i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0\n      ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)\n      : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nexport function tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;\n  else if (error >= e5) step1 *= 5;\n  else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}\n", "export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.interpolator(domain); break;\n    default: this.interpolator(interpolator).domain(domain); break;\n  }\n  return this;\n}\n", "var array = Array.prototype;\n\nexport var map = array.map;\nexport var slice = array.slice;\n", "import {map} from \"d3-collection\";\nimport {slice} from \"./array\";\nimport {initRange} from \"./init\";\n\nexport var implicit = {name: \"implicit\"};\n\nexport default function ordinal() {\n  var index = map(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    var key = d + \"\", i = index.get(key);\n    if (!i) {\n      if (unknown !== implicit) return unknown;\n      index.set(key, i = domain.push(d));\n    }\n    return range[(i - 1) % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = map();\n    var i = -1, n = _.length, d, key;\n    while (++i < n) if (!index.has(key = (d = _[i]) + \"\")) index.set(key, domain.push(d));\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = slice.call(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n", "import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init\";\nimport ordinal from \"./ordinal\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      range = [0, 1],\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = range[1] < range[0],\n        start = range[reverse - 0],\n        stop = range[1 - reverse];\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = [+_[0], +_[1]], rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = [+_[0], +_[1]], round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), range)\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "export default function(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "export default function(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport {map, slice} from \"./array\";\nimport constant from \"./constant\";\nimport number from \"./number\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(domain) {\n  var a = domain[0], b = domain[domain.length - 1], t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    piecewise = Math.min(domain.length, range.length) > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = map.call(_, number), clamp === identity || (clamp = clamper(domain)), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = slice.call(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = slice.call(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? clamper(domain) : identity, scale) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous(transform, untransform) {\n  return transformer()(transform, untransform);\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy, identity} from \"./continuous\";\nimport {initRange} from \"./init\";\nimport tickFormat from \"./tickFormat\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain(),\n        i0 = 0,\n        i1 = d.length - 1,\n        start = d[i0],\n        stop = d[i1],\n        step;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n\n    step = tickIncrement(start, stop, count);\n\n    if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n      step = tickIncrement(start, stop, count);\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n      step = tickIncrement(start, stop, count);\n    }\n\n    if (step > 0) {\n      d[i0] = Math.floor(start / step) * step;\n      d[i1] = Math.ceil(stop / step) * step;\n      domain(d);\n    } else if (step < 0) {\n      d[i0] = Math.ceil(start * step) / step;\n      d[i1] = Math.floor(stop * step) / step;\n      domain(d);\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous(identity, identity);\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {bisector, tickStep} from \"d3-array\";\nimport {timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeMillisecond} from \"d3-time\";\nimport {timeFormat} from \"d3-time-format\";\nimport {map} from \"./array\";\nimport continuous, {copy, identity} from \"./continuous\";\nimport {initRange} from \"./init\";\nimport nice from \"./nice\";\n\nvar durationSecond = 1000,\n    durationMinute = durationSecond * 60,\n    durationHour = durationMinute * 60,\n    durationDay = durationHour * 24,\n    durationWeek = durationDay * 7,\n    durationMonth = durationDay * 30,\n    durationYear = durationDay * 365;\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nexport function calendar(year, month, week, day, hour, minute, second, millisecond, format) {\n  var scale = continuous(identity, identity),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  var tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  function tickInterval(interval, start, stop, step) {\n    if (interval == null) interval = 10;\n\n    // If a desired tick count is specified, pick a reasonable tick interval\n    // based on the extent of the domain and a rough estimate of tick size.\n    // Otherwise, assume interval is already a time interval and use it.\n    if (typeof interval === \"number\") {\n      var target = Math.abs(stop - start) / interval,\n          i = bisector(function(i) { return i[2]; }).right(tickIntervals, target);\n      if (i === tickIntervals.length) {\n        step = tickStep(start / durationYear, stop / durationYear, interval);\n        interval = year;\n      } else if (i) {\n        i = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n        step = i[1];\n        interval = i[0];\n      } else {\n        step = Math.max(tickStep(start, stop, interval), 1);\n        interval = millisecond;\n      }\n    }\n\n    return step == null ? interval : interval.every(step);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(map.call(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval, step) {\n    var d = domain(),\n        t0 = d[0],\n        t1 = d[d.length - 1],\n        r = t1 < t0,\n        t;\n    if (r) t = t0, t0 = t1, t1 = t;\n    t = tickInterval(interval, t0, t1, step);\n    t = t ? t.range(t0, t1 + 1) : []; // inclusive stop\n    return r ? t.reverse() : t;\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval, step) {\n    var d = domain();\n    return (interval = tickInterval(interval, d[0], d[d.length - 1], step))\n        ? domain(nice(d, interval))\n        : scale;\n  };\n\n  scale.copy = function() {\n    return copy(scale, calendar(year, month, week, day, hour, minute, second, millisecond, format));\n  };\n\n  return scale;\n}\n\nexport default function() {\n  return initRange.apply(calendar(timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeMillisecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n", "export default function(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n", "import {identity} from \"./continuous\";\nimport {initInterpolator} from \"./init\";\nimport {linearish} from \"./linear\";\nimport {loggish} from \"./log\";\nimport {symlogish} from \"./symlog\";\nimport {powish} from \"./pow\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (t0 = transform(x0 = +_[0]), t1 = transform(x1 = +_[1]), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport default function sequential() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialLog() {\n  var scale = loggish(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n"], "names": ["global", "window", "requestAnimationFrame", "webkitRequestAnimationFrame", "cancelAnimationFrame", "lastTime", "callback", "currTime", "Date", "getTime", "timeToCall", "Math", "max", "id", "setTimeout", "clearTimeout", "define", "none", "selector", "this", "querySelector", "empty", "querySelectorAll", "matches", "update", "Array", "length", "EnterNode", "parent", "datum", "ownerDocument", "namespaceURI", "_next", "_parent", "__data__", "prototype", "constructor", "append<PERSON><PERSON><PERSON>", "child", "insertBefore", "next", "bindIndex", "group", "enter", "exit", "data", "node", "i", "groupLength", "dataLength", "<PERSON><PERSON><PERSON>", "key", "keyValue", "nodeByKeyValue", "keyV<PERSON><PERSON>", "call", "ascending", "a", "b", "NaN", "xhtml", "svg", "xlink", "xml", "xmlns", "name", "prefix", "indexOf", "slice", "namespaces", "hasOwnProperty", "space", "local", "attrRemove", "removeAttribute", "attrRemoveNS", "fullname", "removeAttributeNS", "attrConstant", "value", "setAttribute", "attrConstantNS", "setAttributeNS", "attrFunction", "v", "apply", "arguments", "attrFunctionNS", "defaultView", "document", "styleRemove", "style", "removeProperty", "styleConstant", "priority", "setProperty", "styleFunction", "styleValue", "getPropertyValue", "getComputedStyle", "propertyRemove", "propertyConstant", "propertyFunction", "classArray", "string", "trim", "split", "classList", "ClassList", "_node", "_names", "getAttribute", "classedAdd", "names", "list", "n", "add", "classedRemove", "remove", "classedTrue", "classedFalse", "classedFunction", "textRemove", "textContent", "textConstant", "textFunction", "htmlRemove", "innerHTML", "htmlConstant", "htmlFunction", "raise", "nextS<PERSON>ling", "parentNode", "lower", "previousSibling", "<PERSON><PERSON><PERSON><PERSON>", "creator<PERSON><PERSON><PERSON><PERSON>", "uri", "documentElement", "createElement", "createElementNS", "creatorFixed", "namespace", "constant<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "selection_cloneShallow", "clone", "cloneNode", "selection_cloneDeep", "push", "join", "splice", "contains", "filterEvents", "event", "mouseenter", "mouseleave", "filterContextListener", "listener", "index", "contextListener", "related", "relatedTarget", "compareDocumentPosition", "event1", "event0", "onRemove", "typename", "on", "__on", "o", "j", "m", "type", "removeEventListener", "capture", "onAdd", "wrap", "d", "addEventListener", "customEvent", "that", "args", "sourceEvent", "dispatchEvent", "params", "CustomEvent", "createEvent", "initEvent", "bubbles", "cancelable", "detail", "dispatchConstant", "dispatchFunction", "root", "Selection", "groups", "parents", "_groups", "_parents", "selection", "select", "subgroups", "subnode", "subgroup", "selectAll", "selectorAll", "filter", "match", "matcher", "size", "each", "x", "bind", "enterGroup", "updateGroup", "previous", "i0", "i1", "_enter", "_exit", "map", "sparse", "onenter", "onupdate", "onexit", "append", "merge", "order", "groups0", "groups1", "m0", "m1", "min", "merges", "group0", "group1", "sort", "compare", "compareNode", "sortgroups", "sortgroup", "nodes", "attr", "getAttributeNS", "property", "classed", "text", "html", "create", "creator", "insert", "before", "deep", "t", "typenames", "parseTypenames", "dispatch", "source", "current", "ownerSVGElement", "createSVGPoint", "point", "clientX", "y", "clientY", "matrixTransform", "getScreenCTM", "inverse", "rect", "getBoundingClientRect", "left", "clientLeft", "top", "clientTop", "changedTouches", "nopropagation", "stopImmediatePropagation", "preventDefault", "view", "noevent", "__noselect", "MozUserSelect", "yesdrag", "noclick", "touches", "identifier", "touch", "noop", "_", "test", "Error", "Dispatch", "get", "c", "set", "concat", "types", "T", "copy", "taskHead", "taskTail", "frame", "timeout", "interval", "clockLast", "clockNow", "clockSkew", "clock", "performance", "now", "set<PERSON>rame", "f", "clearNow", "Timer", "_call", "_time", "timer", "delay", "time", "restart", "wake", "e", "undefined", "timer<PERSON><PERSON><PERSON>", "t0", "t2", "t1", "Infinity", "sleep", "nap", "poke", "clearInterval", "setInterval", "elapsed", "stop", "TypeError", "emptyOn", "emptyTween", "timing", "schedules", "__transition", "self", "tween", "schedule", "state", "start", "tick", "duration", "ease", "init", "active", "svgNode", "degrees", "PI", "identity", "translateX", "translateY", "rotate", "skewX", "scaleX", "scaleY", "sqrt", "atan2", "atan", "interpolateTransform", "parse", "pxComma", "pxParen", "degParen", "pop", "s", "q", "xa", "ya", "xb", "yb", "number", "translate", "scale", "interpolateTransformCss", "DOMMatrix", "WebKitCSSMatrix", "isIdentity", "decompose", "interpolateTransformSvg", "transform", "baseVal", "consolidate", "matrix", "tweenRemove", "tween0", "tween1", "tweenFunction", "tweenValue", "transition", "_id", "factory", "extend", "definition", "Object", "Color", "darker", "brighter", "reI", "reN", "reP", "reHex", "reRgbInteger", "RegExp", "reRgbPercent", "reRgbaInteger", "reRgbaPercent", "reHslPercent", "reHslaPercent", "named", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "color_formatHex", "rgb", "formatHex", "color_formatRgb", "formatRgb", "color", "format", "l", "toLowerCase", "exec", "parseInt", "rgbn", "Rgb", "rgba", "hsla", "r", "g", "opacity", "rgb_formatHex", "hex", "rgb_formatRgb", "clampa", "clampi", "isNaN", "round", "toString", "h", "Hsl", "hslConvert", "clamph", "clampt", "hsl2rgb", "m2", "basis", "v0", "v1", "v2", "v3", "t3", "channels", "assign", "displayable", "formatHex8", "formatHsl", "k", "pow", "clamp", "linear", "gamma", "nogamma", "exponential", "constant", "rgbGamma", "end", "colorRgb", "rgbSpline", "spline", "colors", "values", "floor", "reA", "reB", "am", "bm", "bs", "bi", "lastIndex", "one", "zero", "interpolateNumber", "interpolateRgb", "interpolateString", "interpolate", "value1", "string00", "interpolate0", "string1", "string0", "string10", "attrTweenNS", "attrInterpolateNS", "_value", "attrTween", "attrInterpolate", "delayFunction", "delayConstant", "durationFunction", "durationConstant", "Transition", "_name", "newId", "selection_prototype", "children", "inherit", "<PERSON><PERSON><PERSON><PERSON>", "select<PERSON><PERSON><PERSON><PERSON>", "id0", "id1", "on0", "on1", "sit", "every", "onFunction", "styleTween", "styleNull", "listener0", "styleMaybeRemove", "styleInterpolate", "textTween", "textInterpolate", "removeFunction", "easeConstant", "easeVarying", "Promise", "resolve", "reject", "cancel", "interrupt", "Symbol", "iterator", "defaultTiming", "target", "MODE_DRAG", "MODE_SPACE", "MODE_HANDLE", "MODE_CENTER", "number1", "number2", "X", "handles", "input", "output", "xy", "Y", "cursors", "overlay", "w", "nw", "ne", "se", "sw", "flipX", "flipY", "signsX", "signsY", "defaultFilter", "ctrl<PERSON>ey", "button", "defaultExtent", "hasAttribute", "viewBox", "width", "height", "defaultTouchable", "navigator", "maxTouchPoints", "__brush", "brushSelection", "dim", "brushY", "brush", "touchending", "extent", "touchable", "keys", "listeners", "handleSize", "initialize", "handle", "redraw", "started", "touchmoved", "touchended", "emitter", "clean", "emit", "Emitter", "w0", "w1", "n0", "n1", "e0", "e1", "s0", "s1", "moving", "lockX", "lockY", "mode", "metaKey", "altKey", "signX", "signY", "W", "N", "E", "S", "dx", "dy", "shifting", "shift<PERSON>ey", "pointer", "mouse", "point0", "beforestart", "moved", "ended", "keyCode", "move", "dragDisable", "point1", "abs", "dragEnable", "selection0", "selection1", "clear", "starting", "BrushEvent", "keyModifiers", "DragEvent", "subject", "defaultContainer", "defaultSubject", "mousedownx", "mousedowny", "mousemoving", "container", "gestures", "clickDistance2", "drag", "mousedowned", "touchstarted", "gesture", "mousemoved", "mouseupped", "nodrag", "p", "sublisteners", "p0", "clickDistance", "cos", "sin", "epsilon", "pi", "halfPi", "tau", "asin", "arcInnerRadius", "innerRadius", "arcOuterRadius", "outerRadius", "arcStartAngle", "startAngle", "arcEndAngle", "endAngle", "arcPadAngle", "padAngle", "cornerTangents", "x0", "y0", "x1", "y1", "r1", "rc", "cw", "x01", "y01", "lo", "ox", "oy", "x11", "y11", "x10", "y10", "x00", "y00", "d2", "D", "cx0", "cy0", "cx1", "cy1", "dx0", "dy0", "dx1", "dy1", "cx", "cy", "cornerRadius", "padRadius", "context", "arc", "buffer", "r0", "a0", "a1", "da", "path", "moveTo", "a01", "a11", "a00", "a10", "da0", "da1", "ap", "rp", "rc0", "rc1", "p1", "oc", "x2", "y2", "x3", "y3", "x32", "y32", "intersect", "ax", "ay", "bx", "by", "kc", "acos", "lc", "lineTo", "closePath", "centroid", "ascendingBisect", "hi", "mid", "right", "bisector", "valueof", "array", "center", "offset", "bandwidth", "entering", "__axis", "axis", "orient", "tickArguments", "tickValues", "tickFormat", "tickSizeInner", "tickSizeOuter", "tickPadding", "ticks", "domain", "spacing", "range", "range0", "range1", "position", "tickExit", "tickEnter", "line", "isFinite", "tickSize", "axisTop", "axisRight", "axisBottom", "axisLeft", "renderQueue", "func", "_queue", "rq", "render", "valid", "invalidate", "doFrame", "_rate", "rate", "remaining", "_invalidate", "config", "margin", "invertByScale", "invert", "found", "for<PERSON>ach", "invertCategorical", "brushExtents", "pc", "extents", "brushes", "brushNodes", "dimensions", "reduce", "acc", "cur", "raw", "yScale", "yscale", "scaled", "brushSelections", "yExtent", "reverse", "renderBrushed", "selected", "brushGroup", "actives", "_brushRange", "within", "dimension", "predicate", "some", "brushPredicate", "brushUpdated", "events", "newSelection", "brushed", "install", "createAxes", "_selector", "brushRangeMax", "_brush", "convertBrushArguments", "args_array", "stopPropagation", "brushFor", "brushReset", "uninstall", "drawBrushes", "brushSelection$$1", "brushObject", "selected$1", "pos", "axisBrushes", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "includes", "dimExt", "_step", "_iteratorNormalCompletion", "_iterator", "done", "_step2", "_iteratorNormalCompletion2", "_iterator2", "_e", "_step3", "_iteratorNormalCompletion3", "_iterator3", "_step4", "_iteratorNormalCompletion4", "_iterator4", "_e2", "_step5", "_iteratorNormalCompletion5", "_iterator5", "brushUpdated$1", "newBrush", "lastBrushID", "brushExtents$1", "hiddenAxes", "_bs", "ext", "install$1", "axisBrush", "brushReset$1", "uninstall$1", "uninstall$2", "strumRect", "crossesStrum", "strum", "strums", "minX", "p2", "b1", "b2", "containmentTest", "d1", "dims", "selected$2", "ids", "getOwnPropertyNames", "crossTest", "removeStrum", "onDragEnd", "drawStrum", "activePoint", "_svg", "points", "_line", "circles", "_drag", "ev", "maxX", "minY", "maxY", "onDrag", "bottom", "dimensionsForPoint", "xscale", "getOrderedDimensionKeys", "consecutive", "first", "second", "keys$$1", "install$2", "brushReset$2", "onDragStart", "uninstall$3", "hypothenuse", "signedAngle", "angle", "crossesStrum$1", "arc$$1", "arcs", "tmp", "containmentTest$1", "selected$3", "removeStrum$1", "onDragEnd$1", "drawStrum$1", "p3", "_path", "onDrag$1", "install$3", "uAngle", "brushReset$3", "onDragStart$1", "intersection", "brushMode", "brushModes", "modes", "String", "toUpperCase", "currentMode", "dimensionLabels", "title", "flipAxisAndUpdatePCP", "flip", "animationTime", "<PERSON><PERSON><PERSON><PERSON>", "delta", "deltaY", "dimensionTitleRotation", "getRange", "nullValueSeparator", "nullValueSeparatorPadding", "<PERSON><PERSON><PERSON><PERSON>", "applyAxisConfig", "axisCfg", "innerTickSize", "outerTickSize", "isBrushed", "object", "brushState", "PRECISION", "Matrix", "elements", "setElements", "Vector", "col", "cols", "rows", "M", "nj", "fn", "els", "isSameSizeAs", "returnVector", "modulus", "canMultiplyFromLeft", "sum", "ni", "isSquare", "dup", "np", "multiplier", "toRightTriangular", "det", "determinant", "tr", "rank", "isSingular", "divisor", "new_element", "augment", "I", "inverse_elements", "matrix_rows", "inspect", "result", "Diagonal", "Rotation", "theta", "mod", "z", "RotationX", "RotationY", "RotationZ", "Random", "Zero", "random", "Translation", "toUpperTriangular", "trace", "rk", "inv", "multiply", "dot", "vector", "V", "mod1", "mod2", "angleFrom", "product", "B", "A", "obj", "anchor", "distanceFrom", "part", "plane", "R", "direction", "C", "pointClosestTo", "P", "Q", "singleCurve", "ctx", "centroids", "row", "bundleDimension", "clusterCentroids", "bundlingStrength", "computeCentroids", "cps", "smoothness", "diff", "subtract", "computeControlPoints", "showControlPoints", "fillRect", "bezierCurveTo", "getNullPosition", "log", "colorPath", "beginPath", "stroke", "_functor", "pathMark", "marked", "strokeStyle", "pathBrushed", "brushedColor", "toType", "adjacentPairs", "arr", "ret", "highlight", "canvas", "highlighted", "foreground", "pathHigh<PERSON>", "pathForeground", "toTypeCoerceNumbers", "parseFloat", "detectDimensionTypes", "Number", "DefaultConfig", "sideEffects", "flags", "brushedQueue", "markedQueue", "foregroundQueue", "globalCompositeOperation", "globalAlpha", "resize", "applyDimensionDefaults", "sortDimensions", "interactive", "updateAxes", "detectDimensions", "autoscale", "Map", "clusterCounts", "has", "count", "_map", "computeClusterCentroids", "el", "_rebind", "method", "d3_rebind", "bindEvents", "__", "side_effects", "warn", "old", "userConfig", "dimensionTitles", "eventTypes", "scalePoint", "initState", "dragging", "clientWidth", "clientHeight", "layer", "getContext", "defaultScales", "_extent", "flipAxes", "val", "tempDate", "unshift", "scaleTime", "scaleLinear", "counts", "tempArr", "categoricalRange", "addBy", "scaleOrdinal", "padding", "devicePixelRatio", "lineWidth", "composite", "alpha", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marked<PERSON>ine<PERSON><PERSON><PERSON>", "shadowColor", "markedShadowColor", "<PERSON><PERSON><PERSON><PERSON>", "markedShadowBlur", "default", "commonScale", "scales", "_extends", "renderMarked", "renderDefault", "queue", "renderDefaultQueue", "renderBrushedDefault", "renderBrushedQueue", "renderMarkedDefault", "renderMarkedQueue", "compute_real_centroids", "computeRealCentroids", "shadows", "alphaOnBrushed", "axisDots", "_r", "dots", "fill", "clearRect", "fillStyle", "removeAxes", "_g", "axisElement", "axes", "g_data", "brushable", "reorderable", "outerHTML", "replace", "dat", "brushesToKeep", "brushReset$4", "ranges", "ranger", "ii", "dimRange", "_ii", "category", "categoryIndex", "categoryRangeValue", "multiBrushData", "idx", "_i", "_ii2", "_dim<PERSON>ange", "_j", "_ii3", "filtered", "selected$4", "__origin__", "reorder", "rowdata", "first<PERSON><PERSON>", "sortDimensionsByRowData", "unhighlight", "unmark", "mark", "positionSorted<PERSON>eys", "pixelDifference", "localeCompare", "adjacent_pairs", "mergeParcoords", "mergedCanvas", "foregroundCanvas", "canvasMarginLeft", "marginLeft", "canvasMarginTop", "marginTop", "drawImage", "URL", "webkitURL", "serializer", "XMLSerializer", "svgNodeCopy", "svgStr", "serializeToString", "src", "btoa", "img", "Image", "onload", "version", "bisectRight", "e10", "e5", "e2", "tickIncrement", "step", "power", "LN10", "error", "tickStep", "step0", "step1", "initRange", "initInterpolator", "interpolator", "implicit", "ordinal", "unknown", "band", "ordinalRange", "paddingInner", "paddingOuter", "align", "rescale", "ceil", "sequence", "rangeRound", "pointish", "unit", "normalize", "clamper", "bimap", "d0", "polymap", "bisect", "continuous", "untransform", "piecewise", "interpolateV<PERSON>ue", "interpolateRound", "u", "transformer", "specifier", "precision", "formatSpecifier", "exponent", "precisionPrefix", "formatPrefix", "precisionRound", "precisionFixed", "linearish", "nice", "durationSecond", "durationMinute", "durationHour", "durationDay", "durationWeek", "durationMonth", "durationYear", "date", "calendar", "year", "month", "week", "day", "hour", "minute", "millisecond", "formatMillisecond", "formatSecond", "formatMinute", "formatHour", "formatDay", "formatWeek", "formatMonth", "formatYear", "tickIntervals", "tickInterval", "timeYear", "timeMonth", "timeWeek", "timeDay", "timeHour", "timeMinute", "timeSecond", "timeMillisecond", "timeFormat", "k10", "sequential"], "sourceRoot": ""}