_cvxcore.cp311-win_amd64.pyd,sha256=vGKCwmP7i6kBe6lwzmnp0DVYpmL2cQ9E-aUAAm51Dgw,631808
_cvxpy_sparsecholesky.cp311-win_amd64.pyd,sha256=YKQ2MsJoS1ySc4lsHY_CnBMyADO_ErSFAITKibGSwkw,266240
cvxpy-1.7.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cvxpy-1.7.1.dist-info/METADATA,sha256=Tl15o08ajM5DfqCfYsGkJfOBKajlJMRMTGex2xkm9JQ,9817
cvxpy-1.7.1.dist-info/RECORD,,
cvxpy-1.7.1.dist-info/WHEEL,sha256=XYtH8YJwpb0T2FmFFI6kgzf4oRrtCRFvqirwcA5nq04,100
cvxpy-1.7.1.dist-info/licenses/LICENSE,sha256=We6oxRR70fj32FSAlfecqsRb4TZqMKNO2ZOZHYIU3fA,11545
cvxpy-1.7.1.dist-info/top_level.txt,sha256=-LXmuPcF7l-IfbP5XfPj_xi_FjiZx5tzTY1HGh1GOdk,43
cvxpy/__init__.py,sha256=dK0oe6s7cu3iI0vDAkLlIpaTc9SfwFglXGrZ0mnhO2I,3232
cvxpy/__pycache__/__init__.cpython-311.pyc,,
cvxpy/__pycache__/error.cpython-311.pyc,,
cvxpy/__pycache__/settings.cpython-311.pyc,,
cvxpy/__pycache__/version.cpython-311.pyc,,
cvxpy/atoms/__init__.py,sha256=gNUnEHLtYprfDH9UvrcSAuR_ks0ytdxxBdFPrW2cVUU,5909
cvxpy/atoms/__pycache__/__init__.cpython-311.pyc,,
cvxpy/atoms/__pycache__/atom.cpython-311.pyc,,
cvxpy/atoms/__pycache__/axis_atom.cpython-311.pyc,,
cvxpy/atoms/__pycache__/condition_number.cpython-311.pyc,,
cvxpy/atoms/__pycache__/cummax.cpython-311.pyc,,
cvxpy/atoms/__pycache__/cumprod.cpython-311.pyc,,
cvxpy/atoms/__pycache__/cvar.cpython-311.pyc,,
cvxpy/atoms/__pycache__/dist_ratio.cpython-311.pyc,,
cvxpy/atoms/__pycache__/dotsort.cpython-311.pyc,,
cvxpy/atoms/__pycache__/errormsg.cpython-311.pyc,,
cvxpy/atoms/__pycache__/eye_minus_inv.cpython-311.pyc,,
cvxpy/atoms/__pycache__/gen_lambda_max.cpython-311.pyc,,
cvxpy/atoms/__pycache__/geo_mean.cpython-311.pyc,,
cvxpy/atoms/__pycache__/gmatmul.cpython-311.pyc,,
cvxpy/atoms/__pycache__/harmonic_mean.cpython-311.pyc,,
cvxpy/atoms/__pycache__/inv_prod.cpython-311.pyc,,
cvxpy/atoms/__pycache__/lambda_max.cpython-311.pyc,,
cvxpy/atoms/__pycache__/lambda_min.cpython-311.pyc,,
cvxpy/atoms/__pycache__/lambda_sum_largest.cpython-311.pyc,,
cvxpy/atoms/__pycache__/lambda_sum_smallest.cpython-311.pyc,,
cvxpy/atoms/__pycache__/length.cpython-311.pyc,,
cvxpy/atoms/__pycache__/log_det.cpython-311.pyc,,
cvxpy/atoms/__pycache__/log_sum_exp.cpython-311.pyc,,
cvxpy/atoms/__pycache__/matrix_frac.cpython-311.pyc,,
cvxpy/atoms/__pycache__/max.cpython-311.pyc,,
cvxpy/atoms/__pycache__/min.cpython-311.pyc,,
cvxpy/atoms/__pycache__/mixed_norm.cpython-311.pyc,,
cvxpy/atoms/__pycache__/norm.cpython-311.pyc,,
cvxpy/atoms/__pycache__/norm1.cpython-311.pyc,,
cvxpy/atoms/__pycache__/norm_inf.cpython-311.pyc,,
cvxpy/atoms/__pycache__/norm_nuc.cpython-311.pyc,,
cvxpy/atoms/__pycache__/one_minus_pos.cpython-311.pyc,,
cvxpy/atoms/__pycache__/perspective.cpython-311.pyc,,
cvxpy/atoms/__pycache__/pf_eigenvalue.cpython-311.pyc,,
cvxpy/atoms/__pycache__/pnorm.cpython-311.pyc,,
cvxpy/atoms/__pycache__/prod.cpython-311.pyc,,
cvxpy/atoms/__pycache__/ptp.cpython-311.pyc,,
cvxpy/atoms/__pycache__/quad_form.cpython-311.pyc,,
cvxpy/atoms/__pycache__/quad_over_lin.cpython-311.pyc,,
cvxpy/atoms/__pycache__/quantum_cond_entr.cpython-311.pyc,,
cvxpy/atoms/__pycache__/quantum_rel_entr.cpython-311.pyc,,
cvxpy/atoms/__pycache__/sigma_max.cpython-311.pyc,,
cvxpy/atoms/__pycache__/sign.cpython-311.pyc,,
cvxpy/atoms/__pycache__/stats.cpython-311.pyc,,
cvxpy/atoms/__pycache__/sum_largest.cpython-311.pyc,,
cvxpy/atoms/__pycache__/sum_smallest.cpython-311.pyc,,
cvxpy/atoms/__pycache__/sum_squares.cpython-311.pyc,,
cvxpy/atoms/__pycache__/suppfunc.cpython-311.pyc,,
cvxpy/atoms/__pycache__/total_variation.cpython-311.pyc,,
cvxpy/atoms/__pycache__/tr_inv.cpython-311.pyc,,
cvxpy/atoms/__pycache__/von_neumann_entr.cpython-311.pyc,,
cvxpy/atoms/affine/__init__.py,sha256=lQWpvlXK3dTthra4vA9Ivhr83_JqeLJtXfd9F2lKlcE,578
cvxpy/atoms/affine/__pycache__/__init__.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/add_expr.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/affine_atom.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/binary_operators.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/bmat.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/broadcast_to.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/concatenate.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/conj.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/conv.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/cumsum.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/diag.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/diff.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/hstack.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/imag.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/index.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/kron.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/partial_trace.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/partial_transpose.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/promote.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/real.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/reshape.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/sum.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/trace.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/transpose.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/unary_operators.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/upper_tri.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/vec.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/vstack.cpython-311.pyc,,
cvxpy/atoms/affine/__pycache__/wraps.cpython-311.pyc,,
cvxpy/atoms/affine/add_expr.py,sha256=qwKTfgHXA7E6WOIQcPpWsmfiiLs40gLsOkZvWfzci4w,4392
cvxpy/atoms/affine/affine_atom.py,sha256=CZfDDgGSxyXsU41Xq9-7ALz3HwHXunQffHsep_dBRXw,5842
cvxpy/atoms/affine/binary_operators.py,sha256=FUvu-z1ZA7R_KE_u5sbGPKVlQ1EbFx4hKxMFFYX8jOU,16399
cvxpy/atoms/affine/bmat.py,sha256=V7eCwS-XTty3rY5kBYSb3MCV3Z51iwGgdy3ekDuqEOM,1175
cvxpy/atoms/affine/broadcast_to.py,sha256=sNw7gJNuLOi4fc1rxycrC2YqlS8AkYe9u7yXeyZIM1E,2421
cvxpy/atoms/affine/concatenate.py,sha256=76oX1Ecy3hgXMQXKbnYrtM3xZuJp7LNtOsBgwk5Kuoc,3085
cvxpy/atoms/affine/conj.py,sha256=6M8GAP8FtupqnTMKyS5wW49U9a1tT4I7mzvLEvd_s6Q,2397
cvxpy/atoms/affine/conv.py,sha256=TDRjPypKq7bKCYXDiP7E3_ZdoMEE2LuuaqdPX9JGUxk,7472
cvxpy/atoms/affine/cumsum.py,sha256=WSf0PTROcIsoipCZ-n0e8pQdOzRjIGRynl5dRPVWCXg,4448
cvxpy/atoms/affine/diag.py,sha256=J5NEpArcK3P9DHLFlWlZWn-cQ37hIZshO0lYQ3i-mNo,6009
cvxpy/atoms/affine/diff.py,sha256=rxh4cP4EeqzMHoGVhBhlfnuSwTXb_WF45HsdHW2m008,1695
cvxpy/atoms/affine/hstack.py,sha256=TpQ_Zopv__67qV-SIn0XDHaucrTGSQOX2ljkypzaFHs,3079
cvxpy/atoms/affine/imag.py,sha256=T068Rh41nzkrD1IwRo94I_yjuN7rEi2Ratdlno-HjVk,1491
cvxpy/atoms/affine/index.py,sha256=3UN8fDZqFMdyKm69618bmPtK6xxNDFHl_EtCFLEfjaA,6779
cvxpy/atoms/affine/kron.py,sha256=YfmuVOil32z1uPDifrnZDcSyQdy6QcWhgg0ZTxto1VI,4514
cvxpy/atoms/affine/partial_trace.py,sha256=bRcy_RDkN-B1_OwHytnLWcFpNJNzFMDQcvKuj-i8Q7I,3420
cvxpy/atoms/affine/partial_transpose.py,sha256=EZKjHRkgmwiPf_KnlJ2xYIuiTwbeoMRelGDnfihEBto,3487
cvxpy/atoms/affine/promote.py,sha256=t1p10DecH8mDHn1EDyV8-1fu4wrtIV2yr_0hgErAxx8,3337
cvxpy/atoms/affine/real.py,sha256=At3uliThtXsEshK0M4T8s9g9NnWu_MJDxna3uC1VEuA,1505
cvxpy/atoms/affine/reshape.py,sha256=bHv_RP-kq0MqyngjXUR8Ct0BYrXgOyN3duGqY-bQ7Mc,5896
cvxpy/atoms/affine/sum.py,sha256=l_NuJIKY3M8AR2VzjmCk1yJM8VoHUjzDCMMXXU-9n00,4668
cvxpy/atoms/affine/trace.py,sha256=wQhfNvyVRNo0QA_JOHayhA94F8bWDXEiz6jPzIppELk,3040
cvxpy/atoms/affine/transpose.py,sha256=Hb_keIK97A7UA_AwViG_tCQdoo88qoXEDJ3qcFsRgWA,5094
cvxpy/atoms/affine/unary_operators.py,sha256=20ksfYkU29BXQP0g3i4WfDicSbY2xy_VCJmnqmYPUz0,2848
cvxpy/atoms/affine/upper_tri.py,sha256=QyUKIZomg1Qkc3tANy-th-3-ZOJB9_lb0PDsjhSSUy4,5465
cvxpy/atoms/affine/vec.py,sha256=v8GGcnghX9CWQ8dNNq-OpJzUVJ9v7nIy0B4ZGUvfyho,1435
cvxpy/atoms/affine/vstack.py,sha256=YQGgg7XC1G6G5Gsk6RUbbaEsd6u0oVbSJE92wqjFvDk,3127
cvxpy/atoms/affine/wraps.py,sha256=fTrP_1D9n-WGtK31Ne2Br158_DY8XMVMJ5Q0n8vDlEo,4339
cvxpy/atoms/atom.py,sha256=e8TdPx_yb6rl_I0c2j55j9eU2IjpFqLSEhhfYRA-2lg,16822
cvxpy/atoms/axis_atom.py,sha256=eR30wdVPbN1vg8RBTw70c0SsLFiaUFYpjkmhmMMR168,5580
cvxpy/atoms/condition_number.py,sha256=KMdUqnbstRn4cAIEAbI0ab6sE89L8vJxp2z5dAdW-eY,3259
cvxpy/atoms/cummax.py,sha256=VsOVf1pZgLh8vBkAp8HX74JpaI_w8ndEG7yKRzNQOpM,3047
cvxpy/atoms/cumprod.py,sha256=hQVf7oCqUS31z6agtpWPF1jF0aDNCYqZa6HzHeBgR64,2481
cvxpy/atoms/cvar.py,sha256=83ilKDOxBTB1bADlI8jxxaNGjT7RZSwNegQjqTyPidw,2051
cvxpy/atoms/dist_ratio.py,sha256=_zbgc27uA35wthBVW2MVVEgneckMIa_XuhdmtcdprQQ,2508
cvxpy/atoms/dotsort.py,sha256=boqBcKgcj3yE6PjUXFeS3XUng2xPkMk2BWtYs3CQS2k,5186
cvxpy/atoms/elementwise/__init__.py,sha256=lQWpvlXK3dTthra4vA9Ivhr83_JqeLJtXfd9F2lKlcE,578
cvxpy/atoms/elementwise/__pycache__/__init__.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/abs.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/ceil.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/elementwise.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/entr.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/exp.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/huber.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/inv_pos.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/kl_div.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/log.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/log1p.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/log_normcdf.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/loggamma.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/logistic.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/maximum.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/minimum.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/neg.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/pos.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/power.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/rel_entr.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/scalene.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/sqrt.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/square.cpython-311.pyc,,
cvxpy/atoms/elementwise/__pycache__/xexp.cpython-311.pyc,,
cvxpy/atoms/elementwise/abs.py,sha256=0xDZA5KbHTdOb1zTRha80Jni9SqT1j28uj_G6csSnVw,2542
cvxpy/atoms/elementwise/ceil.py,sha256=rhQ7PwXxFtbP6M3KMzs5XugoqmL7VHNWOWRt36ZvZZU,4963
cvxpy/atoms/elementwise/elementwise.py,sha256=6BMmtAew0_-Ap5ol63ofqLBk6Ky15lJ3fI89TbMm2qo,2452
cvxpy/atoms/elementwise/entr.py,sha256=JuaHNOwUJCPnggX1fHcdgTrEt_SKMzykF1SP919pWSE,2841
cvxpy/atoms/elementwise/exp.py,sha256=lFbJO_vyxwXmcQ0N8ShkeMJWFDLlfzrPUTTM8uJP9W8,2444
cvxpy/atoms/elementwise/huber.py,sha256=WjsdbMfSBaS-LibNN7wCv_KnH48l7ceBwk8wT8g8Ra8,3715
cvxpy/atoms/elementwise/inv_pos.py,sha256=-u1XG8lBlxEEvsTd941HLfDylGxrom9TxOyS_oVL1OQ,726
cvxpy/atoms/elementwise/kl_div.py,sha256=sG29sIf68E0ZaIwOTWCgvMfxg1gJuCsu602NZuTh7YM,3070
cvxpy/atoms/elementwise/log.py,sha256=SaEmJ3DYIYTgn4Qrn-_-whO-_NHgRzTtnBZpBUMxgZw,2854
cvxpy/atoms/elementwise/log1p.py,sha256=neJE6vwXmcHNLz_huMd6cXvEtuf-6UzLtfvQaWLrzDs,2113
cvxpy/atoms/elementwise/log_normcdf.py,sha256=F-dRgdjQz171FXoDWyLtgpPYwB7ffPxbrENWkz1J8vo,2121
cvxpy/atoms/elementwise/loggamma.py,sha256=-puoCtkSko1ysDHgdxlEHF5TDL2ooGawtB898hmcl5A,1456
cvxpy/atoms/elementwise/logistic.py,sha256=6usb_ud9g2QrwXs06fnp0s8BSzB-WfBayv0Cvw1tcrg,2425
cvxpy/atoms/elementwise/maximum.py,sha256=A1fCxDsJuuMB9vnFLoMP1_tz_MLS8twgG6q5qL9jy0s,3583
cvxpy/atoms/elementwise/minimum.py,sha256=aP0qom7mDfOkc2stMMZbdMDPVyTk2TmaP1_tHozyCM4,3278
cvxpy/atoms/elementwise/neg.py,sha256=JnMdHx6c37NEaGUK0-iKjlF9pt9zUDZZVp2fKpmRXoI,723
cvxpy/atoms/elementwise/pos.py,sha256=B0Sm3W84l_EFMOtVLDlysGePtcyD-81kpn-mBC52ao4,720
cvxpy/atoms/elementwise/power.py,sha256=vkSX81vCNbNglCwJrczt03JgMFf9EYoG_HsSqkt5YH8,15215
cvxpy/atoms/elementwise/rel_entr.py,sha256=dDoG9Z6ZDBik5HL32vb8-NLVvO1_pWExE15QMJLdlzQ,3050
cvxpy/atoms/elementwise/scalene.py,sha256=tqlRw2TMFJ4q0VVxqvA7Z1dJjok7O-tFWNaDaOON5kU,803
cvxpy/atoms/elementwise/sqrt.py,sha256=PFBX9UaNBNafGdLzLfhw5Qm-Rr2pS5pTK2yETXIkFmM,763
cvxpy/atoms/elementwise/square.py,sha256=c-X2PNHxyjZj6vUp1AeDM9uavPD-Psw60eVeurCu9hM,713
cvxpy/atoms/elementwise/xexp.py,sha256=yBZYQlffR-kwPVJO5VzxefrNJTWwwipYzeYTLRwFZOQ,2774
cvxpy/atoms/errormsg.py,sha256=qjAeBYcYfGRL67tBT__1iaQRQHLjSQRKgg4FDQngZPM,248
cvxpy/atoms/eye_minus_inv.py,sha256=04_XdT7k0TCc462NM8SA5GY4jkClcuF28pHJRYUQiZ4,3423
cvxpy/atoms/gen_lambda_max.py,sha256=SSNRkHbYeOCzzgRje7kSPxnFGYzQyXJ4V05sPAwmCMw,3636
cvxpy/atoms/geo_mean.py,sha256=Nr6AZ29hpm0FTbeJ8F69rukQGR7n8IySZdEpmdjRu4k,12756
cvxpy/atoms/gmatmul.py,sha256=QKNimVBORam1E4JZDga4mbSGHDZ5GMMpUsT1cRj32GI,5060
cvxpy/atoms/harmonic_mean.py,sha256=IlI9O3slS-UAwCBkX8g6iK1mTGW7H11jfZkqNJgI8SU,1334
cvxpy/atoms/inv_prod.py,sha256=NQO4-WExhufX324IUU5_OGtz9H_rMcgF7B3_v7c7Jw8,1261
cvxpy/atoms/lambda_max.py,sha256=8ziq6Oi7V2OiFcYwOb1PynR-u8pUVcrZi0rGPmJtf1k,3416
cvxpy/atoms/lambda_min.py,sha256=1rulpPD_O0aWKeUvzqpYqswLFzUX2l4qzzSWo2fxBxY,836
cvxpy/atoms/lambda_sum_largest.py,sha256=by1g8jkKuEOKl8BtxBSo1G5xd9WCIfFS9AZZV7T0Eq8,2392
cvxpy/atoms/lambda_sum_smallest.py,sha256=KzW9L3vY7Nr2bMYVZPSXSJ0YzjgbxaWKdJMHkCqlz-A,859
cvxpy/atoms/length.py,sha256=1HV_-Br6Tv8uDH1EbcFgggqq1eTeqgOzegcbEP5u1Bw,2406
cvxpy/atoms/log_det.py,sha256=h_qgZbEDjlVvgr2kFmb3_0uw70CTmf_s2E-x5Oydw7o,3774
cvxpy/atoms/log_sum_exp.py,sha256=wdaJHRP07W3GphmJRTGSMVpZN4G7c9KoW4GklHf5S_w,2783
cvxpy/atoms/matrix_frac.py,sha256=U8vs9q_ZrTjlCPEIjeoxWyBbkYDrBMi1k_jGMAOcpFE,4693
cvxpy/atoms/max.py,sha256=plFbDn9Op4rhukwvlr7O1M1TzpaYkcC_Q9_Vr-kud6g,3752
cvxpy/atoms/min.py,sha256=rCQMR7tDTulDMYI_ElEDc5Z5IS9baQx3xNUzGthz1gI,3752
cvxpy/atoms/mixed_norm.py,sha256=9yHq89Gc44LOX6SgX-jV9q1LuFLcqGUuyoOEbihoGKI,1349
cvxpy/atoms/norm.py,sha256=E7cft7cXUMtUDQz1PmHJxVOLx7gVAcLNiiVhIulv56E,3482
cvxpy/atoms/norm1.py,sha256=RfKvCcVftc0wpl0LVJODWFu86Ql_wTdl9tFAwfnlvSU,3321
cvxpy/atoms/norm_inf.py,sha256=hndh2rpN6gpeHrP5DiM8IcX7c9woTvUEIhcDQqmjYyY,3492
cvxpy/atoms/norm_nuc.py,sha256=hxw_iDyC3o3sGK2J1hqoRBd1yu4i_YkJSxetpkonutU,2375
cvxpy/atoms/one_minus_pos.py,sha256=-AdMqnUW288XneF0xFOqL3q7PBb5umz4EVSXijxW58k,2904
cvxpy/atoms/perspective.py,sha256=U1N1GgM1DZ_xyf_KC53TmlUeB9O8OnbeW575aML3XtU,5277
cvxpy/atoms/pf_eigenvalue.py,sha256=ClKbQK_K8jfxS9mFOpO_gIExa04b_nmvElwkJStHwME,2931
cvxpy/atoms/pnorm.py,sha256=zaKbFl5dGEkpW1OHICXXdiUM8YCO_52VIa453UWun44,8712
cvxpy/atoms/prod.py,sha256=3IkgOaHUEr7mhYYDH3A-LYARCF94PTNXDJIOqj0IZq0,5240
cvxpy/atoms/ptp.py,sha256=bYll163nWKETNMlVwR9SD6N52PRboWlpceQcVIpz4ag,1004
cvxpy/atoms/quad_form.py,sha256=UIcCCqYqmOo-gCwnoV-OKBsJjoPaR4jzpq0zVkbZ4Yc,8422
cvxpy/atoms/quad_over_lin.py,sha256=tCy0ejfV5qC5fZQKVCyqqQFELvZzSIVnrEO8VyfXyWc,4757
cvxpy/atoms/quantum_cond_entr.py,sha256=Wkur_6uKCiWcvqkIVlUXZA1Pp6Rh6UFH_U-dzQKbSs4,2467
cvxpy/atoms/quantum_rel_entr.py,sha256=Caiwmusd78RlKfT6OsGqzmOhn4c0Cbza1LgKQbal_Cs,4318
cvxpy/atoms/sigma_max.py,sha256=jYhHWAxnUN_-XKy23acT1RVHH9HHwOL60Q0E-4iWas4,2737
cvxpy/atoms/sign.py,sha256=g6FlDUXdPlro-efxR1-uGvX8va3XpbmeHwCtsWGNhh8,2198
cvxpy/atoms/stats.py,sha256=udU6ScMhXg8V_LAuwzfVxPNV7wkzdLFvVVjyEjkKoX4,2372
cvxpy/atoms/sum_largest.py,sha256=im4tqtH5s_F5QR8YB_x9TcMdLpMXxiX-RDSozSMSHxU,3299
cvxpy/atoms/sum_smallest.py,sha256=NurYQLg5VT59-dllf5eU_ILJ8_ser0l2uvB9YG2pu3A,827
cvxpy/atoms/sum_squares.py,sha256=ibJc9ti56DZ6i7vK2mTVnroH5UC2ujD71Vu4zyClzDI,963
cvxpy/atoms/suppfunc.py,sha256=r7lXqhyjD0BjnWiS9eefyNBUf813qWptUv7D3K_sK7k,4200
cvxpy/atoms/total_variation.py,sha256=IQMJANAeCa_6BNmANwWnwBvKJOJkqzurf-WFV7HrrpI,2284
cvxpy/atoms/tr_inv.py,sha256=m_0Kl9ZH2gsLgKlv3F3gJT_T0zxo_GQSUZF7_xTw3Xw,4043
cvxpy/atoms/von_neumann_entr.py,sha256=YeVwU4uS_tUf0iiDXq6MQFYdt8Zie1W5qXaEZkO0KA8,4489
cvxpy/constraints/__init__.py,sha256=Npc-RbDJNDBYtrcP_TMyUfiZxDKh68Ys0qC9byN431I,1148
cvxpy/constraints/__pycache__/__init__.cpython-311.pyc,,
cvxpy/constraints/__pycache__/cones.cpython-311.pyc,,
cvxpy/constraints/__pycache__/constraint.cpython-311.pyc,,
cvxpy/constraints/__pycache__/exponential.cpython-311.pyc,,
cvxpy/constraints/__pycache__/finite_set.cpython-311.pyc,,
cvxpy/constraints/__pycache__/nonpos.cpython-311.pyc,,
cvxpy/constraints/__pycache__/power.cpython-311.pyc,,
cvxpy/constraints/__pycache__/psd.cpython-311.pyc,,
cvxpy/constraints/__pycache__/second_order.cpython-311.pyc,,
cvxpy/constraints/__pycache__/utilities.cpython-311.pyc,,
cvxpy/constraints/__pycache__/zero.cpython-311.pyc,,
cvxpy/constraints/cones.py,sha256=JOgQTllc77O_Jo4PpzUvXEi4SJi94Onqfx4AQPzrFwk,2324
cvxpy/constraints/constraint.py,sha256=HTS8SsI8r3T7YjolGtRdXFnsvi7_NESwXbhO2TZQNpE,7395
cvxpy/constraints/exponential.py,sha256=xDMj10hZiF1S5IYcjsWHOubO5Rn5A7ZQVGYfofjl5OQ,13908
cvxpy/constraints/finite_set.py,sha256=31XvqhYX487VILhRDtzxVjvCp_w-_LE9SpOTe2NM6qU,4729
cvxpy/constraints/nonpos.py,sha256=ZQfmVku1cwaTqXPJbwlAOFLwpZoigAYgX0ywcPHP7bQ,8151
cvxpy/constraints/power.py,sha256=k2y2oXeKB8MeNALnHCACO0cryu4XW38ZsrZQ81aILV4,11994
cvxpy/constraints/psd.py,sha256=irrC_iw21qQuRWdaLKzaEwuDSGjr_8IM0OplIUXFB1Y,3340
cvxpy/constraints/second_order.py,sha256=zE71Lki6L19eQ92qlUnNI7xeFF64Dtl0l1286NhqI_0,6210
cvxpy/constraints/utilities.py,sha256=d1wZMVQ9PcGhYL5Lr5-VP_k7cvcQ7SLjBGe7NKIxXGE,3952
cvxpy/constraints/zero.py,sha256=sc4Pxy4vK2cPyu7XD-kMZEPY5KKSn-T06FSlIcFrNXc,5443
cvxpy/cvxcore/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/cvxcore/__pycache__/__init__.cpython-311.pyc,,
cvxpy/cvxcore/python/__init__.py,sha256=6deRsENpSDGxehU_b_NBNlqJX8z9nlOqH4kw0HlnJM0,361
cvxpy/cvxcore/python/__pycache__/__init__.cpython-311.pyc,,
cvxpy/cvxcore/python/__pycache__/canonInterface.cpython-311.pyc,,
cvxpy/cvxcore/python/__pycache__/cppbackend.cpython-311.pyc,,
cvxpy/cvxcore/python/__pycache__/cvxcore.cpython-311.pyc,,
cvxpy/cvxcore/python/canonInterface.py,sha256=bdP7fShC1nIWVLJ6ukH_5_iylwfsvn7dpZbeAxS3Ex4,12465
cvxpy/cvxcore/python/cppbackend.py,sha256=MyvYi_GGbY-7qsMtgCtfQQdVdhX72eezfMI9zcHTrzQ,8617
cvxpy/cvxcore/python/cvxcore.py,sha256=NLHomImJO36z3prN78mLbWS8s1Q2tsZZLd6OEYF3I9Q,29672
cvxpy/error.py,sha256=D5XoHDMhZZdwWWSfhGbfI6mkDLTe2vepEiwLXuqPTBM,1361
cvxpy/expressions/__init__.py,sha256=burZ64UMhpKWaj6qhj9kJgHyQMHsK8xNe5Ev700MEeE,607
cvxpy/expressions/__pycache__/__init__.cpython-311.pyc,,
cvxpy/expressions/__pycache__/cvxtypes.cpython-311.pyc,,
cvxpy/expressions/__pycache__/expression.cpython-311.pyc,,
cvxpy/expressions/__pycache__/leaf.cpython-311.pyc,,
cvxpy/expressions/__pycache__/variable.cpython-311.pyc,,
cvxpy/expressions/constants/__init__.py,sha256=hq_86jn_ZLQR5PhyL223_krOzbe4-SJ_6ZDvGGzvEPE,689
cvxpy/expressions/constants/__pycache__/__init__.cpython-311.pyc,,
cvxpy/expressions/constants/__pycache__/callback_param.cpython-311.pyc,,
cvxpy/expressions/constants/__pycache__/constant.cpython-311.pyc,,
cvxpy/expressions/constants/__pycache__/parameter.cpython-311.pyc,,
cvxpy/expressions/constants/callback_param.py,sha256=RfLCdesPr6sunU5B1HXkINKpYdMbuHEHGTsVpDMc2oc,1783
cvxpy/expressions/constants/constant.py,sha256=wDWWC9NNoXaKhHmH3JAxi3y0exZ20UL02VVqykBxIac,8797
cvxpy/expressions/constants/parameter.py,sha256=YeaMMpUum5lgqcYWpj8Qh5PpMgSdnjziJPU7YNNeQd4,3564
cvxpy/expressions/cvxtypes.py,sha256=EoZlgG_Xt2bKSnhy-VjMxkBbUDu3YmbquHvnYo3mIQg,3593
cvxpy/expressions/expression.py,sha256=IPiV_bTXVCaJLaoKQs693Aj8QlKJ4YANiktyrDoYFqY,31850
cvxpy/expressions/leaf.py,sha256=SWrPtgPK34Fd1LhdbpYmEishhn7wHSSrVHIG_7rVMbQ,27502
cvxpy/expressions/variable.py,sha256=IO2yD9Slk28yZ5C3moB9jJldGaLd7S9ACvLD6M08ekQ,3299
cvxpy/interface/__init__.py,sha256=v4tIdKfPZIts6omoHrjcri3LlT1-GT7KtOcavwrONlc,628
cvxpy/interface/__pycache__/__init__.cpython-311.pyc,,
cvxpy/interface/__pycache__/base_matrix_interface.cpython-311.pyc,,
cvxpy/interface/__pycache__/matrix_utilities.cpython-311.pyc,,
cvxpy/interface/base_matrix_interface.py,sha256=vdyd5VhASxpSFNw4EIgSrH9i4y1L19vJPmAxb8cyo40,5301
cvxpy/interface/matrix_utilities.py,sha256=8MdWTef7Omo6CRBFo8qT6AR30txuIH10PnTCor3A1AY,11378
cvxpy/interface/numpy_interface/__init__.py,sha256=6Oz3f97N5F4I96l1zAuKdAHvJM2CFn5ROSxhc55DJRE,828
cvxpy/interface/numpy_interface/__pycache__/__init__.cpython-311.pyc,,
cvxpy/interface/numpy_interface/__pycache__/matrix_interface.cpython-311.pyc,,
cvxpy/interface/numpy_interface/__pycache__/ndarray_interface.cpython-311.pyc,,
cvxpy/interface/numpy_interface/__pycache__/sparse_matrix_interface.cpython-311.pyc,,
cvxpy/interface/numpy_interface/matrix_interface.py,sha256=1blPi98qc84V04npVUh1jhaat73eefxoo3r8BPfg_18,2118
cvxpy/interface/numpy_interface/ndarray_interface.py,sha256=0FXuLEjEvRqeHHnzOGmGNa5_JMChrpZDvciHJ-3L_gE,2628
cvxpy/interface/numpy_interface/sparse_matrix_interface.py,sha256=VZFSjs6nWF3RhjAh_IEoZL7EDGUULkGVnzomeS5kesM,3556
cvxpy/lin_ops/__init__.py,sha256=9CVKQchREWIdBAhA6jQucz1oKT2rAyjT_6JWtsyk5A0,702
cvxpy/lin_ops/__pycache__/__init__.cpython-311.pyc,,
cvxpy/lin_ops/__pycache__/canon_backend.cpython-311.pyc,,
cvxpy/lin_ops/__pycache__/lin_constraints.cpython-311.pyc,,
cvxpy/lin_ops/__pycache__/lin_op.cpython-311.pyc,,
cvxpy/lin_ops/__pycache__/lin_utils.cpython-311.pyc,,
cvxpy/lin_ops/__pycache__/tree_mat.cpython-311.pyc,,
cvxpy/lin_ops/canon_backend.py,sha256=oYjRr3VYPIsk7rvYrfI3gqHtXL7yUe4ghHWBEpfEwEo,83304
cvxpy/lin_ops/lin_constraints.py,sha256=m-AGl2k1CsJiVXV3itMocsIRRGdheb3QlsgDqhsNnlI,1156
cvxpy/lin_ops/lin_op.py,sha256=7AhDxxaMLmIYz2YqmhhWj7DRPt_LU2uFf-pY3A0ruCo,3686
cvxpy/lin_ops/lin_utils.py,sha256=rZ8eu5aNlkc5FSMCjH-2NgN3YFttz3wS1ANUBavdeFY,20522
cvxpy/lin_ops/tree_mat.py,sha256=3bVUM_A53f6PqW53rm_y_ulnJj_bsIGQ-p0YTX5w_Xs,12555
cvxpy/problems/__init__.py,sha256=lQWpvlXK3dTthra4vA9Ivhr83_JqeLJtXfd9F2lKlcE,578
cvxpy/problems/__pycache__/__init__.cpython-311.pyc,,
cvxpy/problems/__pycache__/iterative.cpython-311.pyc,,
cvxpy/problems/__pycache__/objective.cpython-311.pyc,,
cvxpy/problems/__pycache__/param_prob.cpython-311.pyc,,
cvxpy/problems/__pycache__/problem.cpython-311.pyc,,
cvxpy/problems/iterative.py,sha256=4h62ZTHPjoZ52kIVPRY5NfIaHW0l4lYgHDgbC8p36Ac,5128
cvxpy/problems/objective.py,sha256=o-gD4cC3vYKN88KBO7rNowlWyWNUN1lQ5J1MLTrDiG4,7666
cvxpy/problems/param_prob.py,sha256=YFX7tAr58MCdPlIDiD41MEsbH8VHQmjeyvEWIZxZIrE,1645
cvxpy/problems/problem.py,sha256=OyL_EbGuwm350R_SW7iXurvZybuZdHqzrQ3DlVIHEBc,70688
cvxpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/reductions/__init__.py,sha256=5QvGRapeBbBFnGY665b02n8L6lBtrwOHex28UWpHsjw,1483
cvxpy/reductions/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/__pycache__/canonicalization.cpython-311.pyc,,
cvxpy/reductions/__pycache__/chain.cpython-311.pyc,,
cvxpy/reductions/__pycache__/cvx_attr2constr.cpython-311.pyc,,
cvxpy/reductions/__pycache__/eval_params.cpython-311.pyc,,
cvxpy/reductions/__pycache__/flip_objective.cpython-311.pyc,,
cvxpy/reductions/__pycache__/inverse_data.cpython-311.pyc,,
cvxpy/reductions/__pycache__/matrix_stuffing.cpython-311.pyc,,
cvxpy/reductions/__pycache__/reduction.cpython-311.pyc,,
cvxpy/reductions/__pycache__/solution.cpython-311.pyc,,
cvxpy/reductions/__pycache__/utilities.cpython-311.pyc,,
cvxpy/reductions/canonicalization.py,sha256=iT3qMceaAtmcqwfSUoooaaz56zpWILK_GLcfwBPcGwg,6336
cvxpy/reductions/chain.py,sha256=bWdYJ96Sz-qInqAvxNpPSNBNhB-WtJadU64NabLaAVk,2752
cvxpy/reductions/complex2real/__init__.py,sha256=3-NTqwE3oPDFEbbHv6Hk_pWqSmbpINiUxe0dWj_-IhY,581
cvxpy/reductions/complex2real/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/complex2real/__pycache__/complex2real.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__init__.py,sha256=Wt2VVmGTYxXLkyzmvm4-vBzr3ylT3PiPMwxEFKZt0Y4,4893
cvxpy/reductions/complex2real/canonicalizers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/abs_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/aff_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/constant_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/equality_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/inequality_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/matrix_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/param_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/pnorm_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/psd_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/soc_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/variable_canon.cpython-311.pyc,,
cvxpy/reductions/complex2real/canonicalizers/abs_canon.py,sha256=GIyQh4ULV5oEbVcBTku6BNca6o3dhHsPmVu61yy5GBU,1118
cvxpy/reductions/complex2real/canonicalizers/aff_canon.py,sha256=lMT36hI4q8tS71soUfU4q3Kfa1Tn1NAIWsqeXQqLZ8U,3572
cvxpy/reductions/complex2real/canonicalizers/constant_canon.py,sha256=a_9muQwK7nyxwCXHw9VmJco_3FwvwmwYuvR8nia-fLQ,939
cvxpy/reductions/complex2real/canonicalizers/equality_canon.py,sha256=w5hWxyxdd_7ybPqlkGOlujDAtDTepziB4MO9lalDTX0,1832
cvxpy/reductions/complex2real/canonicalizers/inequality_canon.py,sha256=5jJXtcCAt43_3JZu9ufM4hyj7gvAsEBV_OdWTil2CME,1501
cvxpy/reductions/complex2real/canonicalizers/matrix_canon.py,sha256=_5QcCq_nCAypP6uYQnS5_ks_KmJ0wtlU11lAUnZtvZY,8424
cvxpy/reductions/complex2real/canonicalizers/param_canon.py,sha256=XKGDiwDwpiY_LLOOqqQITR1XIQH7u4D1T2UJsHgzMFk,887
cvxpy/reductions/complex2real/canonicalizers/pnorm_canon.py,sha256=H5CiHzA8eXc0E_k0l9N___Z56QgVKvQPCRm-KXGDFxU,837
cvxpy/reductions/complex2real/canonicalizers/psd_canon.py,sha256=Iieur19ey39Lsf3MfJdm4wXFvh8Z8IygoftV6S__rpQ,1074
cvxpy/reductions/complex2real/canonicalizers/soc_canon.py,sha256=eojBxD_ZyUpwCsbvgY8al4ACc7LSq3Ww360pugXPzXw,1625
cvxpy/reductions/complex2real/canonicalizers/variable_canon.py,sha256=E-3oSWXBs3JWKHI6iHhl9_qQPlxpAsJq41xYlDamYjw,1765
cvxpy/reductions/complex2real/complex2real.py,sha256=xA4QdbubxQlP5hRTLKgyWlmrgxZPFJen_tv-yeIM-hA,8408
cvxpy/reductions/cone2cone/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/reductions/cone2cone/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/cone2cone/__pycache__/affine2direct.cpython-311.pyc,,
cvxpy/reductions/cone2cone/__pycache__/approximations.cpython-311.pyc,,
cvxpy/reductions/cone2cone/__pycache__/exotic2common.cpython-311.pyc,,
cvxpy/reductions/cone2cone/__pycache__/soc2psd.cpython-311.pyc,,
cvxpy/reductions/cone2cone/affine2direct.py,sha256=5IyHu_qGlOJry0EwKkWvrPDS886nm1NYR7Nxd6c4jN8,19004
cvxpy/reductions/cone2cone/approximations.py,sha256=7Uf0vCtIWgfuPun7fMgjoklAN2GFLjctuXHxxtDMHj8,7262
cvxpy/reductions/cone2cone/exotic2common.py,sha256=7bWAes7DzljC3yYZo8bfpvDD4ZqQ1QkzmjeMXtij36I,4891
cvxpy/reductions/cone2cone/soc2psd.py,sha256=o3--gmX3Fgdtmt0p8tax2gYukWh579Fy7qP8_HywsMw,6744
cvxpy/reductions/cvx_attr2constr.py,sha256=WGX4ajr1uJXngUCA94C3RsAgRrR7g5FVYyv6Ghv0Rtk,8054
cvxpy/reductions/dcp2cone/__init__.py,sha256=jiB2bxZL2ofP3-Wj7s99cBGk9UoZQEaqFUy187te6Ys,580
cvxpy/reductions/dcp2cone/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/__pycache__/cone_matrix_stuffing.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/__pycache__/dcp2cone.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__init__.py,sha256=IaWsmfcta7x1Rq9aBOH9FQqHId2iuL_MlFg4zPyG3Bg,4939
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/entr_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/exp_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/geo_mean_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/huber_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/indicator_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/kl_div_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/lambda_max_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/lambda_sum_largest_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log1p_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log_det_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log_sum_exp_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/logistic_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/matrix_frac_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/mul_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/normNuc_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/perspective_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/pnorm_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/power_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/quad_form_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/quad_over_lin_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/quantum_rel_entr_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/rel_entr_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/sigma_max_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/suppfunc_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/tr_inv_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/von_neumann_entr_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/xexp_canon.cpython-311.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/entr_canon.py,sha256=2UHJlQGZ7qdESjcNeFJQV4GtIP8S-hrZ-05wprnyXwQ,1124
cvxpy/reductions/dcp2cone/canonicalizers/exp_canon.py,sha256=sfgEd2CZjP-gd1vDx1b67Jp4Hx8iaFASoJ2x5RZLhn0,995
cvxpy/reductions/dcp2cone/canonicalizers/geo_mean_canon.py,sha256=mWjP2FUdrr-TiZ_SQtN6VnEtEYg5BwDnxf_JvBUtyQA,1160
cvxpy/reductions/dcp2cone/canonicalizers/huber_canon.py,sha256=vHRijIPCbyBLqpPhpbDehr3yaIT7MK2B3Hn8welWAy4,1529
cvxpy/reductions/dcp2cone/canonicalizers/indicator_canon.py,sha256=mh5mEkoKig_myyWUjjXz-LwqWdv-n2LsVu6uO7tMLfc,698
cvxpy/reductions/dcp2cone/canonicalizers/kl_div_canon.py,sha256=aLW6WKryH4SgzlX5AouNgCpvw1orVtiynVWiIzFBxbQ,966
cvxpy/reductions/dcp2cone/canonicalizers/lambda_max_canon.py,sha256=miQxZuY8f8OHzoiIosH2IHwTKSCjouDD9Qwo-LeI4rM,1230
cvxpy/reductions/dcp2cone/canonicalizers/lambda_sum_largest_canon.py,sha256=Z5EiRl9OIoCPiOdWQ_ACvpz66ho6X2iQrXpxXBbD_8c,1537
cvxpy/reductions/dcp2cone/canonicalizers/log1p_canon.py,sha256=RLKyP7N7p7W6MhcRWNKnsTmr7S6X0h8cRawjxImgYDI,731
cvxpy/reductions/dcp2cone/canonicalizers/log_canon.py,sha256=1UEqavzbg0oppTCde7C0iMoQW71xV-CD_E1OMalUKCg,1081
cvxpy/reductions/dcp2cone/canonicalizers/log_det_canon.py,sha256=rqBzmoac0K7HX4wmlIOntV1GwPQ97Gn6CUMJ-Fc_hE4,2359
cvxpy/reductions/dcp2cone/canonicalizers/log_sum_exp_canon.py,sha256=2i95dk_oOdTOayjj3QJRTNPxBGucV1ceyISMjfo9IbQ,1640
cvxpy/reductions/dcp2cone/canonicalizers/logistic_canon.py,sha256=6MsY6zb8gzhw6wOsWn1BTdy5NX2Gi_Ie1XP6_T4fWPs,1146
cvxpy/reductions/dcp2cone/canonicalizers/matrix_frac_canon.py,sha256=WKRl2ph7WA4DRNRET0MrSoxjtWtTl7a-AMBzw7qBi6A,1269
cvxpy/reductions/dcp2cone/canonicalizers/mul_canon.py,sha256=WqUe3grwlKzzdfDJubmX_YC9xDErKWBSf2yaYXsLP38,1250
cvxpy/reductions/dcp2cone/canonicalizers/normNuc_canon.py,sha256=gFqlB0_I5jlbLekzYNUIiPB4npNAeW6ZvyLneHvhnGA,1349
cvxpy/reductions/dcp2cone/canonicalizers/perspective_canon.py,sha256=tTxTfH3El9Sao9qPGvYZZBbSyuQwOzrm0yp0cvNMIl8,3765
cvxpy/reductions/dcp2cone/canonicalizers/pnorm_canon.py,sha256=dOOgSi3fC8J9Yo6ZUZ0VuXQRa-lDtxe2pBBe9XjsJd4,2484
cvxpy/reductions/dcp2cone/canonicalizers/power_canon.py,sha256=NGfQMwhbn6WRGrsD7d16lTxb1E7HcnwrnUgv_ov9uSI,1474
cvxpy/reductions/dcp2cone/canonicalizers/quad_form_canon.py,sha256=u71nhCblSL4xGJwwdiTF3mTp5SvAaqEkSe-Aq4dvkkk,1315
cvxpy/reductions/dcp2cone/canonicalizers/quad_over_lin_canon.py,sha256=RRPYPnKW5UcuXxhWGPPkGWukR65uMLN9Hj5nGMC5zBY,1165
cvxpy/reductions/dcp2cone/canonicalizers/quantum_rel_entr_canon.py,sha256=kmZDCArV9PtWhcUmu2vaw_0Gps5m_bVw4VjKtaK7NPE,1066
cvxpy/reductions/dcp2cone/canonicalizers/rel_entr_canon.py,sha256=bEM4D8S9WKOpHw6P8wJDC_TeD4mvdXBBTz8bKAz8liY,967
cvxpy/reductions/dcp2cone/canonicalizers/sigma_max_canon.py,sha256=GK18-ZN5gvgPwxdLLFzIM5h9clEQJnAPq3ffe2I4ibA,1172
cvxpy/reductions/dcp2cone/canonicalizers/suppfunc_canon.py,sha256=PtgZPDaOyy7zwAjtUK8rNyHpH1poI5UaOhxpe8ObNWM,2202
cvxpy/reductions/dcp2cone/canonicalizers/tr_inv_canon.py,sha256=GzRdw5C3VdC1EOWnecAOF7Puyqs5P_XKnSqSncG6JMs,1748
cvxpy/reductions/dcp2cone/canonicalizers/von_neumann_entr_canon.py,sha256=DfrQdIJMn14jLdWY1IKIHsFwhSMMyekBglYbmIpxQjo,2026
cvxpy/reductions/dcp2cone/canonicalizers/xexp_canon.py,sha256=uBcFcU_1iuQ_1_A2Rnv1YnJ_6rQeI2geqRFDhIcnI6I,1182
cvxpy/reductions/dcp2cone/cone_matrix_stuffing.py,sha256=JfA9Ghri76O74jOkzJ7XCfmycOwQpAEYcnK9aLpj5IY,18154
cvxpy/reductions/dcp2cone/dcp2cone.py,sha256=9AX2OCmHDCrUZoqJWKg_U7vybI4VYiO5WDHdDfDOh2E,5745
cvxpy/reductions/dgp2dcp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/reductions/dgp2dcp/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/__pycache__/dgp2dcp.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/__pycache__/util.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__init__.py,sha256=4WpblPSDpeTN3D8VXGrQ_L7SLrf14011r2yHd0RjpkA,6035
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/add_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/constant_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/cumprod_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/div_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/exp_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/eye_minus_inv_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/finite_set_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/geo_mean_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/gmatmul_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/log_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/mul_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/mulexpression_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/nonpos_constr_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/norm1_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/norm_inf_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/one_minus_pos_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/parameter_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/pf_eigenvalue_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/pnorm_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/power_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/prod_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/quad_form_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/quad_over_lin_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/sum_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/trace_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/xexp_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/zero_constr_canon.cpython-311.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/add_canon.py,sha256=KkNMXJnRWUnrUwSW_pXzglKGX4JN0PzAgR2TA9jbAgk,1632
cvxpy/reductions/dgp2dcp/canonicalizers/constant_canon.py,sha256=bXL-obS2dJgi3-p1zSDq1hFR-5WcggKu8El0B7-KbSY,755
cvxpy/reductions/dgp2dcp/canonicalizers/cumprod_canon.py,sha256=L9EEEPb9bvjse6_aROux_kydtYB3OuPT_aX4IXufZ88,714
cvxpy/reductions/dgp2dcp/canonicalizers/div_canon.py,sha256=cJGg500yZEcdtnN21la3JJOb5HpVqABc0Mv7um09r5M,105
cvxpy/reductions/dgp2dcp/canonicalizers/exp_canon.py,sha256=DC7BB8vXeRyj0-v2W3Akv9oHVx3Zo4jp8O5jzEvncvo,120
cvxpy/reductions/dgp2dcp/canonicalizers/eye_minus_inv_canon.py,sha256=9Az63IGMGGDq02FhY_GDMTWTwaEIwsj7U8kOXR1IYXc,1477
cvxpy/reductions/dgp2dcp/canonicalizers/finite_set_canon.py,sha256=bdGNGOA5FiVa5Gd3ov0G87BW8RRR_pTRQaszXZDTE9s,248
cvxpy/reductions/dgp2dcp/canonicalizers/geo_mean_canon.py,sha256=2DBJFw26_oQltNzq4OA7NAA0ed01bxJ3Zs3AFtZz56s,157
cvxpy/reductions/dgp2dcp/canonicalizers/gmatmul_canon.py,sha256=htn1muAZyq-_WlizPrw2elsNddSM5dmjicO7oPMIZvY,65
cvxpy/reductions/dgp2dcp/canonicalizers/log_canon.py,sha256=I_1LGlUBQLJVI-7U-rZFJ4U4u-YvPSJ5MVbRRC0A5g4,106
cvxpy/reductions/dgp2dcp/canonicalizers/mul_canon.py,sha256=0WO-B-Kh0ZNmhvn1xTGyo8Iv3ECt-pKtdSkx851bQTE,132
cvxpy/reductions/dgp2dcp/canonicalizers/mulexpression_canon.py,sha256=rRueK-uyowOkW4LNg7FvLZ9eg_F_jQezohfo85ZhBVk,1510
cvxpy/reductions/dgp2dcp/canonicalizers/nonpos_constr_canon.py,sha256=FldOnYujrHzc9HeTgIF_mhN-1HGIisbeAMlh8fGl2po,175
cvxpy/reductions/dgp2dcp/canonicalizers/norm1_canon.py,sha256=K5W3MbGgXNJkey_c_UYhYNS7uP-8UXDnl7abVblHlYg,261
cvxpy/reductions/dgp2dcp/canonicalizers/norm_inf_canon.py,sha256=UASY1JNwe3N9o6mwpmBLPs17P7qE7vroEwJKacKLQ2c,263
cvxpy/reductions/dgp2dcp/canonicalizers/one_minus_pos_canon.py,sha256=0NIgHyXSc62AlaLqEhi_jA2tPN4t7ruoSGX2G5e-Sn4,179
cvxpy/reductions/dgp2dcp/canonicalizers/parameter_canon.py,sha256=DjKaVEE_-23DbPXJNX6ueUtC5wfI0mRMjPtWl4By3aI,1044
cvxpy/reductions/dgp2dcp/canonicalizers/pf_eigenvalue_canon.py,sha256=EeVb1hy7E5cjhKQPeX0BgLxTm4nVCkeUhV8W3j3ER1s,703
cvxpy/reductions/dgp2dcp/canonicalizers/pnorm_canon.py,sha256=aZnawg0v8Hw_FroVhWNpd2CU8jPpvB2vjQBPGJQRkEU,732
cvxpy/reductions/dgp2dcp/canonicalizers/power_canon.py,sha256=l5VMMpMs4gX6o9SO_HnAtHrEs9jSniVZSBShJqrFqKA,125
cvxpy/reductions/dgp2dcp/canonicalizers/prod_canon.py,sha256=Wbf5XkFoh6Qmes_q37pBqkrm4mdzkcr3R0IVz6KeGK4,159
cvxpy/reductions/dgp2dcp/canonicalizers/quad_form_canon.py,sha256=jTgu-wcFs50dbJWDE-XImZFf1mOF_T2RO_QKxYNdn9M,338
cvxpy/reductions/dgp2dcp/canonicalizers/quad_over_lin_canon.py,sha256=BlRvhejo9yvBUjm0YnhbDBmFqUgVqbWJ24SVYSxVSDY,298
cvxpy/reductions/dgp2dcp/canonicalizers/sum_canon.py,sha256=J2mY5AKWnrEBhBoScbqymB_wjJ1pSiNDSxPXF0dGoiM,1335
cvxpy/reductions/dgp2dcp/canonicalizers/trace_canon.py,sha256=FSqc0-R2-7j5gmWNgImMlcD3fvyA_FZrjS6c-tgWSnI,296
cvxpy/reductions/dgp2dcp/canonicalizers/xexp_canon.py,sha256=qXLyfJ9fDJeVIKW16yJcLs19NL5hRkvy0TM6mLmc0_8,131
cvxpy/reductions/dgp2dcp/canonicalizers/zero_constr_canon.py,sha256=uhyaN5LEnVjg75dzsO28wKHH9rTWP8_vCds7EVXtj8A,167
cvxpy/reductions/dgp2dcp/dgp2dcp.py,sha256=PsmgNg0UjMsS05oYITcDV08Ca0QFChbrALAWQA54yHI,3948
cvxpy/reductions/dgp2dcp/util.py,sha256=oA2Ry_QREBDZS6rc1P5Nyq9kVEyd92-A2JNScAP0beQ,875
cvxpy/reductions/discrete2mixedint/__init__.py,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
cvxpy/reductions/discrete2mixedint/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/discrete2mixedint/__pycache__/valinvec2mixedint.cpython-311.pyc,,
cvxpy/reductions/discrete2mixedint/valinvec2mixedint.py,sha256=ehTC0EZjI_4H2oS_Ly78OprU1QpdPbeQizINlLybYPs,2814
cvxpy/reductions/dqcp2dcp/__init__.py,sha256=mKzG2pkQogsymR7EHErEKlCuZiPt-ItLC5ZNAjTVYCY,577
cvxpy/reductions/dqcp2dcp/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/dqcp2dcp.cpython-311.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/inverse.cpython-311.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/sets.cpython-311.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/tighten.cpython-311.pyc,,
cvxpy/reductions/dqcp2dcp/dqcp2dcp.py,sha256=RHncWWS4ST6nTIgGl6GEoJvLoRXUCh2GJkiX5Pr69ss,9819
cvxpy/reductions/dqcp2dcp/inverse.py,sha256=EWifUn2oFSEm0j35rZH5dt8uOL7hSHJedKtFnLTRCWQ,3550
cvxpy/reductions/dqcp2dcp/sets.py,sha256=eaeDI0Dff3ArDavSSRohIVZNjJpbEnQp2rw4L0SQhh8,5343
cvxpy/reductions/dqcp2dcp/tighten.py,sha256=cH0acQWSxQB9XcmhwqZSi86mJ8ustPPffP3M_xO3ymQ,1188
cvxpy/reductions/eliminate_pwl/__init__.py,sha256=3-NTqwE3oPDFEbbHv6Hk_pWqSmbpINiUxe0dWj_-IhY,581
cvxpy/reductions/eliminate_pwl/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/__pycache__/eliminate_pwl.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__init__.py,sha256=BRgaTHtfBiMaFG_ByFxtHEuyZu_VBFMZg_ySLtNCW0I,2051
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/abs_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/cummax_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/cumsum_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/dotsort_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/max_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/maximum_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/min_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/minimum_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/norm1_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/norm_inf_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/sum_largest_canon.cpython-311.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/abs_canon.py,sha256=VE_dOUr4rcCiB2T9JQgwVliD0mvJxuRKHehiau3rZI0,772
cvxpy/reductions/eliminate_pwl/canonicalizers/cummax_canon.py,sha256=Z7hl1ygqnvnUq28iuHfhEBIwLwDbzMdMyMOoPu799-U,1145
cvxpy/reductions/eliminate_pwl/canonicalizers/cumsum_canon.py,sha256=olIK9jgv-9t7annqRKn4_nxF_fL4-VVKUnpkP50aZNk,1171
cvxpy/reductions/eliminate_pwl/canonicalizers/dotsort_canon.py,sha256=IOObRNAi_sFKaS-GjXnYvcAlby8DSIgJaSrwV4dPDvc,1490
cvxpy/reductions/eliminate_pwl/canonicalizers/max_canon.py,sha256=Vcli084RZydPmImEbg6DyYSPUEeyX8-4YpOoyXZEqcg,1277
cvxpy/reductions/eliminate_pwl/canonicalizers/maximum_canon.py,sha256=75WG2fj1KJei9a9abrHMWtM7uARL5Fhk0Sxsq3HWQhg,972
cvxpy/reductions/eliminate_pwl/canonicalizers/min_canon.py,sha256=lwAvNgPRPLrpHMfSlAfftWlW3O7wXfhmx7Ly8-g7HjU,945
cvxpy/reductions/eliminate_pwl/canonicalizers/minimum_canon.py,sha256=K5kjNzCkvomq6O-TgStsgvW757lkuaJWX2CRb4VYGMw,902
cvxpy/reductions/eliminate_pwl/canonicalizers/norm1_canon.py,sha256=7RtVYEaaUqwHgqctwWhJaGi2lCdCmgGiaoi-YAxrUlM,1207
cvxpy/reductions/eliminate_pwl/canonicalizers/norm_inf_canon.py,sha256=CbfOKz4rEZRDXPUF6pU4_XV8c8tpDrcyBhjyzbcv294,1272
cvxpy/reductions/eliminate_pwl/canonicalizers/sum_largest_canon.py,sha256=HmOjTuLGBw6MGun70YQUEYTVHZ2RbR5WBtyrg7EDTRA,949
cvxpy/reductions/eliminate_pwl/eliminate_pwl.py,sha256=qlS4gf_gduDiTUPCojumi-deL1BNrhZPyh9o4c1QDZw,1488
cvxpy/reductions/eval_params.py,sha256=4HlGoVdlWU0HZOqe5jJIYLsRzCMxqrbLwkTf3VXo9Ko,2767
cvxpy/reductions/flip_objective.py,sha256=lXu8At9hUXTsGKYEf4_ERmReiDv1qfpRmclOwur25qk,2118
cvxpy/reductions/inverse_data.py,sha256=Hv-NYAgQQvMOPTXPlA7G1ZkNFjyB2OwOjR-RqEdM9yw,2000
cvxpy/reductions/matrix_stuffing.py,sha256=8hcO8ycD5RLOk-LYiMWH3P3Ogj6pXabfcG89lAvo0dc,4378
cvxpy/reductions/qp2quad_form/__init__.py,sha256=jiB2bxZL2ofP3-Wj7s99cBGk9UoZQEaqFUy187te6Ys,580
cvxpy/reductions/qp2quad_form/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/__pycache__/qp2symbolic_qp.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/__pycache__/qp_matrix_stuffing.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__init__.py,sha256=sP7s7IOzVEEygpe6vfgfOJkTpai6Q2hQQmYy5i7QvtY,2171
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/huber_canon.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/power_canon.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/quad_form_canon.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/quad_over_lin_canon.cpython-311.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/huber_canon.py,sha256=BhJSJTL18Xj3AhwqgfgGGJ-RLds_pR8rqBvwGAoExWY,1520
cvxpy/reductions/qp2quad_form/canonicalizers/power_canon.py,sha256=DWFhOOhyYVpoVln16pAr3qUlayz3_3LUfLkifisRu-Q,1484
cvxpy/reductions/qp2quad_form/canonicalizers/quad_form_canon.py,sha256=1wHhs8V7H1Qz_gYYGB0yAtE_03IOEaP2-kUa0C0RypI,995
cvxpy/reductions/qp2quad_form/canonicalizers/quad_over_lin_canon.py,sha256=tmB-PYdSk52qehRKBxEFCBSsN98m9h7wXqRROu3nrxA,1358
cvxpy/reductions/qp2quad_form/qp2symbolic_qp.py,sha256=q70380XJHffq5oS0re94rUasKJy-LiTIQKqXWITwtCI,2438
cvxpy/reductions/qp2quad_form/qp_matrix_stuffing.py,sha256=VpEPpt-rJNojC24FiyFlu14nsMsGFG00KnHwnxMEgPU,13184
cvxpy/reductions/reduction.py,sha256=QW7C6HzlGtS2dr1ZmKhYlJt0_ASVlKamcRspM0QLUhs,5196
cvxpy/reductions/solution.py,sha256=9tcgOqkIdEByd0OJo5WdyLJjrgS3qacXWM67k_E1JU8,3114
cvxpy/reductions/solvers/__init__.py,sha256=lQWpvlXK3dTthra4vA9Ivhr83_JqeLJtXfd9F2lKlcE,578
cvxpy/reductions/solvers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/bisection.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/compr_matrix.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/constant_solver.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/defines.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/intermediate_chain.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/kktsolver.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/solver.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/solving_chain.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/solving_chain_utils.cpython-311.pyc,,
cvxpy/reductions/solvers/__pycache__/utilities.cpython-311.pyc,,
cvxpy/reductions/solvers/bisection.py,sha256=txNooGI7DMnW7DIvNFQ1YMLBIIAGzit7fiYWVcx8Hrw,7580
cvxpy/reductions/solvers/compr_matrix.py,sha256=yTMUSxyR1O9hieKb6qJ359qik-txAW97jEMPOq7jxpo,3742
cvxpy/reductions/solvers/conic_solvers/__init__.py,sha256=gy0-A7q1wfTxsU-QlCLxd1rnipIu5li_HUWcKBorue8,1240
cvxpy/reductions/solvers/conic_solvers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cbc_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/clarabel_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/conic_solver.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/copt_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cplex_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cuclarabel_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cuopt_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cvxopt_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/diffcp_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/ecos_bb_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/ecos_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/glop_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/glpk_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/glpk_mi_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/gurobi_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/highs_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/mosek_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/nag_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/pdlp_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/qoco_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/scip_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/scipy_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/scs_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/sdpa_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/xpress_conif.cpython-311.pyc,,
cvxpy/reductions/solvers/conic_solvers/cbc_conif.py,sha256=oYH2x8XZ_NqjnshEvViSDLpFchFtyHFP9-kMuiQOKoA,8255
cvxpy/reductions/solvers/conic_solvers/clarabel_conif.py,sha256=Mu-DZhR-t1ZTZLmlDnZiT2bXjcYQ5WlIb6fX198dM4g,12457
cvxpy/reductions/solvers/conic_solvers/conic_solver.py,sha256=i6n3ES1DfDdd_1xueZUPAvZgkGkio4P62hA5yk7orgY,14897
cvxpy/reductions/solvers/conic_solvers/copt_conif.py,sha256=CglX2jcuPxIbQwqdCvA97YsD6V66mJkbbM_ht6kKeg4,15768
cvxpy/reductions/solvers/conic_solvers/cplex_conif.py,sha256=cXta1KrWJK1OLTAHWa_x1_baeHCa3DuvRNieo7c6EF4,19510
cvxpy/reductions/solvers/conic_solvers/cuclarabel_conif.py,sha256=V_wjw-MB-4DwVg9b-gEt6V4CFhbHSRtaVemKJ_4-cDA,7459
cvxpy/reductions/solvers/conic_solvers/cuopt_conif.py,sha256=fI5uIcORYV0p8INGDMytiu1XYtEY0NOeIekRrG-ELeM,12316
cvxpy/reductions/solvers/conic_solvers/cvxopt_conif.py,sha256=MAwXXRlQPsglE4IWmorDQLz4xt-zRW4sncj0Rt_WuxU,14256
cvxpy/reductions/solvers/conic_solvers/diffcp_conif.py,sha256=cUSAjHMOZSDsKc3EVCYY8yxXA-ZqBBaWWv2_ojI2p08,7400
cvxpy/reductions/solvers/conic_solvers/ecos_bb_conif.py,sha256=IaGvHJOMyiVXE9a7EJooe8d2EejWmZzQ_7YJUkaatCk,5799
cvxpy/reductions/solvers/conic_solvers/ecos_conif.py,sha256=VFLXWesWvETta6pxDuSlk4aCqcRsMyrCQ_Dgke7j3sU,7593
cvxpy/reductions/solvers/conic_solvers/glop_conif.py,sha256=Z0ZYXdScsy_lCogZ5WqUq3I5kiu6T3dFkOTrPvZ9JFQ,9418
cvxpy/reductions/solvers/conic_solvers/glpk_conif.py,sha256=iFrQFERy47W5UxCtoVxTMAF4U32z_lWkbpSSMLasN1I,4346
cvxpy/reductions/solvers/conic_solvers/glpk_mi_conif.py,sha256=2uJQ0i_KTyc-0YSkQ1ep2fCuAQvdwXDn3862r4iuEIM,4898
cvxpy/reductions/solvers/conic_solvers/gurobi_conif.py,sha256=bKuk4Qi5swimn3BzAHFZT4_RfN152CuEgoJciESYxIM,15428
cvxpy/reductions/solvers/conic_solvers/highs_conif.py,sha256=INwK8XrxfRQLbsZr-EY6vq5Pe3u8_Ng8hwQgUDQ1n0w,9877
cvxpy/reductions/solvers/conic_solvers/mosek_conif.py,sha256=h9-5RjYAVxgOqOviLw6SBCVqd7FPFclpIo6lwdWF_ak,35090
cvxpy/reductions/solvers/conic_solvers/nag_conif.py,sha256=V-VYUEybVRQgpEJROdZ-ZEsJLJ76lMAvRoKPevCh9H4,11036
cvxpy/reductions/solvers/conic_solvers/pdlp_conif.py,sha256=8gOH9AxIAAIK82HchVTKXFULpZhd8_EnDsMQbkFRBu0,8949
cvxpy/reductions/solvers/conic_solvers/qoco_conif.py,sha256=qqKrBDMSNlQXK2uQtm-jZNNl9mJToBWH5ZMOBv2kly0,6775
cvxpy/reductions/solvers/conic_solvers/scip_conif.py,sha256=uIsdrXCrwMdhULJOzToN7hM2Tg5yzwAu4I6DZDXi4fU,16616
cvxpy/reductions/solvers/conic_solvers/scipy_conif.py,sha256=3wv15b3hBFFjUNL334z785ub48w9_h22nodSYrekmrE,12153
cvxpy/reductions/solvers/conic_solvers/scs_conif.py,sha256=n5qD42zL6_Z9_JEr5pEXOZT6ZRMptl_2vKTnDrpBBtQ,13860
cvxpy/reductions/solvers/conic_solvers/sdpa_conif.py,sha256=-Fn7Wx371BiiCk__V8Ho-8ARFpnMdyTXC95O5au_ZtM,7072
cvxpy/reductions/solvers/conic_solvers/xpress_conif.py,sha256=1bM6n981Yf54o-4Z98qQOAj5C7KSZRbohPOkc4wrBrg,16390
cvxpy/reductions/solvers/constant_solver.py,sha256=-7YGnv4VW3KR0TFm6QU87g-Z1cJF_bKSeOu9SBOe2Nc,1170
cvxpy/reductions/solvers/defines.py,sha256=eLmtbmtLjUQuE4DMxJ-n9szwi7el9x1IGywOFlEdHbk,6342
cvxpy/reductions/solvers/intermediate_chain.py,sha256=SVU4rI5tNnEHO4iW0FsT4BLkL2xf6DpDUgrnuTxwhEI,4279
cvxpy/reductions/solvers/kktsolver.py,sha256=-YfJFoezeuZKzbeCHcfviJ8gyrD782LN667lZ8YafZk,4496
cvxpy/reductions/solvers/qp_solvers/__init__.py,sha256=lQWpvlXK3dTthra4vA9Ivhr83_JqeLJtXfd9F2lKlcE,578
cvxpy/reductions/solvers/qp_solvers/__pycache__/__init__.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/copt_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/cplex_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/daqp_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/gurobi_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/highs_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/mpax_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/osqp_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/piqp_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/proxqp_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/qp_solver.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/xpress_qpif.cpython-311.pyc,,
cvxpy/reductions/solvers/qp_solvers/copt_qpif.py,sha256=tJvLOgBVrjL2QbM8tTdgyM_XCSA0hzN4hrXtngHawPc,6912
cvxpy/reductions/solvers/qp_solvers/cplex_qpif.py,sha256=arOgQPtxKo6-HbdowCzYoeEyd2cw79C3dN2fxkOPWzU,6234
cvxpy/reductions/solvers/qp_solvers/daqp_qpif.py,sha256=2a3UCNS-10QuNqX1crcur5Fab8r_tt1x33SuxUca3SM,6790
cvxpy/reductions/solvers/qp_solvers/gurobi_qpif.py,sha256=DOYWCl6Mmi8-nSXtZIbueP285jORPWMNcP_kFqmsIC4,8444
cvxpy/reductions/solvers/qp_solvers/highs_qpif.py,sha256=tP0zbRBuY9Wg2voFNZSdCdWMDZogwFOl8vQqVHNdh0I,8011
cvxpy/reductions/solvers/qp_solvers/mpax_qpif.py,sha256=k9QKGcVXFYdt0wVtiuc5jWET0K8xEU6tnVZBvshp4nY,5159
cvxpy/reductions/solvers/qp_solvers/osqp_qpif.py,sha256=2oIX2pTungOgfcByhdwS436eyzDn3NEgk0cHIE1vK-Q,5847
cvxpy/reductions/solvers/qp_solvers/piqp_qpif.py,sha256=umi0ogPT8k_Jbi_8xPtJs0uOw6pMI-HRXR0o0UdU1us,6547
cvxpy/reductions/solvers/qp_solvers/proxqp_qpif.py,sha256=J2SQm-cBQmq1my7nDN5HAe5s0zNRri4zBVYzT_5z3uI,6184
cvxpy/reductions/solvers/qp_solvers/qp_solver.py,sha256=x6FvbW_px15B6ZG-PG3BGp3jWwuJDwTNl2mTnhDky4s,3769
cvxpy/reductions/solvers/qp_solvers/xpress_qpif.py,sha256=CaS36ADT1l45XoeD0lkXLZgvX5PnaN4qS5JRJJiR3e8,9993
cvxpy/reductions/solvers/solver.py,sha256=FElLEf5o1M0SDlWSrl00UbL7wAfuFMEPlTOLU3Ubb1s,2727
cvxpy/reductions/solvers/solving_chain.py,sha256=5dXL__yMwIs0gJJA39YsOjHnI19NeAw0OhDGB26IMTA,19406
cvxpy/reductions/solvers/solving_chain_utils.py,sha256=4jmb5Ubx7LVDKA6D9j8D9lA_9oRiRgfmRJjxVfcf8Ws,2165
cvxpy/reductions/solvers/utilities.py,sha256=ZQHerF7Qs9-JICujTk8z62u_6V_u8dfEosJ5PgpaE7U,2917
cvxpy/reductions/utilities.py,sha256=2Cmc_CU9kmjbi58D4UH37yCt59UdCyg4v3OiVoCwyFw,6327
cvxpy/settings.py,sha256=elYciUS90VEgXPz853mx4HqSxColaGBlRjAoI_bMfM0,5839
cvxpy/tests/__init__.py,sha256=lQWpvlXK3dTthra4vA9Ivhr83_JqeLJtXfd9F2lKlcE,578
cvxpy/tests/__pycache__/__init__.cpython-311.pyc,,
cvxpy/tests/__pycache__/base_test.cpython-311.pyc,,
cvxpy/tests/__pycache__/ram_limited.cpython-311.pyc,,
cvxpy/tests/__pycache__/solver_test_helpers.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_KKT.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_atoms.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_attributes.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_base_classes.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_canon_sign.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_coeff_extractor.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_complex.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_cone2cone.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_conic_solvers.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_constant.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_constant_atoms.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_constraints.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_convolution.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_copt_write.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_copy.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_curvature.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_custom_solver.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_cvxpygen.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_derivative.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_dgp.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_dgp2dcp.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_domain.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_dpp.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_dqcp.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_errors.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_examples.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_expression_methods.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_expressions.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_grad.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_gurobi_write.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_interfaces.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_kron_canon.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_lin_ops.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_linalg_utils.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_linear_cone.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_matrices.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_mip_vars.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_monotonicity.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_nonlinear_atoms.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_objectives.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_param_cone_prog.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_param_quad_prog.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_perspective.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_power_tools.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_problem.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_python_backends.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_qp_solvers.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_quad_form.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_quadratic.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_quantum_rel_entr.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_scalarize.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_semidefinite_vars.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_shape.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_sign.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_suppfunc.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_valinvec2mixedint.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_versioning.cpython-311.pyc,,
cvxpy/tests/__pycache__/test_von_neumann_entr.cpython-311.pyc,,
cvxpy/tests/base_test.py,sha256=ysyH_g9UhloyfOwDLLLt9OynCi7iEd32DXXr2AEvS88,1544
cvxpy/tests/ram_limited.py,sha256=a9wg94Um42ECRUjEYIGsI-aO9l7UylVBdhFgHdPXOBA,2031
cvxpy/tests/solver_test_helpers.py,sha256=SBk38HkzC7VYQCwrcuG9Vt-Fn0DDaT-oJFnCY-OoT3o,54342
cvxpy/tests/test_KKT.py,sha256=H4ZrXiXR28pazpk9RuZLp_H3BoyfDnLuHcoYQbEE6Ws,14069
cvxpy/tests/test_atoms.py,sha256=1TbQtF4bHAagzN33xF2LuPu504nNQJDIwcI0xhYWyZc,83833
cvxpy/tests/test_attributes.py,sha256=OwbVYzwiAXMJG5jVBDkJJwXSiZd7ZGWJR7dimbUIhfY,19590
cvxpy/tests/test_base_classes.py,sha256=wchUZvmpvkmx1n9R3KArcq-VXOu0Zh6u1UXuhYRN2uo,901
cvxpy/tests/test_canon_sign.py,sha256=VSYk6ZQ-iSTp_IfUrG_qZLT0et0RKFbs7TL4092Bbv8,2110
cvxpy/tests/test_coeff_extractor.py,sha256=hRhXm0QRJAIbxnxk4M9khv5MFVTow9si_VIZLiYkLxs,7124
cvxpy/tests/test_complex.py,sha256=bhtp-NxlmSKHp_ffHZGPGtGFzEvaHDOwhcay0SjDD74,30423
cvxpy/tests/test_cone2cone.py,sha256=3lU_u6onXOW0-RdBmy6rVyp4t7fKGxqrdyd1AyR2MaQ,30737
cvxpy/tests/test_conic_solvers.py,sha256=TlV5gpKl74h01I6sfyl09hvthZUKpxX_MP9jrNLK4No,102677
cvxpy/tests/test_constant.py,sha256=Ex-LYozoj4muWXESZdS1Rx7nSzD9LmeeGR4yPYBmeEE,3016
cvxpy/tests/test_constant_atoms.py,sha256=TAsrAo97JkiX-AjM4jUGISfY80eLKWgSU7MWrylhscY,19517
cvxpy/tests/test_constraints.py,sha256=qDNeVrb-yyBWq4HGQLpdpscvsiAFn6dMUQbnyrKvzZI,23153
cvxpy/tests/test_convolution.py,sha256=-QXsk-cB_1639ISbnF-hySC90QyLnKxBv24FXzy4pT4,6908
cvxpy/tests/test_copt_write.py,sha256=B7XIkM74SflYayCHP65srrTt_ORw0j9ct1ffSYB_vOs,1292
cvxpy/tests/test_copy.py,sha256=kjyvyEnLRrQzlDYbgPmmqPNFYRfZd20rigDPphc-DyQ,3105
cvxpy/tests/test_curvature.py,sha256=Ahk1rIxIb7YhSrXhJrGI-OTEtrca7pknzSZab1Lis-U,3575
cvxpy/tests/test_custom_solver.py,sha256=t4ATnq9kr-wfypg8892nrMZyIRAZNddplK_Rdy2HUOo,4049
cvxpy/tests/test_cvxpygen.py,sha256=fRYLUi7AjZZPrJS_h1-CTd1WItkrncVCjE0sPuvPcMQ,9159
cvxpy/tests/test_derivative.py,sha256=aITFAODxQaCOymbzsrGiH87ywn0Ivzr6YosnOM7o-nc,26233
cvxpy/tests/test_dgp.py,sha256=1nyb0jVDazGGQBtoCb9BdSl_cLYCLvQk4KA3tDs2s4E,9275
cvxpy/tests/test_dgp2dcp.py,sha256=K4CHQnU7Zm0geVit0T9IbcgfeMDCvPhO0CAJigUj-og,28061
cvxpy/tests/test_domain.py,sha256=gJhetttX-hEjMhaTMmkoQdFf8-GI9-AN_colX0pSkQs,7397
cvxpy/tests/test_dpp.py,sha256=bRsa_UAXVepK0LPUqs2sbFWD44mKr9mk2ceH7HhLVPQ,35530
cvxpy/tests/test_dqcp.py,sha256=jyZNyjog3JJS23U2HABr-GNn9JPXTWeE9kdYBFxhjzQ,28575
cvxpy/tests/test_errors.py,sha256=n1JvVnI6TiOJssOH94mg88HPhbH3Qr8KFSsZ4Eds9WA,4420
cvxpy/tests/test_examples.py,sha256=bCm-_ngSjy5602z9UlpuXu54yCRBbIqd_VjqWWN2w0g,21015
cvxpy/tests/test_expression_methods.py,sha256=6gE_qx7peJdTsP0YkmVwTZz6Trt0ENAxA0rNktFCNmQ,15216
cvxpy/tests/test_expressions.py,sha256=D1YhcV4ejcOXWRvOtc7MWPm1iOW3eCJZAR75I-slCPA,70857
cvxpy/tests/test_grad.py,sha256=f4pp2dxga8TTXIMc-tyvNID0wP3U_e9ic68HvoQF2W8,34175
cvxpy/tests/test_gurobi_write.py,sha256=eTOVutQ-QWJzc8GDmEQ3kLomD-FwMY91KxiHVCFr22Y,1299
cvxpy/tests/test_interfaces.py,sha256=SsQfJw-46_YQQn10aRFYD2MusTsZz6N2fv5m7_2dERo,6448
cvxpy/tests/test_kron_canon.py,sha256=VlFVKvNggQknkJNg-RaohGERouMmiXtehDthJWCRqUA,7803
cvxpy/tests/test_lin_ops.py,sha256=NgYBC3vLdWi__khrZDp0xh0JMfBrsIpseXXA3Gt3c2c,5107
cvxpy/tests/test_linalg_utils.py,sha256=aM4o4FpNbEg6tQjlVMpQdfzRo2XC7iBXR23K3YT_atI,3733
cvxpy/tests/test_linear_cone.py,sha256=x9dsl1ClnrW8Rnm0r36mCxSzcYAkMZvhZ7doazZ56To,16991
cvxpy/tests/test_matrices.py,sha256=QDz0MaTsMLs7QBVDZMqyHoUoHr4U0Yv0OjXRZYBTaqE,5212
cvxpy/tests/test_mip_vars.py,sha256=ckCnQAPFtfAFjg9u8fVtWykEN-mpcm2DOpAMLZkRh28,4208
cvxpy/tests/test_monotonicity.py,sha256=6_OjvLe_NKScmz6N4XX19s9pel-UYli5v6rnlPOwebk,3036
cvxpy/tests/test_nonlinear_atoms.py,sha256=jzowzAUdXwXRi3IauGTUDSp0FH3qr_KWe7CydPVJO7c,6674
cvxpy/tests/test_objectives.py,sha256=_Hud1ftpMjCtGBLxweS0M4fxysadXmcUJVrbtbeCzEE,5120
cvxpy/tests/test_param_cone_prog.py,sha256=3pS3hums1cWbNsg4y3Ub8ZOeOB9kNXTJtFypE2f96I4,5939
cvxpy/tests/test_param_quad_prog.py,sha256=3DL3HyfAj1UvMgukTJ1TCmcdl7N7-DyqQmBo842A3nc,6931
cvxpy/tests/test_perspective.py,sha256=-RPl9x1M8Iizvq_9adQ4CShkwQ0-8Wt-fT7LzIRf19c,13827
cvxpy/tests/test_power_tools.py,sha256=Y65gACOC7kyX9Q_5ULZ6p0eIEx5yzHWF-PdLFgPHsZg,4454
cvxpy/tests/test_problem.py,sha256=alQTyrYNwnhLeDW20V8PVeNY71Sn0PZ79VxdBX-nv6Y,92863
cvxpy/tests/test_python_backends.py,sha256=PZPeg7niUC4l2sn0O5cVTL9XpypKj_PNqdtd9lYIGa4,95167
cvxpy/tests/test_qp_solvers.py,sha256=8fEl6sTD6DOdav4vhYRVeaI-qf3Vmzxnd4DX5R4DGfg,27664
cvxpy/tests/test_quad_form.py,sha256=55WpCe7CBa02XDc8ouTQymN8gBnjYfQWO3riAwS4C3I,7995
cvxpy/tests/test_quadratic.py,sha256=rOE9hF660DXnh_Nn3UmLs1MYpbg30yQohRfYRbGCFOM,7300
cvxpy/tests/test_quantum_rel_entr.py,sha256=R_2GMXzej7PCmrkNJd9EL4LfjoadEFQb4PezyFCAqsE,6341
cvxpy/tests/test_scalarize.py,sha256=tRzrmYrvEv5x8y25IJPZYEKFPCsprQ4Je3Wcio2CJkc,6092
cvxpy/tests/test_semidefinite_vars.py,sha256=mBYxddENhrLhQvxfByPOkMRY1z3Rajhg0IkmPX8-lRE,3314
cvxpy/tests/test_shape.py,sha256=XB3s5Eng0DgvVYsfa6bMIUHW5VqV6nFYzuDmbQGSM4o,3329
cvxpy/tests/test_sign.py,sha256=F3Oj8F8ruzFdM4hk1xeA57VMYDK7HnmPVvnNrBzE_S8,2763
cvxpy/tests/test_suppfunc.py,sha256=7Cgbuz4ewesunBiBej9hQDs0Se5dTSX185AqYsOZM_M,8020
cvxpy/tests/test_valinvec2mixedint.py,sha256=ZucCCeq02QNro7bSF47Z4fh-Vcb3jh0hRXbbUSIElTw,14642
cvxpy/tests/test_versioning.py,sha256=MZ_fIhlio0M69-Jt__QJrQjdeNJvGX5HA7b_yleRCc0,1947
cvxpy/tests/test_von_neumann_entr.py,sha256=RAorpKp3JQQ-ig7KW2gIifJxcIy5R_I5ERdPmfed_HY,10510
cvxpy/transforms/__init__.py,sha256=NqHkteN9WWeNnXdcN7MvGn3K61CmlB9YhtweJk1mXiU,1019
cvxpy/transforms/__pycache__/__init__.cpython-311.pyc,,
cvxpy/transforms/__pycache__/indicator.cpython-311.pyc,,
cvxpy/transforms/__pycache__/linearize.cpython-311.pyc,,
cvxpy/transforms/__pycache__/partial_optimize.cpython-311.pyc,,
cvxpy/transforms/__pycache__/scalarize.cpython-311.pyc,,
cvxpy/transforms/__pycache__/suppfunc.cpython-311.pyc,,
cvxpy/transforms/indicator.py,sha256=zHSlKBQtDs2scRbPd5wHeEGFgVgm6hxkEcZ9LZ7KKug,3961
cvxpy/transforms/linearize.py,sha256=7AgJUdKDOO6D6rqglXkTXEO7U_tmqjobcA5a8ckMdaA,2172
cvxpy/transforms/partial_optimize.py,sha256=R2PRDLXW3RDMA8lmmAcFPthhzqgs2f2EhuafWDYs9wg,11229
cvxpy/transforms/scalarize.py,sha256=jtj1Ca09a-Z2wRlFGFsggkzj_x4RvdZDSo4gesYoHZQ,5202
cvxpy/transforms/suppfunc.py,sha256=eS-reP3PRcOGn5VSvpuj7m9FXRvhWS3D2iEroXFE4dU,6959
cvxpy/utilities/__init__.py,sha256=dXrGLPEHWC2vU2G0bTVwHQQNYlNKWEmCdV9S0bSZ8oU,647
cvxpy/utilities/__pycache__/__init__.cpython-311.pyc,,
cvxpy/utilities/__pycache__/canonical.cpython-311.pyc,,
cvxpy/utilities/__pycache__/citations.cpython-311.pyc,,
cvxpy/utilities/__pycache__/coeff_extractor.cpython-311.pyc,,
cvxpy/utilities/__pycache__/coo_array_compat.cpython-311.pyc,,
cvxpy/utilities/__pycache__/cvxpy_upgrade.cpython-311.pyc,,
cvxpy/utilities/__pycache__/debug_tools.cpython-311.pyc,,
cvxpy/utilities/__pycache__/deterministic.cpython-311.pyc,,
cvxpy/utilities/__pycache__/grad.cpython-311.pyc,,
cvxpy/utilities/__pycache__/key_utils.cpython-311.pyc,,
cvxpy/utilities/__pycache__/linalg.cpython-311.pyc,,
cvxpy/utilities/__pycache__/performance_utils.cpython-311.pyc,,
cvxpy/utilities/__pycache__/perspective_utils.cpython-311.pyc,,
cvxpy/utilities/__pycache__/power_tools.cpython-311.pyc,,
cvxpy/utilities/__pycache__/replace_quad_forms.cpython-311.pyc,,
cvxpy/utilities/__pycache__/scopes.cpython-311.pyc,,
cvxpy/utilities/__pycache__/shape.cpython-311.pyc,,
cvxpy/utilities/__pycache__/sign.cpython-311.pyc,,
cvxpy/utilities/__pycache__/versioning.cpython-311.pyc,,
cvxpy/utilities/canonical.py,sha256=lKxTB7zdJuBfSIG2U5aulAbJ3SGCo6O06CHmbBZP56M,7016
cvxpy/utilities/citations.py,sha256=k_r8bBshvIIP5KYA_WCmzvqkH2KL3NbOJz1rcV_EsTk,15232
cvxpy/utilities/coeff_extractor.py,sha256=R8QbzOwqYWhxbfExJrLR1wtLubwujBODVxhHyMQ58_8,13977
cvxpy/utilities/coo_array_compat.py,sha256=x5Dc0iH3DwLrkC6i5wb554N7xrU2PakcqgGGQsixb8s,795
cvxpy/utilities/cpp/__init__.py,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
cvxpy/utilities/cpp/__pycache__/__init__.cpython-311.pyc,,
cvxpy/utilities/cpp/sparsecholesky/__init__.py,sha256=W4yXibt5g9__pWWFRrwiLE-ugGaQftK_vXS2kmix_D8,37
cvxpy/utilities/cpp/sparsecholesky/__pycache__/__init__.cpython-311.pyc,,
cvxpy/utilities/cvxpy_upgrade.py,sha256=-Gf5-u5shP-aAvPDdYR_Jt0GeCHk_4bJZPrRIaC5Lvo,3031
cvxpy/utilities/debug_tools.py,sha256=8oiHccU6nspeFGI8IHElts_Mr7zTEFQWxZ95LmVkI-U,3343
cvxpy/utilities/deterministic.py,sha256=ECjpCmIWoPUVdrClUFm6-uQN-qJuB2WsxkTlKCUe1yk,253
cvxpy/utilities/grad.py,sha256=lbOOyFOthou_KviEP9CS8-czg7i13g_DCpvYrMejzIU,1477
cvxpy/utilities/key_utils.py,sha256=oP03mVGO7o02RZcKzuvA0JmA3bxV9AsLMcNL-tdpm78,7067
cvxpy/utilities/linalg.py,sha256=7K0c6mcMLqr_iiOm4WjSUC1CQpTtpDVBBPkym-rEmPY,9510
cvxpy/utilities/performance_utils.py,sha256=704uEqeuUsKcvgDDiuCE3WuUVCEmK5c2mMn9PjPxLrw,2513
cvxpy/utilities/perspective_utils.py,sha256=Q0htI4MVYdQpS0kFmwoWVICunJ2ZrYo5L5SRWQ1QnXI,2247
cvxpy/utilities/power_tools.py,sha256=cy3dwbIbs3W6ZOCw8gcR2pad2wTzXp1kBCx-WCMdF5s,20266
cvxpy/utilities/replace_quad_forms.py,sha256=T8L7nYhvgquvAXUof1OnFkhArotRH9XYzg-Q9Wie4e8,1596
cvxpy/utilities/scopes.py,sha256=KpyVHCkSXlfXVED2l2T6u4CVxt92BJzBx2VhQG-Nclw,1448
cvxpy/utilities/shape.py,sha256=0uR7QfRZ7Z30ujqPSq4VTGD4_pLZTbrK9VtAp964a-0,4641
cvxpy/utilities/sign.py,sha256=IsLWMy6Rdw9fVf1NDZFYPVZZ-b4lzGTa0jhqmtbOEXw,2032
cvxpy/utilities/versioning.py,sha256=QIDOs7Nz2g6kb0UZfw6BgC8y2dWpKrQwoWDBecQmd1I,1881
cvxpy/version.py,sha256=WiA0SFfFTI81dhHHKFlY0wAVMowzM9-j3K00Qz7jh4g,223
setup/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
setup/__pycache__/__init__.cpython-311.pyc,,
setup/__pycache__/build_meta.cpython-311.pyc,,
setup/__pycache__/extensions.cpython-311.pyc,,
setup/__pycache__/versioning.cpython-311.pyc,,
setup/build_meta.py,sha256=zAT1IPY5nS582xa6i9rmaUAscVo8P01pITc5vh71zJk,166
setup/extensions.py,sha256=YUCB7Fjgxp6XSeTf2IDzIoOhBCmAlQLyBI7JbMLiBTw,1871
setup/versioning.py,sha256=5-yqLsMZeN3KGjKGpAqgetqpnTs3D_vaJbO0xCJFYAM,4964
