2025-07-28 12:42:03 | ERROR    | qlib_trading_system.utils.system_init:install_dependencies:57 | 依赖包安装超时
2025-07-28 12:42:03 | ERROR    | qlib_trading_system.utils.system_init:full_initialization:245 | 步骤失败: 安装依赖包
2025-07-31 09:18:10 | ERROR    | __main__:test_alert_manager:163 | ❌ 报警管理器测试失败: name 'AlertEvent' is not defined
2025-07-31 09:18:17 | ERROR    | qlib_trading_system.monitoring.dashboard:get_current_metrics:172 | 获取当前指标失败: Object of type datetime is not JSON serializable
2025-07-31 09:18:17 | ERROR    | __main__:test_dashboard_api:192 | ❌ 获取当前指标失败: 500
2025-07-31 09:18:19 | ERROR    | __main__:test_mobile_api:263 | ❌ 用户登录失败: 401
2025-07-31 09:18:24 | ERROR    | __main__:run_comprehensive_test:460 | ❌ 3 个测试失败
2025-07-31 09:19:44 | ERROR    | qlib_trading_system.monitoring.dashboard:get_metrics_history:186 | 获取历史指标失败: Object of type datetime is not JSON serializable
2025-07-31 09:19:44 | ERROR    | __main__:test_dashboard_api:203 | ❌ 获取历史指标失败: 500
2025-07-31 09:19:46 | ERROR    | __main__:test_mobile_api:264 | ❌ 用户登录失败: 401
2025-07-31 09:19:51 | ERROR    | __main__:run_comprehensive_test:461 | ❌ 2 个测试失败
2025-07-31 10:50:30 | ERROR    | qlib_trading_system.utils.logging.log_manager:_initialize_components:42 | 日志管理系统初始化失败: 'Job' object has no attribute 'month'
2025-07-31 10:51:19 | ERROR    | qlib_trading_system.utils.logging.audit_logger:log_event:203 | {
  "event_id": "d812fbd8-e36c-4f9c-a321-2f10ccf2047e",
  "timestamp": "2025-07-31T02:51:19.241595+00:00",
  "event_type": "system_start",
  "level": "ERROR",
  "user_id": null,
  "session_id": null,
  "component": "COMPLIANCE",
  "action": "COMPLIANCE_VIOLATION",
  "details": {
    "violation_id": "AMOUNT_VIOLATION_20250731_105119",
    "rule_id": "AMOUNT_001",
    "rule_name": "单笔交易金额限制",
    "violation_level": "HIGH",
    "description": "交易金额超限: 交易比例60.00%，超过限制50.00%",
    "details": {
      "trade_amount": 60000,
      "total_assets": 100000,
      "trade_ratio": 0.6,
      "max_allowed_ratio": 0.5
    }
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | ERROR    | qlib_trading_system.utils.logging.compliance_checker:_handle_violation:445 | 高级别合规违规: 交易金额超限: 交易比例60.00%，超过限制50.00%
2025-08-01 11:53:16 | ERROR    | __main__:test_backup_manager:245 | ✗ 备份列表为空
2025-08-01 11:53:16 | ERROR    | __main__:run_all_tests:61 | ✗ 备份管理器测试 失败
2025-08-01 11:53:23 | ERROR    | __main__:run_all_tests:80 | ❌ 部分部署系统测试失败
2025-08-01 11:53:23 | ERROR    | __main__:main:504 | 部分部署系统测试失败，请检查日志
2025-08-01 11:53:58 | ERROR    | __main__:test_backup_manager:245 | ✗ 备份列表为空
2025-08-01 11:53:58 | ERROR    | __main__:run_all_tests:61 | ✗ 备份管理器测试 失败
2025-08-01 11:54:04 | ERROR    | __main__:run_all_tests:80 | ❌ 部分部署系统测试失败
2025-08-01 11:54:04 | ERROR    | __main__:main:504 | 部分部署系统测试失败，请检查日志
2025-08-01 11:54:56 | ERROR    | __main__:test_backup_manager:270 | ✗ 备份恢复失败: 备份信息文件缺失
2025-08-01 11:54:56 | ERROR    | __main__:run_all_tests:61 | ✗ 备份管理器测试 失败
2025-08-01 11:55:02 | ERROR    | __main__:run_all_tests:80 | ❌ 部分部署系统测试失败
2025-08-01 11:55:02 | ERROR    | __main__:main:513 | 部分部署系统测试失败，请检查日志
2025-08-01 11:55:23 | ERROR    | deployment.backup.backup:restore_backup:177 | 备份恢复失败: [WinError 267] 目录名称无效。: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpp41spvb1\\backups\\test_backup.tar.gz'
2025-08-01 11:55:23 | ERROR    | __main__:test_backup_manager:270 | ✗ 备份恢复失败: [WinError 267] 目录名称无效。: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpp41spvb1\\backups\\test_backup.tar.gz'
2025-08-01 11:55:23 | ERROR    | __main__:run_all_tests:61 | ✗ 备份管理器测试 失败
2025-08-01 11:55:29 | ERROR    | __main__:run_all_tests:80 | ❌ 部分部署系统测试失败
2025-08-01 11:55:29 | ERROR    | __main__:main:513 | 部分部署系统测试失败，请检查日志
2025-08-01 12:40:00 | ERROR    | qlib_trading_system.utils.database:test_connections:100 | ClickHouse连接测试失败: Code: 210. 由于目标计算机积极拒绝，无法连接。 (localhost:9000)
2025-08-01 12:40:04 | ERROR    | qlib_trading_system.utils.database:redis:64 | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:40:04 | ERROR    | qlib_trading_system.utils.database:test_connections:109 | Redis连接测试失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:45:44 | ERROR    | qlib_trading_system.utils.database:test_connections:100 | ClickHouse连接测试失败: Code: 210. 由于目标计算机积极拒绝，无法连接。 (localhost:9000)
2025-08-01 12:45:48 | ERROR    | qlib_trading_system.utils.database:redis:64 | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:45:48 | ERROR    | qlib_trading_system.utils.database:test_connections:109 | Redis连接测试失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:46:18 | ERROR    | qlib_trading_system.utils.database:mongodb:84 | MongoDB连接失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c467c4d065d32ce8bbe06, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 12:46:18 | ERROR    | qlib_trading_system.utils.database:test_connections:118 | MongoDB连接测试失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c467c4d065d32ce8bbe06, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 12:46:58 | ERROR    | qlib_trading_system.utils.database:test_connections:100 | ClickHouse连接测试失败: Code: 210. 由于目标计算机积极拒绝，无法连接。 (localhost:9000)
2025-08-01 12:47:02 | ERROR    | qlib_trading_system.utils.database:redis:64 | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:47:02 | ERROR    | qlib_trading_system.utils.database:test_connections:109 | Redis连接测试失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:47:32 | ERROR    | qlib_trading_system.utils.database:mongodb:84 | MongoDB连接失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c46c6e885ca63407f15cf, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 12:47:32 | ERROR    | qlib_trading_system.utils.database:test_connections:118 | MongoDB连接测试失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c46c6e885ca63407f15cf, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 12:48:10 | ERROR    | qlib_trading_system.utils.database:test_connections:100 | ClickHouse连接测试失败: Code: 210. 由于目标计算机积极拒绝，无法连接。 (localhost:9000)
2025-08-01 12:48:14 | ERROR    | qlib_trading_system.utils.database:redis:64 | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:48:14 | ERROR    | qlib_trading_system.utils.database:test_connections:109 | Redis连接测试失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 12:48:44 | ERROR    | qlib_trading_system.utils.database:mongodb:84 | MongoDB连接失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c470e39795842ba5ff4ba, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 12:48:44 | ERROR    | qlib_trading_system.utils.database:test_connections:118 | MongoDB连接测试失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c470e39795842ba5ff4ba, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 13:15:35 | ERROR    | qlib_trading_system.utils.database:test_connections:100 | ClickHouse连接测试失败: Code: 210. 由于目标计算机积极拒绝，无法连接。 (localhost:9000)
2025-08-01 13:15:39 | ERROR    | qlib_trading_system.utils.database:redis:64 | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 13:15:39 | ERROR    | qlib_trading_system.utils.database:test_connections:109 | Redis连接测试失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-08-01 13:16:09 | ERROR    | qlib_trading_system.utils.database:mongodb:84 | MongoDB连接失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c4d7bcc50d42ef5b94079, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 13:16:09 | ERROR    | qlib_trading_system.utils.database:test_connections:118 | MongoDB连接测试失败: localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 688c4d7bcc50d42ef5b94079, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] 由于目标计算机积极拒绝，无法连接。 (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-08-01 13:17:32 | ERROR    | qlib_trading_system.utils.qlib_init:initialize:55 | Qlib初始化失败: module 'qlib' has no attribute 'init'
2025-08-01 13:17:32 | ERROR    | __main__:main:37 | Qlib初始化失败
2025-08-01 13:17:32 | ERROR    | __main__:<module>:111 | 系统启动失败，请检查配置和日志
2025-08-01 13:21:42 | ERROR    | qlib_trading_system.utils.database:test_connections:100 | ClickHouse连接测试失败: Code: 210. 由于目标计算机积极拒绝，无法连接。 (***************:9011)
2025-08-01 13:52:14 | ERROR    | qlib_trading_system.utils.qlib_init:initialize:55 | Qlib初始化失败: module 'qlib' has no attribute 'init'
2025-08-01 13:52:14 | ERROR    | __main__:main:37 | Qlib初始化失败
2025-08-01 13:52:14 | ERROR    | __main__:<module>:111 | 系统启动失败，请检查配置和日志
2025-08-01 13:58:29 | ERROR    | qlib_trading_system.utils.qlib_init:initialize:55 | Qlib初始化失败: 'flask_port'
2025-08-01 13:58:29 | ERROR    | __main__:main:37 | Qlib初始化失败
2025-08-01 13:58:29 | ERROR    | __main__:<module>:111 | 系统启动失败，请检查配置和日志
2025-08-01 15:44:08 | ERROR    | __main__:download_real_stock_data:146 | 没有成功下载任何数据
2025-08-01 16:59:31 | ERROR    | __main__:download_specific_stocks:227 | 下载失败: Can't instantiate abstract class AkShareAdapter with abstract method get_minute_price
2025-08-01 16:59:31 | ERROR    | __main__:main:264 | ❌ 数据下载失败
2025-08-01 17:01:02 | ERROR    | __main__:download_specific_stocks:223 | 没有获取到数据
2025-08-01 17:01:02 | ERROR    | __main__:main:264 | ❌ 数据下载失败
2025-08-01 17:14:52 | ERROR    | __main__:test_akshare_download:46 | AkShare连接失败
2025-08-01 17:14:52 | ERROR    | __main__:main:121 | ❌ AkShare测试失败
2025-08-01 17:15:33 | ERROR    | __main__:test_akshare_basic:46 | 获取实时数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
